msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Localazy (https://localazy.com)\n"
"Project-Id-Version: Django\n"
"Language: en\n"
"Plural-Forms: nplurals=2; plural=(n==1) ? 0 : 1;\n"

msgid "+mail_transaction_new_product_shipped_text_5_3"
msgstr "P.S. For your safety, all Tylko furniture (especially tall pieces or those with drawers) should be fastened to the wall securely. The hardware and wall plugs you'll need will be found right in your package."

msgid "ANONCHK"
msgstr "Indicates whether MUID is transferred to ANID, a cookie used for advertising. Clarity doesn't use ANID and so this is always set to 0."

msgid "ATN"
msgstr "This cookie carries out information about how the end user navigates the website, and keeps track of any advertising that the end user may have seen before visiting our website."

#: frontend_cms/templates/front/base.html frontend_cms/templates/front/contest.html
msgid "Account"
msgstr "Account"

#: producers/templates/admin/stack_change_form.html
msgid "Add"
msgstr "Add"

#: producers/templates/admin/shipment_change_list.html
msgid "Add %(name)s"
msgstr "Add %(name)s"

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/homepage.html frontend_cms/templates/front/pdp.html frontend_cms/templates/front/pdp.html
msgid "Add to Cart"
msgstr "Add to Cart"

#: frontend_cms/templates/front/base.html frontend_cms/templates/front/contest.html
msgid "Added to cart"
msgstr "Added to cart."

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/confirmation.html frontend_cms/templates/front/order-details.html frontend_cms/templates/front/order-details.html frontend_cms/templates/front/order-details.html frontend_cms/templates/front/review-order.html frontend_cms/templates/front/review.html
msgid "Address"
msgstr "Address"

#: frontend_cms/templates/front/cart.html frontend_cms/templates/front/checkout.html
msgid "All prices include VAT and exclude <a href='/shipping/'>delivery costs</a>"
msgstr "All prices include VAT."

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/homepage.html frontend_cms/templates/front/pdp.html
msgid "All prices include VAT and exclude delivery costs"
msgstr "All prices include VAT."

msgid "Apply"
msgstr "Apply"

#: frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/order-details.html frontend_cms/templates/front/review-order.html frontend_cms/templates/front/review.html
msgid "Assembly (Additional Cost)"
msgstr "Assembly (Additional Cost)"

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html gallery/models.py
msgid "Austria"
msgstr "Austria"

#: frontend_cms/templates/front/change.html frontend_cms/templates/front/change_address.html
msgid "Back"
msgstr "Back"

#: frontend_cms/templates/front/order-details.html
msgid "Billing"
msgstr "Billing"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/checkout.html
msgid "Billing Address"
msgstr "Billing Address"

msgid "Blocked Card"
msgstr "It seems the card used for this order has been blocked. We weren’t able to process this transaction, so please contact your bank or try a different payment method."

msgid "CLID"
msgstr "Identifies the first-time Clarity saw this user on any site using Clarity."

msgid "CONSENT"
msgstr "These are third party cookies. They provide certain features from Google and may store certain preferences according to usage patterns, as well as personalise the ads that appear in Google searches."

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Cancel This Order"
msgstr "Cancel This Order"

msgid "Cancelled"
msgstr "Uh-oh, it seems this transaction didn’t go through due to your bank cancelling it. Please contact your bank or try a different payment method."

#: frontend_cms/templates/front/base.html frontend_cms/templates/front/base.html frontend_cms/templates/front/cart.html frontend_cms/templates/front/contest.html
msgid "Cart"
msgstr "Cart"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/change_address.html
msgid "Change Address"
msgstr "Change address"

#: frontend_cms/templates/front/account.html
msgid "Change Password"
msgstr "Change password"

#: frontend_cms/templates/front/change.html
msgid "Change password"
msgstr "Change password"

#: frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_checkout_item.html frontend_cms/templates/tag_checkout_item.html frontend_cms/templates/tag_checkout_item.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_order_placed.html mailing/templates/mails/mail_order_placed.html mailing/templates/mails/mail_order_placed.html
msgid "Color"
msgstr "Colour"

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html
msgid "Company Name"
msgstr "Company Name"

#: frontend_cms/templates/front/base.html
msgid "Contact"
msgstr "Contact"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Context Card"
msgstr "Context Card"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/cart.html frontend_cms/templates/front/cart.html frontend_cms/templates/front/confirmation.html
msgid "Continue Shopping"
msgstr "Continue Shopping"

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html
msgid "Country"
msgstr "Country"

#: frontend_cms/templates/front/_login-register-popup.html frontend_cms/templates/front/register.html frontend_cms/templates/front/register2.html
msgid "Create a tylko account to keep all of your designs and orders in one place, and easily switch between desktop and app."
msgstr "Create a Tylko account to keep all of your designs and orders in one place and easily switch between your desktop and mobile."

#: frontend_cms/templates/front/_login-register-popup.html frontend_cms/templates/front/_login-register-popup.html frontend_cms/templates/front/confirmation.html frontend_cms/templates/front/invite.html frontend_cms/templates/front/register.html frontend_cms/templates/front/register.html frontend_cms/templates/front/register2.html
msgid "Create an Account"
msgstr "Create an account"

#: frontend_cms/templates/front/order-details.html
msgid "Current Order"
msgstr "Current Order"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Current Orders"
msgstr "Current Orders"

#: mailing/templates.py
msgid "Daily report"
msgstr "Daily report"

#: frontend_cms/templates/front/order-details.html
msgid "Delivered"
msgstr "Delivered"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/review-order.html frontend_cms/templates/front/review.html invoice/models.py mailing/templates/mails/mail_order_placed.html
msgid "Delivery"
msgstr "Delivery"

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/homepage.html frontend_cms/templates/front/pdp.html frontend_cms/templates/front/products.html frontend_cms/templates/front/products.html
msgid "Description"
msgstr "Description"

#: frontend_cms/templates/front/order-details.html frontend_cms/templates/front/order-details.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_checkout_item.html frontend_cms/templates/tag_checkout_item.html frontend_cms/templates/tag_checkout_item.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_order_placed.html
msgid "Dimensions"
msgstr "Dimensions"

#: invoice/models.py
msgid "Disabled"
msgstr "Disabled"

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/homepage.html frontend_cms/templates/front/pdp.html
msgid "Easy assembly"
msgstr "Easy assembly"

#: frontend_cms/templates/front/_library-nav.html
msgid "Edit"
msgstr "Edit"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/forgotten_password_init.html
msgid "Email"
msgstr "Email"

#: frontend_cms/templates/front/review.html
msgid "Enter Your Promo Code"
msgstr "Enter Your Promocode"

msgid "Expired Card"
msgstr "It seems the card used for this order has expired. We aren’t able to process this transaction, so please contact your bank or try a different payment method."

#: producers/templates/admin/shipment_change_list.html
msgid "Filter"
msgstr "Filter"

msgid "Follow us:"
msgstr "Follow us:"

#: frontend_cms/templates/front/change.html frontend_cms/templates/front/login.html frontend_cms/templates/front/register2.html
msgid "Forgot password?"
msgstr "Forgot Password?"

#: frontend_cms/templates/front/forgotten_password_init.html
msgid "Forgot your password? No worries. Enter your email in the box and will send you a link to reset your password."
msgstr "Forgot your password? Don't fret, it happens. Enter your email and we'll send you a link to reset it."

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html gallery/models.py
msgid "Germany"
msgstr "Germany"

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html
msgid "Germany / Austria / Poland / UK"
msgstr "Select Country"

#: frontend_cms/templates/front/order-details.html
msgid "Go Back to Your Account"
msgstr "Go Back to Your Account"

#: frontend_cms/templates/front/404.html
msgid "Go back to home page."
msgstr "Go back to home page."

#: frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_checkout_item.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_order_placed.html
msgid "Gradient"
msgstr "Gradient"

msgid "Grid"
msgstr "Grid"

#: custom/templates/admin/pc_change_form.html custom/templates/admin/pc_object_history.html producers/templates/admin/stack_change_form.html
msgid "History"
msgstr "History"

#: frontend_cms/templates/front/404.html
msgid "Homepage"
msgstr "Homepage"

msgid "IDE"
msgstr "This cookie has been set up by Double Click (Google), to help us analyse and optimise our advertising campaigns."

msgid "IVY_story_3sizes_part1"
msgstr "for that precious photo of your grandmother"

msgid "IVY_story_3sizes_part2"
msgstr "for every book and magazine you love"

msgid "IVY_story_3sizes_part3"
msgstr "for the vinyl records you can't live without"

msgid "IVY_story_header_1_1"
msgstr "Your dearest things <span class=\"mobile-visible\"><br/></span> all have a story."

msgid "IVY_story_header_1_2"
msgstr "Meet the Tylko shelf."

msgid "IVY_story_header_2_1"
msgstr "It’s the little things"

msgid "IVY_story_header_2_2"
msgstr "Three ideal sizes"

msgid "IVY_story_header_2_3"
msgstr "Every centimeter counts"

msgid "IVY_story_header_2_4"
msgstr "Every detail counts"

msgid "IVY_story_header_2_5"
msgstr "Responsive design"

msgid "IVY_story_header_2_6"
msgstr "Test-drive your shelf"

msgid "IVY_story_header_2_7"
msgstr "Start your own shelf story"

msgid "IVY_story_header_2_8"
msgstr "Our delivery guarantee"

msgid "IVY_story_paragraph_2_1"
msgstr "We began with common household items and built the shelves around them- using the golden rules of design for the perfect proportions."

msgid "IVY_story_paragraph_3_1"
msgstr "We know every room is different, and only you know exactly how to fill your space. With Tylko you can define the exact dimensions you want - down to the last centimeter."

msgid "IVY_story_paragraph_4_1"
msgstr "We want to provide you with the best product we can. We've meticulously examined every detail - from a custom instruction manual and color-coded connectors to every hand-beveled edge or adjustable foot. We really have thought of everything."

msgid "IVY_story_paragraph_5_1"
msgstr "We asked ourselves how we could design perfectly-proportioned shelves (no matter how much you stretch them) and spent two years honing the rules that guarantee your shelves will be spot on - even as you add or delete rows and columns to tweak your design."

msgid "IVY_story_paragraph_6_1"
msgstr "What if you could see your shelf in place before you buy? With our augmented reality app you can customize your shelf and preview it live in your space before you order."

msgid "IVY_story_paragraph_7_buton_1"
msgstr "Shop the Shelf"

msgid "IVY_story_paragraph_8_1"
msgstr "We’ve perfected our packaging so we can guarantee safe delivery to your door anywhere in Europe. Your shelf is delivered in manageable, numbered flat-pack boxes - making assembly a breeze."

#: frontend_cms/templates/front/_login-register-popup.html frontend_cms/templates/front/invite.html frontend_cms/templates/front/register.html
msgid "I Accept"
msgstr "I Accept"

msgid "Invalid Amount"
msgstr "Oops, there seems to be an inconsistency with the bank regarding the amount of this transaction, so it hasn’t gone through. Please contact your bank or try a different payment method."

msgid "Invalid Card Number"
msgstr "It seems as if the card number entered is incorrect or invalid. We weren’t able to process this transaction, so please contact your bank or try a different payment method."

msgid "Invalid Pin"
msgstr "It seems the PIN number entered is incorrect or invalid. We weren’t able to process this transaction, so please contact your bank or try a different payment method."

msgid "Issuer Unavailable"
msgstr "Uh-oh, it seems the bank is unable to authorize this payment. It’s not possible to complete this transaction, so please contact your bank or try a different payment method."

#: frontend_cms/templates/front/account.html invoice/models.py
msgid "Item"
msgstr "Item"

#: gallery/models.py gallery/models.py
msgid "Ivy Shelf"
msgstr "Tylko Shelf"

msgid "Ivy_story_header_chapter"
msgstr "Chapter 1: The Shelf"

#: frontend_cms/templates/front/base.html
msgid "Journal"
msgstr "Journal"

#: mailing/templates/mails/mail_kpi_report.html
msgid "KPI report"
msgstr "KPI report"

msgid "LP_matte_black_view_in_ar_button"
msgstr "View it in AR "

msgid "LP_popup_body"
msgstr "Each box includes four colours of samples from our Tylko Original line. By ordering a set, you'll get a chance to compare colours and get a proper feel of the premium materials and finishes we use."

msgid "LP_sample.button_whats_in_the box"
msgstr "What’s in the box"

msgid "LP_smaple_body_main"
msgstr "Get a proper look at our colours and feel the quality of our materials from the comfort of your home.  Discover the Tylko Original Classic palette, the bold Tylko Original Modern hues, and get acquainted with the premium details that makes the Tone wardrobe a timeless piece.  "

msgid "LP_smaple_body_t01_t02"
msgstr "Make deciding easy by getting properly acquainted with our colour and material selection for our shelves. Select the palette you love and explore what feels right for you and your home."

msgid "LP_smaple_body_t03"
msgstr "The Tone Wardrobe was intentionally designed to last for years to come. Order a sample set to get firsthand experience of the quality of our materials, and to see the softness of the colours up close. "

msgid "LP_smaple_headline_main"
msgstr "Explore our sample kits"

msgid "LP_smaple_headline_t01_t02"
msgstr "Sample sets for Tylko Original  Shelves"

msgid "LP_smaple_headline_t03"
msgstr "Sample sets for the Tone<br>Wardrobe"

#: frontend_cms/templates/front/account.html
msgid "Log Out"
msgstr "Log Out"

#: frontend_cms/templates/front/_login-register-popup.html frontend_cms/templates/front/_login-register-popup.html frontend_cms/templates/front/invite.html frontend_cms/templates/front/login.html frontend_cms/templates/front/login.html frontend_cms/templates/front/register2.html frontend_cms/templates/front/register2.html
msgid "Log in"
msgstr "Login"

#: frontend_cms/views.py user_profile/views.py
msgid "Login successful."
msgstr "Login successful."

#: frontend_cms/views.py
msgid "Login successful. Now share the love by clicking Send."
msgstr "Login successful. Now share the love by clicking Send."

#: frontend_cms/views.py frontend_cms/views.py
msgid "Login unsuccessful."
msgstr "Login unsuccessful."

msgid "MR"
msgstr "Indicates whether to refresh MUID."

msgid "MUID"
msgstr "This cookie is widely used my Microsoft as a unique user identifier. It can be set by embedded microsoft scripts. Widely believed to sync across many different Microsoft domains, allowing user tracking."

msgid "MUIDB"
msgstr "This cookie is widely used my Microsoft as a unique user identifier. It can be set by embedded microsoft scripts. Widely believed to sync across many different Microsoft domains, allowing user tracking."

msgid "Mail_transaction_new_product_tobeshipped_text_2_3"
msgstr "Size"

msgid "Mail_transaction_new_product_tobeshipped_text_2_4"
msgstr "Weight"

#: frontend_cms/templates/front/order-details.html frontend_cms/templates/front/order-details.html frontend_cms/templates/front/pdp.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_checkout_item.html frontend_cms/templates/tag_checkout_item.html frontend_cms/templates/tag_checkout_item.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_order_placed.html mailing/templates/mails/mail_order_placed.html mailing/templates/mails/mail_order_placed.html
msgid "Material"
msgstr "Material & Color"

#: frontend_cms/templates/front/_library-hero.html frontend_cms/templates/front/base.html frontend_cms/templates/front/contest.html
msgid "My Library"
msgstr "Wishlist"

msgid "NID"
msgstr "Send anonymous statistics for advertising use to Google."

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html frontend_cms/templates/tag_cart_table.html
msgid "Name"
msgstr "Name"

#: frontend_cms/templates/front/register2.html
msgid "New to"
msgstr "New to"

msgid "No additional vouchers can be applied to this order. Please remove the current voucher to apply a new one."
msgstr "No additional vouchers can be applied to this order.\nPlease remove the current voucher to apply a new one."

msgid "Not Submitted"
msgstr "Oops - it seems the bank found issue with the payment being submitted. We weren’t able to complete this transaction, so please contact your bank or try a different payment method."

msgid "Not enough balance"
msgstr "It seems the card entered does not have an adequate balance to cover the amount. We weren’t able to process this transaction, so please contact your bank or try a different payment method."

#: producers/templatetags/producers_extra.py
msgid "Not yet"
msgstr "Not yet"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Open Manual"
msgstr "Open Manual"

#: frontend_cms/views.py
msgid "Order Canceled"
msgstr "Order Canceled"

#: frontend_cms/templates/front/order-details.html
msgid "Order Details"
msgstr "Order Details"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Order History"
msgstr "Order History"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Order Number"
msgstr "Order Number"

#: frontend_cms/templates/front/forgotten_password_confirmation.html frontend_cms/templates/front/forgotten_password_init.html
msgid "Password Assistance"
msgstr "Password assistance"

#: frontend_cms/views.py
msgid "Password change failed."
msgstr "Password change failed."

msgid "Pattern"
msgstr "Pattern"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Pay"
msgstr "Pay"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Payment"
msgstr "Payment"

msgid "Pending"
msgstr "Hm, it seems the payment seems to be in limbo between processing stages. We weren’t able to process this transaction, so please contact your bank or try a different payment method."

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/contact.html
msgid "Phone Number"
msgstr "Phone Number"

msgid "Play"
msgstr "Play"

#: producers/templates/admin/shipment_change_list.html producers/templates/admin/stack_change_form.html
msgid "Please correct the error below."
msgstr "Please correct the error below."

#: producers/templates/admin/shipment_change_list.html producers/templates/admin/stack_change_form.html
msgid "Please correct the errors below."
msgstr "Please correct the errors below."

#: frontend_cms/templates/front/base.html frontend_cms/templates/front/contest.html
msgid "Please enter a valid promo code."
msgstr "Please enter a valid Promocode."

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/contest.html frontend_cms/templates/front/contest.html frontend_cms/templates/front/contest.html frontend_cms/templates/front/homepage.html frontend_cms/templates/front/homepage.html frontend_cms/templates/front/order-details.html frontend_cms/templates/front/pdp.html frontend_cms/templates/front/pdp.html frontend_cms/templates/front/pdp.html frontend_cms/templates/front/pdp.html frontend_cms/templates/front/products.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_checkout_item.html frontend_cms/templates/tag_checkout_item.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_contest_entry_de.html mailing/templates/mails/mail_order_placed.html mailing/templates/mails/mail_order_placed.html
msgid "Plywood"
msgstr "Plywood"

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html gallery/models.py mailing/templates/mails/base.html
msgid "Poland"
msgstr "Poland"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/cart.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/contest.html frontend_cms/templates/front/homepage.html frontend_cms/templates/front/order-details.html frontend_cms/templates/front/pdp.html frontend_cms/templates/tag_cart_table.html
msgid "Price"
msgstr "Price"

#: frontend_cms/templates/front/base.html frontend_cms/templates/front/faq.html frontend_cms/templates/front/faq.html frontend_cms/templates/front/homepage.html frontend_cms/templates/front/order-details.html
msgid "Products"
msgstr "Products"

#: frontend_cms/templates/front/order-details.html frontend_cms/templates/front/review-order.html frontend_cms/templates/front/review.html
msgid "Promo Code"
msgstr "Promo Code"

#: frontend_cms/templates/front/base.html frontend_cms/templates/front/contest.html
msgid "Promo Code accepted."
msgstr "Promo Code accepted."

#: frontend_cms/templates/front/review.html
msgid "Promo code"
msgstr "Promo Code"

msgid "Promotion %(code)s is not active"
msgstr "Promotion %(code)s is not active"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html frontend_cms/templates/tag_cart_table.html frontend_cms/templates/tag_checkout_item.html invoice/templates/invoice/default.html mailing/templates/mails/mail_order_placed.html
msgid "Quantity"
msgstr "Quantity"

#: frontend_cms/templates/front/_customer-review.html frontend_cms/templates/front/journal.html
msgid "Read more"
msgstr "Read more"

msgid "Refused"
msgstr "Oops - this payment was refused. We weren’t able to process this transaction, so please contact your bank or try a different payment method."

#: frontend_cms/views.py
msgid "Registration complete."
msgstr "Registration complete."

#: frontend_cms/views.py
msgid "Registration successful. Now share the love by clicking Send."
msgstr "Registration successful. Now share the love by clicking Send."

#: frontend_cms/templates/front/base.html frontend_cms/templates/front/contest.html
msgid "Removed from cart"
msgstr "Removed from cart."

msgid "Removed voucher from cart"
msgstr "Removed voucher from cart"

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/pdp.html
msgid "Row Height"
msgstr "Row Height"

msgid "SEO_CATEGORY_Bedside_Table_Meta"
msgstr "Design a White, Black, Grey, or Oak Bedside Table for your Bedroom. Choose the ideal width, depth and height to create slim storage that maximises on space. Order a set of two for a mirrored set up. Includes free delivery and returns."

msgid "SEO_CATEGORY_Bedside_Table_Title"
msgstr "Bespoke, customisable Bedside Tables - Tylko"

msgid "SEO_CATEGORY_Bookcases_Meta"
msgstr "White, black, oak or other colors of bookshelves. Customized bookshelves for your living room or bedroom or children. Free delivery and free 100-day returns."

msgid "SEO_CATEGORY_Bookcases_Title"
msgstr "Designer furniture - Buy small or large bookcase  in the online shop - Tylko"

msgid "SEO_CATEGORY_Chest_Meta"
msgstr "White, black, grey, oak veneer and other colors chests of drawers for your living room or bedroom. Bespoke chests of drawers at a fair price. Free delivery and free 100-day returns."

msgid "SEO_CATEGORY_Chest_Title"
msgstr "Buy custom chest of drawers and dressers online - Tylko"

msgid "SEO_CATEGORY_Desk_Meta"
msgstr "Design your custom home office desk with expert storage, cable management and drawers. Adjust the size and height to meet your needs. Free delivery and returns. "

msgid "SEO_CATEGORY_Desk_Title"
msgstr "Desks & Computer Desks - Made-to-measure, storage-focused - Tylko"

msgid "SEO_CATEGORY_Shoe_Racks_Meta"
msgstr "Bench or rack, tall or low, white, black or wooden veneer. Bespoke designer shoe racks at fair prices. Free delivery and free 100-day returns."

msgid "SEO_CATEGORY_Shoe_Racks_Title"
msgstr "Custom shoe storage - shoe racks, benches and cabinets - Tylko"

msgid "SEO_CATEGORY_Sideboards_Meta"
msgstr "White, black, grey, oak and other colors and finish of sideboards for your bedroom or living room. Shop furniture online with permanent discount system. Free delivery and free 100-day returns."

msgid "SEO_CATEGORY_Sideboards_Title"
msgstr "Designer sideboards and storage cabinets - Tylko"

msgid "SEO_CATEGORY_TV_Stands_Meta"
msgstr "White, black, grey and other colors contemporary TV stands for your living room or bedroom. Bespoke TV media units. Check sale prices. Free delivery and free 100-day returns."

msgid "SEO_CATEGORY_TV_Stands_Title"
msgstr "Modern TV stands with storage and cable management - Tylko"

msgid "SEO_CATEGORY_Vinyl_Meta"
msgstr "Sturdy vinyl storage cabinets for your LP collection. Buy record vinyl cabinets at a fair price in the online shop. Free delivery and free 100-day returns."

msgid "SEO_CATEGORY_Vinyl_Title"
msgstr "Bespoke Vinyl Record Storage - Designer Furniture - Tylko"

msgid "SEO_CATEGORY_Wall_Storage_Meta"
msgstr "Bespoke wall cabinets and shelf units with doors and shelves at a fair price. Wall storage ideas for your bedroom or living room. Free delivery and free 100-day returns."

msgid "SEO_CATEGORY_Wall_Storage_Title"
msgstr "Custom wall storage units and wall storage solutions - Tylko"

msgid "SEO_CATEGORY_Wardrobe_Meta"
msgstr "Custom wardrobes with doors and drawers for your bedroom, living room or hallway. Design storage online. Fair price, free 100-day returns."

msgid "SEO_CATEGORY_Wardrobe_Title"
msgstr "Made to measure wardrobes - free delivery and assembly - Tylko"

msgid "SM"
msgstr "Used in synchronizing the MUID across Microsoft domains."

msgid "Sample Materials"
msgstr "Material Samples"

msgid "Sample Set"
msgstr "Samples of wood"

#: frontend_cms/templates/front/change.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/contest.html frontend_cms/templates/front/library-edit.html frontend_cms/templates/front/pdp.html
msgid "Save"
msgstr "Save"

#: frontend_cms/templates/front/base.html frontend_cms/templates/front/contest.html
msgid "Saving failed. Please try again."
msgstr "Saving failed. Please try again."

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html
msgid "Select"
msgstr "Select"

#: frontend_cms/templates/front/forgotten_password_init.html
msgid "Send"
msgstr "Send"

#: frontend_cms/templates/front/_steps.html frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html frontend_cms/templates/tag_cart_table.html
msgid "Shipping"
msgstr "Shipping"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/review-order.html frontend_cms/templates/front/review.html mailing/templates/mails/mail_order_placed.html
msgid "Shipping Address"
msgstr "Shipping Address"

msgid "Short lived cookies used to temporarily store data for the visit"
msgstr "Short lived cookies used to temporarily store data for the visit"

#: frontend_cms/templates/front/base.html mailing/templates/mails/mail_account_created.html
msgid "Sign up"
msgstr "Sign in"

msgid "Slant"
msgstr "Slant"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Sorry, we had a problem processing your payment. Please go to checkout to try a different payment option or"
msgstr "Sorry, we had a problem processing your payment. Please go to checkout to try a different payment option or"

#: frontend_cms/templates/front/order-details.html mailing/templates/mails/mail_order_placed.html
msgid "Status:"
msgstr "Status:"

#: frontend_cms/templates/front/_subscribe.html frontend_cms/templates/front/business.html frontend_cms/templates/front/partners.html
msgid "Submit"
msgstr "Submit"

#: frontend_cms/templates/front/order-details.html frontend_cms/templates/front/review-order.html frontend_cms/templates/front/review.html mailing/templates/mails/mail_order_placed.html
msgid "Subtotal"
msgstr "Subtotal"

#: frontend_cms/templates/front/_login-register-popup.html frontend_cms/templates/front/_pilot-popup.html frontend_cms/templates/front/_subscribe.html frontend_cms/templates/front/base.html frontend_cms/templates/front/invite.html frontend_cms/templates/front/register.html frontend_cms/templates/front/review-order.html frontend_cms/templates/front/review.html frontend_cms/templates/front/review.html frontend_cms/templates/front/terms.html
msgid "Terms of Service"
msgstr "Terms of Service"

#: frontend_cms/forms.py frontend_cms/forms.py
msgid "The email you entered is invalid."
msgstr "The email you entered is invalid."

#: frontend_cms/views.py
msgid "There is no account linked to this email. Please enter a different one."
msgstr "There is no account linked to this email. Please enter a different one."

msgid "There is no voucher with %(code)s code"
msgstr "There is no voucher with %(code)s code"

#: frontend_cms/forms.py
msgid "This email is already linked to a tylko account. Please enter a different one."
msgstr "This email is already linked to a Tylko account. Please enter a different one."

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/homepage.html frontend_cms/templates/front/pdp.html
msgid "This product requires assembly."
msgstr "This product requires assembly."

msgid "Type01"
msgstr "Type01"

msgid "Type02"
msgstr "Type02"

msgid "Type03"
msgstr "Type03"

msgid "Type13"
msgstr "Type13"

msgid "Used to store a few details about the user such as the unique visitor ID."
msgstr "Used to store a few details about the user such as the unique visitor ID."

msgid "Used to store the attribution information, the referrer initially used to visit the website"
msgstr "Used to store the attribution information, the referrer initially used to visit the website"

#: frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/change_address.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html frontend_cms/templates/front/checkout.html
msgid "VAT identification number (Optional)"
msgstr "VAT identification number"

msgid "VISITOR_INFO1_LIVE"
msgstr "This cookie allows Youtube to check for bandwidth usage."

#: frontend_cms/templates/front/order-details.html
msgid "View in App"
msgstr "View in App"

#: custom/templates/admin/pc_change_form.html producers/templates/admin/stack_change_form.html
msgid "View on site"
msgstr "View on site"

#: mailing/templates/mails/base.html
msgid "Warsaw"
msgstr "Warsaw"

msgid "Weight"
msgstr "Weight"

msgid "YSC"
msgstr "This cookie registers a unique ID to keep statistics on which YouTube videos have been watched by the user."

#: frontend_cms/templates/front/order-details.html
msgid "Your Account"
msgstr "Your Account"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "Your Info"
msgstr "Your Info"

#: frontend_cms/views.py
msgid "Your password has been changed."
msgstr "Your password has been changed."

#: frontend_cms/templates/front/base.html frontend_cms/templates/front/contest.html
msgid "Your piece has been successfully saved."
msgstr "Your piece has been added to Wishlist."

msgid "^category/new-wardrobes/$"
msgstr "^category/new-wardrobes/$"

msgid "^front-rooms$"
msgstr "rooms/"

msgid "^front-tylko-for-business$"
msgstr "tylko-for-business/"

msgid "^front-tylko-for-business-refer$"
msgstr "tylko-for-business-refer/"

msgid "^furniture/(?P<furniture_category>[a-z\\-_]+)/(?P<pk>[0-9]+),j,/"
msgstr "^furniture/(?P<furniture_category>[a-z\\-_]+)/(?P<pk>[0-9]+),j,/"

msgid "^furniture/(?P<furniture_category>[a-z\\-_]+)/(?P<pk>[0-9]+),w,/"
msgstr "^furniture/(?P<furniture_category>[a-z\\-_]+)/(?P<pk>[0-9]+),w,/"

msgid "^furniture/(?P<pk>[0-9]+),j,/"
msgstr "^furniture/(?P<pk>[0-9]+),j,/"

msgid "^furniture/(?P<pk>[0-9]+),w,/"
msgstr "^furniture/(?P<pk>[0-9]+),w,/"

msgid "^material-samples/"
msgstr "^material-samples/"

msgid "^our-mission/"
msgstr "^our-mission/"

msgid "^product-lines/"
msgstr "^product-lines/"

msgid "^product-lines/storage"
msgstr "^product-lines/storage/"

msgid "^product-lines/type02-shelf/matte-black$"
msgstr "^product-lines/tylko-original-modern-shelf/matte-black$"

msgid "^products/$"
msgstr "^products/$"

msgid "^shelf/"
msgstr "^shelf/"

msgid "^shelf/(?P<furniture_category>.+)/$"
msgstr "^shelf/(?P<furniture_category>.+)/$"

msgid "^shelf/(?P<pk>[0-9]+)/v-(?P<version>[0-9]+)/"
msgstr "^shelf/(?P<pk>[0-9]+)/v-(?P<version>[0-9]+)/"

msgid "^shelves/(?P<furniture_category>.+)/$"
msgstr "^shelves/(?P<furniture_category>.+)/$"

msgid "^shelves/type02-cottonbeige-burgundyred-skyblue/$"
msgstr "^shelves/tylko-original-modern-cottonbeige-burgundyred-skyblue/$"

msgid "^sofa/(?P<furniture_category>[a-z\\-_]+)/(?P<pk>[0-9]+),s,/"
msgstr "^sofa/(?P<furniture_category>[a-z\\-_]+)/(?P<pk>[0-9]+),s,/"

msgid "__cf_bm"
msgstr "The __cf_bm cookie is necessary to support Cloudflare Bot Management, currently in private beta. As part of our bot management service, this cookie helps manage incoming traffic that matches criteria associated with bots."

msgid "_amp_*"
msgstr "Cookie to store a unique user ID."

msgid "_clck"
msgstr "Persists the Clarity User ID and preferences, unique to that site is attributed to the same user ID."

msgid "_clsk"
msgstr " \n \t\nConnects multiple page views by a user into a single Clarity session recording."

msgid "_fbp"
msgstr "This cookie is used by Facebook for advertising purposes and conversion tracking."

msgid "_ga"
msgstr "This cookie registers a unique ID for website visitors, and it tracks how they use the website. The data is used for statistics."

msgid "_gat_*****"
msgstr "This cookie is set up by Google Analytics to control the request rate."

msgid "_gcl_au"
msgstr "This cookie is set by Google Tag Manager to take information in advert clicks and store it in a 1st party cookie so that conversions can be attributed outside of the landing page."

msgid "_gid"
msgstr "This cookie registers a unique ID for website visitors, and it tracks how they use the website. The data is used for statistics."

msgid "_hjAbsoluteSessionInProgress"
msgstr "This cookie has been set up by Hotjar for performance and analytics purposes."

msgid "_hjFirstSeen"
msgstr "This cookie has been set up by Hotjar for performance and analytics purposes."

msgid "_hjIncludedInPageviewSample"
msgstr "This cookie has been set up by Hotjar for performance and analytics purposes."

msgid "_hjIncludedInSessionSample"
msgstr "This cookie has been set up in relation to Hotjar tracking."

msgid "_hjid"
msgstr "This is a Hotjar cookie and it is set when the customer first lands on a page with the Hotjar script. It is used to persist the random user ID, unique to that site on the browser. This cookie ensures that behavior in subsequent visits to the same site will be attributed to the same user ID."

msgid "_mkto_trk"
msgstr "This cookie has been set up by Amplitude for performance and product analytics purposes."

msgid "_pin_unauth"
msgstr "Used by Pinterest to track the usage of services."

msgid "_pinterest_ct_ua"
msgstr "Cookie is a third party cookie which groups actions for users who cannot be identified by Pinterest."

msgid "_pinterest_sess"
msgstr "The Pinterest login cookie. It contains user ID(s), authentication token(s) and timestamps. This cookie is used to deliver better advertising content."

msgid "_uetsid"
msgstr "This cookie is used by Bing to gather anonymous information on how visitors are using our website."

msgid "_uetvid"
msgstr "This is a cookie utilised by Microsoft Bing Ads and is a tracking cookie. It allows us to engage with a user that has previously visited our website."

msgid "account_created_at"
msgstr "Created at: "

msgid "account_dimensions"
msgstr "Dimensions: "

msgid "account_download_invoice"
msgstr "Download invoice"

msgid "account_download_manual"
msgstr "Download manual"

msgid "account_myaccount_no_orders"
msgstr "You don't have any orders yet. Ready to start browsing?"

msgid "account_password_CTA_2"
msgstr "Got it"

#, python-format
msgid "account_password_body_2_%(email)s"
msgstr "We've sent a link so you can change your password to %(email)s."

msgid "account_pay_for_order"
msgstr "Pay for order"

msgid "addreview_page_upload_problem"
msgstr "Something went wrong. Please try again."

msgid "ajs%3Acookies"
msgstr "This cookie is used to assign specific visitors into segments. This segmentation is based on visitor behavior on the website and can be used to target larger groups."

msgid "ajs%3Atest"
msgstr "This cookie is used to assign specific visitors into segments. This segmentation is based on visitor behavior on the website and can be used to target larger groups."

msgid "alt_basic_pdp_assembly_image_1"
msgstr "labelled packages and personalized manual"

msgid "alt_basic_pdp_assembly_image_2"
msgstr "premounted and color coded conectors"

msgid "alt_basic_pdp_assembly_video_1"
msgstr "easy assembly"

msgid "alt_basic_pdp_dna_1"
msgstr "shelf with staggered slant compartment pattern"

msgid "alt_basic_pdp_dna_2"
msgstr "shelf with irregular pattern compartments"

msgid "alt_basic_pdp_dna_3"
msgstr "shelf with symmetrical grid compartment pattern"

msgid "alt_basic_pdp_dna_4"
msgstr "shelf with gradient pattern compartments"

msgid "alt_basic_pdp_doors_1"
msgstr "shelf without doors"

msgid "alt_basic_pdp_doors_2"
msgstr "shelf with some doors"

msgid "alt_basic_pdp_doors_3"
msgstr "shelf with doors"

msgid "alt_basic_pdp_drawers_1"
msgstr "shelf without drawers"

msgid "alt_basic_pdp_drawers_2"
msgstr "shelf with some drawers"

msgid "alt_basic_pdp_drawers_3"
msgstr "shelf with drawers"

msgid "alt_basic_pdp_row_depth_1"
msgstr "32cm deep shelves"

msgid "alt_basic_pdp_row_depth_2"
msgstr "40cm deep shelves"

msgid "alt_basic_pdp_row_height_1"
msgstr "low shelf row - 18cm"

msgid "alt_basic_pdp_row_height_2"
msgstr "low shelf row - 28cm"

msgid "alt_basic_pdp_row_height_3"
msgstr "low shelf row - 38cm"

msgid "alt_basic_pdp_specs_image_1"
msgstr "18mm non-toxic particle board + Durable edge wrap band"

msgid "alt_basic_pdp_specs_image_2"
msgstr "tylko.com easy assembly system - shelves"

msgid "alt_basic_pdp_specs_image_3"
msgstr "tylko.com easy assembly system - doors"

msgid "alt_basic_pdp_specs_image_4"
msgstr "tylko.com easy assembly system - drawers"

msgid "alt_basic_pdp_specs_image_5"
msgstr "tylko.com easy assembly system - shelf feet and leveling"

msgid "alt_business_hero"
msgstr "Custom furniture for businesses - Tylko bespoke furniture offer"

msgid "anapopescu_terms_description_1"
msgstr "<p class=\"tp-small-m tp-default-t tpb-s\">1. The promoter is: Custom Sp. z o.o. whose registered office is located in Warsaw, ul. Mińska 25 Building. 63, 03-808 Warsaw, Poland. KRS 0000536581 NIP 1132882374, REGON *********, company capital 505 050,00 PLN. <br><br>2. The competition is open to residents of Europe aged 18 years and over, and excludes any employees of Custom Sp. z o.o. and their close relatives.<br><br>3. There is no entry fee to enter this competition.<br><br>4. By entering this competition, the entrant is indicating his/her agreement to be bound by these terms and conditions.<br><br>5. Details regarding entry for the competition can be found at <a href=\"https://www.instagram.com/tylko\" target=\"_blank\" rel=\"noopener noreferrer\">Tylko’s Instagram</a> with rules available in the captions and a full description in the link in bio.<br><br>6. Only one entry will be accepted per person, per print. Multiple entries from the same person for the same print will be disqualified.<br><br>7. The closing date for entry will be 23.07.2019. After this date, no further entries to the competition will be considered.<br><br>8. Competition rules and details on how to enter are as follows:<br><br>a. Follow @tylko and @ana__popescu on Instagram<br>b. Regram 1, 2 or 3 of the prints you’d like to win (all available on the @tylko feed)<br>c. Write a short story in English (20 words maximum) about the room pictured in the caption <br>d. Use hashtag #TylkoAna<br><br>Three winners will be announced on 26.07 on Tylko’s Insta Stories and contacted via direct message.<br>If any of the winners fails to reply to the message with the shipping details by 29.07, the next runner-up will be chosen.<br><br>9. The promoter reserves the right to cancel or amend the competition and these terms and conditions without notice in the event of a catastrophe, war, civil or military disturbance, act of God or any actual or anticipated breach of any applicable law or regulation or any other event outside of the promoter’s control. All entrants will be notified of any changes to the competition as soon as possible by the promoter.<br><br>10. The promoter is not responsible for inaccurate prize details supplied to any entrant by any third party connected with this competition.<br><br>11. The prize is as stated and no cash or other alternatives will be offered. The prizes are non-transferable. Prizes will be awarded to a total of three entrants. The prize is three original prints of Ana Popescu’s artwork created exclusively for Tylko and three vouchers for 500€ that can be used to purchase on tylko.com. Each of the three winners will receive one print and one voucher each.<br><br>12. The three winners will be selected on the basis of the most creative and interesting description of the reposted visual (one winner per each image), providing that all rules of entry are followed.<br><br>13. The winners will be notified through Instagram via the respective accounts with which they entered the contest on the day the winning results are announced (26.07.2019). If the winner cannot be contacted or does not claim the prize by 29.07.2019, we reserve the right to withdraw the prize from the winner and pick a replacement winner.<br><br>14. The promoter will notify the winner when and where the prize can be collected / will be delivered.<br><br>15. The promoter’s decision in respect of all matters to do with the competition will be final and no correspondence will be entered into.<br><br>16. The competition and these terms and conditions will be governed by Polish law and any disputes will be subject to the exclusive jurisdiction of the courts of Poland.<br><br>17. The winner agrees to release the use of his/her name and image and entry for use in any publicity material. Any personal data relating to the winner or any other entrants will be used solely in accordance with current Polish data protection legislation and will not be disclosed to a third party without the entrant’s prior consent.<br><br>18. This promotion is in no way sponsored, endorsed, administered by, or associated with Facebook, Instagram or any other social network. You are providing your information to Custom Sp. z o.o. and not to any other party.<br><br>19. Custom Sp. z o.o.‘s decision regarding participants and selection of winners is final. No correspondence relating to the competition will be entered into.<br><br>20. The entrant must follow @tylko and @ana__popescu on Instagram in order to be considered.<br><br>21. Custom Sp. z o.o. shall have the right at its sole discretion and at any time to change or modify these terms and conditions, with such changes becoming effective immediately upon posting to this webpage.<br><br>22. Participants in the contest agree to the processing of personal data by Custom Sp. z o.o for the purpose of organizing the competition.</p>"

msgid "anapopescu_terms_header_1"
msgstr "Tylko x Ana Popescu"

msgid "announcement_covid_heading_v2"
msgstr "Shopping during COVID-19: updates"

msgid "announcement_covid_more_v2"
msgstr "Read more"

msgid "announcement_covid_subheading_v2"
msgstr "We're taking necessary safety measures to carry on with production. Despite local regulations and stopovers, 95% of orders are being delivered perfectly on time."

msgid "announcement_fastdelivery_heading"
msgstr "Your custom storage in less than 4 weeks"

msgid "announcement_fastdelivery_link"
msgstr "<br> Go get 'em!"

msgid "announcement_fastdelivery_subheading"
msgstr "Shop your Tylko now and we’ll deliver it in less than 4 weeks. Orders including Tylko Original Modern new colours might take longer."

msgid "armchair_pdp_name"
msgstr "armchair"

msgid "armrest_module"
msgstr "Armrest"

msgid "armrest_module_cover"
msgstr "Cover for armrest"

msgid "assembly_service_ask_for_preferred_date_1"
msgstr "Let's find a day that works for you."

msgid "assembly_service_ask_for_preferred_date_2"
msgstr "Please let us know the preferred dates and times for your Tylko assembly service:"

msgid "assembly_service_ask_for_preferred_date_placeholder"
msgstr "Your message"

#, python-format
msgid "assembly_service_date_expired_%(date)s_%(from_hour)s_%(to_hour)s"
msgstr "Unfortunately %(date)s %(from_hour)s - %(to_hour)s is no longer available for your assembly service. Please check the alternate dates shared in your email to see if there is a date slot that will work. If none of the dates are available (or none work for you), please click the “New Date Request” button to generate a new set of dates."

msgid "assembly_service_dates_already_selected"
msgstr "The assembly date for this order has already been selected. Please check your email for your confirmation."

#, python-format
msgid "assembly_service_dates_chosen_confirmation_%(date)s_%(from_hour)s_%(to_hour)s"
msgstr "Thanks! We’ve booked your assembly for %(date)s %(from_hour)s - %(to_hour)s, and will send an email confirmation in the next few minutes, too."

msgid "assembly_service_dates_confirm_answer_no"
msgstr "No"

msgid "assembly_service_dates_confirm_answer_yes"
msgstr "Yes"

msgid "assembly_service_dates_confirm_question_%(date)s"
msgstr "Please click YES below to confirm %(date)s as your assembly service date."

msgid "assembly_service_new_dates_requested"
msgstr "Thank you for requesting new assembly dates. You will receive an email from our team with new possible dates shortly."

msgid "assembly_service_request_new_date_additional_confirmation_text"
msgstr "By continuing, you confirm that you will lose the above proposed date(s) and be provided with new options. This action cannot be undone, and we will email you as soon as possible with new dates."

msgid "assembly_service_request_new_date_confirmation"
msgstr "We'll do our best to schedule your assembly service to align with your request, however please be aware that your exact dates may not be available."

msgid "assembly_service_request_new_dates"
msgstr "REQUEST NEW DATES"

msgid "attribution_user_id"
msgstr "This cookie helps us attribute the conversion to the correct channel."

msgid "basic_pdp_product_specs_header_1"
msgstr "Product details"

msgid "basic_pdp_product_specs_section_1_header_1"
msgstr "Shelf parameters"

msgid "basic_pdp_product_specs_section_1_header_2_1"
msgstr "Width"

msgid "basic_pdp_product_specs_section_1_header_2_2"
msgstr "Height"

msgid "basic_pdp_product_specs_section_1_header_2_3"
msgstr "Depth"

msgid "basic_pdp_product_specs_section_1_header_2_4"
msgstr "Compartment max load"

msgid "basic_pdp_product_specs_section_1_header_2_5"
msgstr "Shelf max load"

msgid "basic_pdp_product_specs_section_2_description_1"
msgstr "<li>Weighted to self-close</li><li>Extruded aluminum handles</li>"

msgid "basic_pdp_product_specs_section_2_description_2"
msgstr "<li>Extendable runners for deep access</li><li>Weighted to self-close</li><li>Extruded aluminum handles</li>"

msgid "basic_pdp_product_specs_section_2_description_3"
msgstr "<li>Adjustable to sit on uneven floors</li>"

msgid "basic_pdp_product_specs_section_2_description_4"
msgstr "<li>A plain-language instruction manual</li><li>Padded tool for shelf disassembly</li><li>Wall fasteners and universal screws for safety</li>"

msgid "basic_pdp_product_specs_section_2_header_1"
msgstr "Features"

msgid "basic_pdp_product_specs_section_2_header_2_1"
msgstr "Doors"

msgid "basic_pdp_product_specs_section_2_header_2_2"
msgstr "Drawers"

msgid "basic_pdp_product_specs_section_2_header_2_3"
msgstr "Shelf Feet"

msgid "basic_pdp_product_specs_section_2_header_2_4"
msgstr "What's Included"

msgid "basic_pdp_product_specs_section_3_description_1_1"
msgstr "Save valuable time and energy with our colour-coded, click-together assembly system. Just unpack the numbered boxes in order, follow the easy instructions and you’ll have your shelf up in minutes."

msgid "basic_pdp_product_specs_section_3_description_2"
msgstr "<li>Pre-mounted hinge hardware</li><li>Click together to connect</li>"

msgid "basic_pdp_product_specs_section_3_description_3"
msgstr "<li>Pre-mounted slider hardware</li><li>Parts easily click together</li><li>Slide in the shelf to complete</li><li>Max load bearing: 30kg</li>"

msgid "basic_pdp_product_specs_section_3_header_1"
msgstr "Assembly"

msgid "basic_pdp_product_specs_section_3_header_2_1"
msgstr "Click it together. Quick and easy."

msgid "basic_pdp_product_specs_section_3_header_2_2"
msgstr "Doors"

msgid "basic_pdp_product_specs_section_3_header_2_3"
msgstr "Drawers"

msgid "basic_pdp_product_specs_section_4_description_1"
msgstr "Our certified particle board is 18 mm thick for durability and sturdiness. It won’t sag, even after years of use. It features wrapped edges with a sleek concealing band to create a smooth, unnoticeable edge and a flawless, full-colour finish."

msgid "basic_pdp_product_specs_section_4_description_2"
msgstr "Once assembled, use gentle furniture cleaners and a dry cloth to keep your shelf pristine."

msgid "basic_pdp_product_specs_section_4_header_1"
msgstr "Material and quality"

msgid "basic_pdp_product_specs_section_4_header_2_1"
msgstr "Material"

msgid "basic_pdp_product_specs_section_4_header_2_2"
msgstr "Caring for your Shelf"

msgid "basic_pdp_product_specs_section_5_description_1"
msgstr "If for any reason you’re not happy with your shelf, we’ll pick it back up for free within 100 days for a full refund. No questions asked."

msgid "basic_pdp_product_specs_section_5_description_2"
msgstr "We professionally deliver your Tylko shelf for free – right to your doorstep. It will arrive in labelled flat pack boxes for quick, easy assembly. "

msgid "basic_pdp_product_specs_section_5_description_3_1"
msgstr "We ship to the following countries: Austria, Belgium, Bulgaria, Croatia, Czech Republic, Denmark, Estonia, Finland, France, Germany, Greece, Hungary, Ireland, Italy, Latvia, Lithuania, Luxembourg, The Netherlands, Poland, Portugal, Romania, Slovakia, Slovenia, Spain, Sweden, Switzerland, Norway and the United Kingdom. "

msgid "basic_pdp_product_specs_section_5_description_4"
msgstr "We accept most major credit cards (Mastercard, Visa) and payments through PayPal.<br><br>Additional payment options differ depending on the country. All transactions are processed securely via our payment provider Adyen.<br><br>For more information regarding payment please read our"

msgid "basic_pdp_product_specs_section_5_description_4_buton"
msgstr "FAQs"

msgid "basic_pdp_product_specs_section_5_description_5"
msgstr "All of our furniture is made from high-quality materials that can withstand tough everyday use. We provide a two-year guarantee against failure and manufacturing flaws. For more details and conditions please read our "

msgid "basic_pdp_product_specs_section_5_description_5_buton"
msgstr "Terms&nbsp;of&nbsp;Service"

msgid "basic_pdp_product_specs_section_5_header_1"
msgstr "Service"

msgid "basic_pdp_product_specs_section_5_header_2_1"
msgstr "Free Returns"

msgid "basic_pdp_product_specs_section_5_header_2_2"
msgstr "Free delivery"

msgid "basic_pdp_product_specs_section_5_header_2_3"
msgstr "Shipping"

msgid "basic_pdp_product_specs_section_5_header_2_4"
msgstr "Payments"

msgid "basic_pdp_product_specs_section_5_header_2_5"
msgstr "The Tylko guarantee"

msgid "basic_pdp_section_2_header_1"
msgstr "A strong style statement"

msgid "basic_pdp_section_2_paragraph_1"
msgstr "Innovative materials. New-look wrapped edges. But what you’ll notice first about the Tylko Original Modern is the vibrant colour. Every shelf features a fresh, modern hue that is guaranteed to make a statement in any space."

msgid "basic_pdp_section_3_header_1"
msgstr "Easy assembly"

msgid "basic_pdp_section_3_header_2_1"
msgstr "Our click-in system makes assembly fast and easy. We are redefining what flat pack furniture can be."

msgid "basic_pdp_section_3_header_2_2"
msgstr "Lighter labelled packages. Plain-language manuals. A Tylko shelf can easily be built by one person."

msgid "basic_pdp_section_3_header_2_3"
msgstr "Pre-mounted and colour-coded connectors click into place with ease."

msgid "basic_pdp_section_4_header_1"
msgstr "Get a perfect fit with the Tylko app"

msgid "basic_pdp_section_4_paragraph_1"
msgstr "See your shelf live in your space before you decide to buy it. Edit size, height and style on the fly. Furniture shopping with the powerful Tylko app always means a perfect fit."

msgid "basic_pdp_section_5_header_1"
msgstr "We’re here to make<br>you comfortable"

msgid "basic_pdp_section_5_header_2_1"
msgstr "Friendly<br><span class=\"bold\">personal service</span>"

msgid "basic_pdp_section_5_header_2_2"
msgstr "<span class=\"bold\">100 days</span><br>to fall in love"

msgid "basic_pdp_section_5_header_2_3"
msgstr "<span class=\"bold\">100 000</span><br>happy customers"

msgid "basic_pdp_section_6_buton_1"
msgstr "More customer reviews"

msgid "basic_pdp_section_6_header_1"
msgstr "Customer Reviews"

msgid "basic_pdp_section_7_header_1"
msgstr "Frequently asked questions"

msgid "basic_pdp_section_7_header_2_1"
msgstr "What’s the difference between Tylko Original shelves?"

msgid "basic_pdp_section_7_header_2_2"
msgstr "How will I know if a shelf colour will suit my home?"

msgid "basic_pdp_section_7_header_2_3"
msgstr "Is a Tylko shelf safe to use in a child’s room?"

msgid "basic_pdp_section_7_header_2_4"
msgstr "How does the 100-day free return work? "

msgid "basic_pdp_section_7_header_2_5"
msgstr "Why do you offer only three row sizes?"

msgid "basic_pdp_section_7_paragraph_1"
msgstr "Tylko Original Classic and Tylko Original Modern are both based on the same iconic Tylko shelf design and feature the same customisation options. Both are delivered in flat packs, and utlilise our easy click-in assembly system. The key difference between the two product families lies in the general design approach and the materials used. Tylko Original Classic is our original design, focusing on natural materials and classic style that will stand the test of time, while the Tylko Original Modern line is a more adventurous approach, with the shelves available in a range of trendy colours with fully-wrapped edges for a bold and sleek look. "

msgid "basic_pdp_section_7_paragraph_2"
msgstr "We offer sample kits that let you experience our great colours and finishes – perfect if you’re having a hard time deciding! You can order three different sample kits at the bottom of <a href=\"https://tylko.com/product-lines/\" class=\"link--color text-no-underline\">this page</a>. We can also provide the RAL/NCS codes for all of our colours."

msgid "basic_pdp_section_7_paragraph_3"
msgstr "Absolutely! Each shelf is made from emission-free, non-toxic particle board, and features a natural veneer top and a water-based lacquer that’s safe for little and large people. We offer rounded handles and soft-close doors and drawers so no fingers can get caught, as well as wall mounts for extra stability. Because of our furniture’s ergonomic features, our furniture is suitable to be handled by children over the age of 12."

msgid "basic_pdp_section_7_paragraph_4"
msgstr "Try your shelf for 100 days. If for any reason you’re not happy with it, we’ll pick it back up for free and give you a full refund. No questions asked."

msgid "basic_pdp_section_7_paragraph_5"
msgstr "Our three row heights are influenced by the common everyday objects of our customers – things like photo frames, vinyl records, oversized coffee table books and shoeboxes. The three heights (18 cm, 28 cm, 38 cm) have been carefully designed so that no matter how much you adjust or adapt them, they won’t sway or sag under weight."

msgid "blind"
msgstr "This cookie is used for user testing purposes."

msgid "bubble_cart_button_checkout"
msgstr "Go to checkout"

msgid "bubble_cart_close"
msgstr "Close"

msgid "bubble_cart_empty"
msgstr "Your cart is empty"

msgid "bubble_empty_add_new_designs"
msgstr "Why not fill it with new designs?"

msgid "bubble_start_designing"
msgstr "Start designing"

msgid "bubble_wishlist_header"
msgstr "Added to Wishlist"

msgid "button_colour_unavailable"
msgstr "Colour unavailable"

msgid "cRow_wall_storage_plus_bottom_storage_too_tall_info"
msgstr "This furniture is too tall for additional Lower Storage."

msgid "cRow_wall_storage_plus_mobile_tab_bottom_storage"
msgstr "LOWER STORAGE"

msgid "cRow_wall_storage_plus_mobile_tab_bottom_storage_info"
msgstr "This furniture is too tall for additional <span class=\"inline-block\">Lower Storage.</span>"

msgid "cRow_wall_storage_plus_mobile_tab_upper_storage"
msgstr "UPPER STORAGE"

msgid "cRow_wall_storage_plus_mobile_tab_upper_storage_info"
msgstr "This furniture is too tall for additional <span class=\"inline-block\">Upper Storage.&nbsp;</span>"

msgid "cRow_wall_storage_plus_modal_bottom_storage_remove_button"
msgstr "Remove lower storage"

msgid "cRow_wall_storage_plus_modal_bottom_storage_title"
msgstr "Lower storage"

msgid "cRow_wall_storage_plus_modal_toggle_bottom_storage_title"
msgstr "Lower storage height"

msgid "cRow_wall_storage_plus_modal_toggle_upper_storage_title"
msgstr "Upper storage height"

msgid "cRow_wall_storage_plus_modal_upper_storage_remove_button"
msgstr "Remove upper storage"

msgid "cRow_wall_storage_plus_modal_upper_storage_title"
msgstr "Upper storage"

msgid "cRow_wall_storage_plus_snackbar"
msgstr "Include additional storage"

msgid "cRow_wall_storage_plus_upper_storage_too_tall_info"
msgstr "This furniture is too tall for additional Upper Storage."

msgid "cWatWar_mobile_modal_layer_column_width_medium_body"
msgstr "A shelf perfect for two stacks of jumpers or t-shirts and a rail that keeps your clothes wrinkle-free, this average-sized segment makes a great place for every garment."

msgid "cWatWar_mobile_modal_layer_column_width_medium_title"
msgstr "Medium Segment"

msgid "cWatWar_mobile_modal_layer_column_width_narrow_body"
msgstr "With shelf space for one stack of clothing and a narrower rail, this slim segment is ideal for streamlined clothes storage that keeps everything neat and tidy."

msgid "cWatWar_mobile_modal_layer_column_width_narrow_title"
msgstr "Narrow Segment"

msgid "cWatWar_mobile_modal_layer_column_width_wide_body"
msgstr "With enough shelf space for three stacks of clothing and an ample rail to hold clothes without overstuffing, this is the most spacious way to keep your outfits ready to go and wrinkle-free."

msgid "cWatWar_mobile_modal_layer_column_width_wide_title"
msgstr "Wide Segment"

msgid "cWatWar_mobile_modal_layer_corner_body"
msgstr "For Wardrobes that will be placed in a corner, select a door that opens against the wall for easy access and the best light."

msgid "cWatWar_mobile_modal_layer_corner_title"
msgstr "Corner Wardrobes"

msgid "cWatWar_mobile_modal_layer_free_space_body"
msgstr "You’ll need 51cm of clearance for the doors to open smoothly. Be sure to allow space when planning out your room."

msgid "cWatWar_mobile_modal_layer_free_space_title"
msgstr "Leave Room for Doors"

msgid "cWatWar_mobile_modal_layer_lighting_body"
msgstr "Choose a door opening direction that doesn’t block your light sources for the best visibility. "

msgid "cWatWar_mobile_modal_layer_lighting_title"
msgstr "Get the Best Light"

msgid "cWatWar_mobile_modal_layer_title"
msgstr "Pro Storage Tips"

msgid "cart_context_navbar_left"
msgstr "Free Shipping and Returns"

msgid "cart_context_navbar_left_moreinfo"
msgstr "Shipping is free. All orders are carefully fulfilled by our logistics partners FedEx and Rhenus. We ship to Austria, Belgium, Bulgaria, Croatia, Czech, Denmark, Estonia, Finland, France, Germany, Greece, Hungary, Ireland, Italy, Latvia, Lithuania, Luxembourg, Netherlands, Poland, Portugal, Romania, Slovakia, Slovenia, Spain, Sweden, Switzerland, Norway and the United Kingdom."

msgid "cart_context_navbar_middle"
msgstr "Hotline"

msgid "cart_context_navbar_middle_moreinfo"
msgstr "Question or a suggestion? You can reach us Monday to Friday between 08:00 and 24:00, and Saturday to Sunday between 10:00 and 24:00. Just give us a call – we’d be more than happy to hear from you!"

msgid "cart_context_navbar_right"
msgstr "Secure shopping"

msgid "cart_context_navbar_right_moreinfo"
msgstr "We use the popular security protocol SSL to make sure no one can see or steal your sensitive data. This includes log-in credentials and bank account details."

msgid "cartabandoner_mail_5a_samples_subject_line"
msgstr "❤️ from Tylko: Try before you buy. Order your sample set today!"

msgid "cf-bulk-consent-080ba036-0576-4fe4-9946-99bfecfd200a"
msgstr "This cookie has been set up by CookieFirst and enables synchronisation of cookie preferences across domains connected to a company."

msgid "chaise_longue_module"
msgstr "Chaise longue"

msgid "chaise_longue_module_cover"
msgstr "Cover for chaise longue"

msgid "chaise_longue_pdp_name"
msgstr "chaise_longue"

msgid "checkout.dropdown.answer.floor.0.house"
msgstr "0 (ground floor / detached house)"

msgid "checkout_chat_cookies_popup_body"
msgstr "You have disabled functional cookies on our website, and they are necessary for the chat feature to work properly. "

msgid "checkout_chat_cookies_popup_button"
msgstr "Adjust cookies "

msgid "checkout_chat_cookies_popup_headline"
msgstr "Chat only works with cookies"

msgid "checkout_promo_notyf"
msgstr "This promo code cannot be used with any other code or promo offer."

msgid "checkout_steps_header_1"
msgstr "1. Address"

msgid "checkout_steps_header_3"
msgstr "3. Summary"

msgid "cntPv"
msgstr "Cookie to store count of visited pages in session."

msgid "color_s01_corduroy_beige"
msgstr "Beige"

msgid "color_s01_corduroy_blush_pink"
msgstr "Blush Pink"

msgid "color_s01_corduroy_brown"
msgstr "Brown"

msgid "color_s01_corduroy_cobalt_blue"
msgstr "Cobalt Blue"

msgid "color_s01_corduroy_light_grey"
msgstr "Light Grey"

msgid "color_s01_corduroy_mustard"
msgstr "Mustard"

msgid "color_s01_corduroy_off_white"
msgstr "Off-White"

msgid "color_s01_corduroy_olive_green"
msgstr "Olive Green"

msgid "color_s01_multicolor"
msgstr "Multicolor"

msgid "color_s01_rewool2_dark_brown"
msgstr "Dark Brown"

msgid "color_s01_rewool2_green"
msgstr "Green"

msgid "color_s01_rewool2_grey"
msgstr "Grey"

msgid "color_s01_rewool2_khaki"
msgstr "Khaki"

msgid "color_s01_rewool2_lilac_blue"
msgstr "Lilac Blue"

msgid "color_s01_rewool2_pale_yellow"
msgstr "Pale Yellow"

msgid "color_s01_rewool2_powder_pink"
msgstr "Powder Pink"

msgid "common.material.beige"
msgstr "common.material.beige"

msgid "common.material.dusty_pink"
msgstr "common.material.dusty_pink"

msgid "common.material.indygo"
msgstr "common.material.indygo"

msgid "common.original_classic"
msgstr "common.original_classic"

msgid "common.original_classic_sideboard"
msgstr "common.original_classic_sideboard"

msgid "common.original_modern"
msgstr "common.original_modern"

msgid "common.original_modern_sideboard"
msgstr "common.original_modern_sideboard"

msgid "common.type23"
msgstr "Tone"

msgid "common.type24"
msgstr "Tone"

msgid "common.type25"
msgstr "Tone"

msgid "common_add_to_cart"
msgstr "Add to Cart"

msgid "common_add_to_wishlist"
msgstr "Add to Wishlist"

#, python-format
msgid "common_all_reviews_%(reviews_count)s"
msgstr "See all %(reviews_count)s Reviews"

msgid "common_app"
msgstr "App"

msgid "common_austria"
msgstr "Austria"

msgid "common_b2b_program"
msgstr "Tylko for Business "

msgid "common_beige"
msgstr "Beige"

msgid "common_beige_feminine"
msgstr "common_beige_feminine"

msgid "common_beige_masculine"
msgstr "common_beige_masculine"

msgid "common_beige_neuter"
msgstr "common_beige_neuter"

msgid "common_belgium"
msgstr "Belgium"

msgid "common_billing_address"
msgstr "Billing Address"

msgid "common_black"
msgstr "Black"

msgid "common_black_feminine"
msgstr "common_black_feminine"

msgid "common_black_masculine"
msgstr "common_black_masculine"

msgid "common_black_neuter"
msgstr "common_black_neuter"

msgid "common_bulgaria"
msgstr "Bulgaria"

msgid "common_burgundy_red"
msgstr "Burgundy Red"

msgid "common_burgundy_red_feminine"
msgstr "common_burgundy_red_feminine"

msgid "common_burgundy_red_masculine"
msgstr "common_burgundy_red_masculine"

msgid "common_burgundy_red_neuter"
msgstr "common_burgundy_red_neuter"

msgid "common_button_login"
msgstr "Login"

msgid "common_cart_button"
msgstr "Buy"

msgid "common_clay_brown"
msgstr "Brown"

msgid "common_clay_brown_feminine"
msgstr "common_clay_brown_feminine"

msgid "common_clay_brown_masculine"
msgstr "common_clay_brown_masculine"

msgid "common_clay_brown_neuter"
msgstr "common_clay_brown_neuter"

msgid "common_close"
msgstr "Close"

msgid "common_color_label_classic_bold"
msgstr "Bold"

msgid "common_color_label_classic_core"
msgstr "Core"

msgid "common_color_label_classic_veneer"
msgstr "Natural finish"

msgid "common_color_label_modern_mix"
msgstr "Mixes"

msgid "common_color_label_modern_neutral"
msgstr "Neutral"

msgid "common_color_label_modern_special"
msgstr "Special"

msgid "common_color_label_modern_vibrant"
msgstr "Vibrant"

msgid "common_color_label_premium"
msgstr "Premium colours"

msgid "common_color_modal_hint"
msgstr "Want to see the full colour range?"

msgid "common_color_modal_link"
msgstr "Take a closer look"

msgid "common_color_tooltip_grey_dark_grey"
msgstr "Grey + Dark Grey"

msgid "common_configurator_backpanels"
msgstr "Back panels"

msgid "common_configurator_backpanels_disclaimer"
msgstr "Back panels are behind drawers and doors by default."

msgid "common_configurator_backpanels_nonvinyl_tooltip"
msgstr "Practical yet stylish: back panels make sure things won’t fall behind your shelf and help create a consistent look against colourful walls. If included, they will feature on all rows and decrease the depth of compartments by 2cm. Please note that cable management holes are not included."

msgid "common_configurator_backpanels_off"
msgstr "Off"

msgid "common_configurator_backpanels_on"
msgstr "On"

msgid "common_configurator_backpanels_vinyl_disclaimer"
msgstr "Vinyl storage is only available with back panels."

msgid "common_configurator_backpanels_vinyl_tooltip"
msgstr "All vinyl storage comes with back panels – it’s a match made in heaven. Looking for something without back panels? Try our sideboards!"

msgid "common_configurator_backpanels_vinyl_tooltip_cta"
msgstr "Design your sideboard"

msgid "common_configurator_color"
msgstr "Color"

msgid "common_configurator_color_classic_red"
msgstr "Classic Red"

msgid "common_configurator_color_dusty_pink"
msgstr "Dusty Pink"

msgid "common_configurator_color_inky_black"
msgstr "Inky Black"

msgid "common_configurator_color_midnight_blue"
msgstr "Midnight Blue"

msgid "common_configurator_color_midnight_blue_feminine"
msgstr "common_configurator_color_midnight_blue_feminine"

msgid "common_configurator_color_midnight_blue_masculine"
msgstr "common_configurator_color_midnight_blue_masculine"

msgid "common_configurator_color_midnight_blue_neuter"
msgstr "common_configurator_color_midnight_blue_neuter"

msgid "common_configurator_color_mint"
msgstr "Mint + Forest Green"

msgid "common_configurator_color_mint_feminine"
msgstr "common_configurator_color_mint_feminine"

msgid "common_configurator_color_mint_masculine"
msgstr "common_configurator_color_mint_masculine"

msgid "common_configurator_color_mint_neuter"
msgstr "common_configurator_color_mint_neuter"

msgid "common_configurator_color_off_white"
msgstr "Off-White"

msgid "common_configurator_color_oyster_beige"
msgstr "Cashmere Beige"

msgid "common_configurator_color_pistachio_green"
msgstr "Pistachio Green"

msgid "common_configurator_color_powder_pink"
msgstr "Powder Pink"

msgid "common_configurator_color_sand"
msgstr "Sand + Midnight Blue"

msgid "common_configurator_color_sand_feminine"
msgstr "common_configurator_color_sand_feminine"

msgid "common_configurator_color_sand_masculine"
msgstr "common_configurator_color_sand_masculine"

msgid "common_configurator_color_sand_neuter"
msgstr "common_configurator_color_sand_neuter"

msgid "common_configurator_color_snow_white"
msgstr "White"

msgid "common_configurator_color_snow_white_feminine"
msgstr "common_configurator_color_snow_white_feminine"

msgid "common_configurator_color_snow_white_masculine"
msgstr "common_configurator_color_snow_white_masculine"

msgid "common_configurator_color_snow_white_neuter"
msgstr "common_configurator_color_snow_white_neuter"

msgid "common_configurator_color_terracotta"
msgstr "Terracotta"

msgid "common_configurator_color_terracotta_feminine"
msgstr "common_configurator_color_terracotta_feminine"

msgid "common_configurator_color_terracotta_masculine"
msgstr "common_configurator_color_terracotta_masculine"

msgid "common_configurator_color_terracotta_neuter"
msgstr "common_configurator_color_terracotta_neuter"

msgid "common_configurator_color_veneer_ash"
msgstr "White Oak"

msgid "common_configurator_color_veneer_ash_feminine"
msgstr "common_configurator_color_veneer_ash_feminine"

msgid "common_configurator_color_veneer_ash_masculine"
msgstr "common_configurator_color_veneer_ash_masculine"

msgid "common_configurator_color_veneer_ash_neuter"
msgstr "common_configurator_color_veneer_ash_neuter"

msgid "common_configurator_color_veneer_dark_oak"
msgstr "Walnut"

msgid "common_configurator_color_veneer_dark_oak_feminine"
msgstr "valnut"

msgid "common_configurator_color_veneer_dark_oak_masculine"
msgstr "valnut"

msgid "common_configurator_color_veneer_oak"
msgstr "Oak"

msgid "common_configurator_color_veneer_oak_feminine"
msgstr "common_configurator_color_veneer_oak_feminine"

msgid "common_configurator_color_veneer_oak_masculine"
msgstr "common_configurator_color_veneer_oak_masculine"

msgid "common_configurator_color_veneer_oak_neuter"
msgstr "common_configurator_color_veneer_oak_neuter"

msgid "common_configurator_color_yellow"
msgstr "Yellow"

msgid "common_configurator_depth_tooltip_paragraph_1"
msgstr "Our new depth is a slimmer 24cm option - perfect for narrower spots. Keep in mind that drawers aren't available with this configuration, though."

msgid "common_configurator_depth_vinyls_tooltip_buton_1"
msgstr "Design a sideboard"

msgid "common_configurator_depth_vinyls_tooltip_header_1"
msgstr "Depth"

msgid "common_configurator_depth_vinyls_tooltip_paragraph_1"
msgstr "A 40cm depth means perfect-fit storage for your vinyl record collection. The standard 32cm is a bit too shallow for LPs!"

msgid "common_configurator_depth_vinyls_tooltip_paragraph_2"
msgstr "Still want 24cm or 32cm deep shelves?"

msgid "common_configurator_doors"
msgstr "Doors"

msgid "common_configurator_doors_max"
msgstr "Max"

msgid "common_configurator_doors_new"
msgstr "new"

msgid "common_configurator_doors_none"
msgstr "None"

msgid "common_configurator_doors_size_limit"
msgstr "Row must be at least <span class=\"gray\">28cm</span> tall to fit doors"

msgid "common_configurator_doors_size_limit_mobile"
msgstr "Change row size to add doors"

msgid "common_configurator_doors_some"
msgstr "Some"

msgid "common_configurator_drawers"
msgstr "Drawers"

msgid "common_configurator_drawers_depth_limit"
msgstr "Drawers are only available for 32cm and 40cm depth shelves."

msgid "common_configurator_drawers_limit"
msgstr "Drawers are only available for rows below 160cm."

msgid "common_configurator_klarna_header"
msgstr "Flexible payment"

msgid "common_configurator_klarna_tooltip_paragraph_1"
msgstr "We work with Klarna to offer payment plans that are simple, straightforward and safe. Pay for your shelf in easy instalments, or get an invoice and pay later."

msgid "common_configurator_rows"
msgstr "Rows"

msgid "common_configurator_taxes_header"
msgstr "Taxes included"

msgid "common_continue_shopping"
msgstr "Continue Shopping"

msgid "common_contrastedge_info"
msgstr "Choose contrasting edges for storage that elevates your whole space."

msgid "common_cotton_beige"
msgstr "Cotton beige"

msgid "common_cotton_beige_feminine"
msgstr "common_cotton_beige_feminine"

msgid "common_cotton_beige_masculine"
msgstr "common_cotton_beige_masculine"

msgid "common_cotton_beige_neuter"
msgstr "common_cotton_beige_neuter"

msgid "common_creditcard_error"
msgstr "Uh-oh, please check carefully if the credit card details were entered correctly. Otherwise, please contact the bank or our Customer Service."

msgid "common_croatia"
msgstr "Croatia"

msgid "common_custom_order"
msgstr "Custom Order"

msgid "common_czech"
msgstr "Czech Rep."

msgid "common_czechia"
msgstr "Czechia"

msgid "common_deliver_comments"
msgstr "Delivery Comments"

msgid "common_delivery_details"
msgstr "Delivery times vary depending on your chosen color."

msgid "common_denmark"
msgstr "Denmark"

msgid "common_density"
msgstr "Density"

msgid "common_density_tooltip_link_button"
msgstr "How does density work?"

msgid "common_discount"
msgstr "Discount"

msgid "common_doors_and_drawers_info"
msgstr "Place your cursor over the design on the left and have fun adding or removing drawers and doors!"

msgid "common_doors_and_drawers_label"
msgstr "Doors and drawers"

msgid "common_eco_tax"
msgstr "eco-fee included"

msgid "common_edit_cookies"
msgstr "Edit cookies"

msgid "common_estonia"
msgstr "Estonia"

msgid "common_explore_cta"
msgstr "Explore"

msgid "common_faq"
msgstr "FAQ"

msgid "common_finish_contrast_edge"
msgstr "Coloured Edges"

msgid "common_finish_full_color"
msgstr "Full colour"

msgid "common_finish_label"
msgstr "Finish"

msgid "common_finish_plywood"
msgstr "Plywood"

msgid "common_finish_veneer"
msgstr "Veneer"

msgid "common_finland"
msgstr "Finland"

msgid "common_france"
msgstr "France"

msgid "common_free"
msgstr "Free"

msgid "common_free_shipping"
msgstr "Free Shipping <br>and Returns"

msgid "common_fullcolor_info"
msgstr "Go bold in your home. Choose from our range of vibrant hues for furniture that stands out."

msgid "common_germany"
msgstr "Germany"

msgid "common_greece"
msgstr "Greece"

msgid "common_grey"
msgstr "grey"

msgid "common_grey_dark_grey"
msgstr "Grey + Dark Grey"

msgid "common_grey_dark_grey_feminine"
msgstr "common_grey_dark_grey_feminine"

msgid "common_grey_dark_grey_masculine"
msgstr "common_grey_dark_grey_masculine"

msgid "common_grey_dark_grey_neuter"
msgstr "common_grey_dark_grey_neuter"

msgid "common_grey_t02"
msgstr "Grey"

msgid "common_grey_t02_feminine"
msgstr "common_grey_t02_feminine"

msgid "common_grey_t02_masculine"
msgstr "common_grey_t02_masculine"

msgid "common_grey_t02_neuter"
msgstr "common_grey_t02_neuter"

msgid "common_grid_dense"
msgstr "Dense Grid"

msgid "common_grid_standard"
msgstr "Standard Grid"

msgid "common_high_street"
msgstr "Typical high street:"

msgid "common_hungary"
msgstr "Hungary"

msgid "common_includes_vat"
msgstr "Includes VAT"

msgid "common_includes_vat_short"
msgstr "tax incl."

msgid "common_increased_demand"
msgstr "We provide free shipping and returns to the United Kingdom and to all European Union countries, including Switzerland and Norway."

msgid "common_ireland"
msgstr "Ireland"

msgid "common_italy"
msgstr "Italy"

msgid "common_item"
msgstr "Item"

msgid "common_items"
msgstr "Items"

msgid "common_jobs"
msgstr "Jobs"

msgid "common_journal"
msgstr "Journal"

msgid "common_label_new"
msgstr "New"

msgid "common_latvia"
msgstr "Latvia"

msgid "common_learn_more_about"
msgstr "Learn more about"

msgid "common_lighting"
msgstr "Lighting"

msgid "common_lithuania"
msgstr "Lithuania"

msgid "common_luxembourg"
msgstr "Luxembourg"

msgid "common_made_in_eu"
msgstr "Made in EU"

msgid "common_matte_black"
msgstr "Premium Matte Black"

msgid "common_matte_black_feminine"
msgstr "common_matte_black_feminine"

msgid "common_matte_black_masculine"
msgstr "common_matte_black_masculine"

msgid "common_matte_black_neuter"
msgstr "Premium Matte Black"

msgid "common_measuring_guide_button"
msgstr "See our guide "

msgid "common_measuring_guide_info"
msgstr "Ready to measure your space?"

msgid "common_netherlands"
msgstr "Netherlands"

msgid "common_new"
msgstr "new"

msgid "common_no"
msgstr "No"

msgid "common_norway"
msgstr "Norway"

msgid "common_norway_info_1_1"
msgstr "Shipping costs with handling charges and additional 25% of the Norwegian VAT are included."

msgid "common_olive_green"
msgstr "Green"

msgid "common_olive_green_feminine"
msgstr "common_olive_green_feminine"

msgid "common_olive_green_masculine"
msgstr "common_olive_green_masculine"

msgid "common_olive_green_neuter"
msgstr "common_olive_green_neuter"

msgid "common_omnibusnotice"
msgstr "The lowest price in the last 30 days:"

msgid "common_our_mission"
msgstr "Our Mission"

msgid "common_out_of_stock"
msgstr "Out of stock"

msgid "common_pdp_assembly_header_1"
msgstr "Optional assembly&nbsp;service"

msgid "common_pdp_assembly_header_2"
msgstr "Available in your location"

msgid "common_pdp_assembly_popup_body_1"
msgstr "You can select your optional assembly service at checkout."

msgid "common_pdp_assembly_popup_body_2"
msgstr "<li>Professional assembly and set-up by our trusted team </li><li>We’ll take away all the leftover packaging</li><li>The total price includes the items in your cart</li>"

msgid "common_pdp_assembly_popup_body_3"
msgstr "This service is currently available for customers in Germany, the Netherlands, Luxembourg, Austria, Belgium, France, Switzerland, Poland, Denmark, England and Scotland."

msgid "common_pdp_assembly_popup_body_4"
msgstr "For more info,"

msgid "common_pdp_assembly_popup_body_5"
msgstr "please read our FAQ"

msgid "common_pdp_assembly_popup_header"
msgstr "Optional assembly service"

msgid "common_pdp_assembly_popup_subheader"
msgstr "Let us do the heavy lifting for you."

msgid "common_pdp_assembly_tooltip"
msgstr "We can arrange quick and easy assembly by a Tylko expert right in your space."

msgid "common_pdp_assembly_tooltip_more"
msgstr "Learn More"

msgid "common_pdp_delivery_header_1"
msgstr "Free delivery"

msgid "common_pdp_delivery_tooltip_paragraph_1"
msgstr "We ship for free to the United Kingdom and to all EU countries, including Switzerland and Norway. Your shelf will arrive in clearly labelled flat pack boxes for a quick and easy assembly. "

msgid "common_pdp_rating_header_1"
msgstr "Customer rating"

msgid "common_pdp_rating_header_2_1"
msgstr "based on"

msgid "common_pdp_rating_header_2_2"
msgstr "reviews"

msgid "common_pdp_rating_header_2_3"
msgstr "Read the reviews"

msgid "common_pdp_returns_header_1"
msgstr "100 days to fall in love"

msgid "common_pdp_returns_header_2"
msgstr "or return it for free"

msgid "common_pdp_returns_tooltip_paragraph_1"
msgstr "If for any reason you’re not happy with your shelf, we’ll pick it back up for free within 100 days and give you a full refund. No questions asked."

msgid "common_plywood_info"
msgstr "Want extra wide compartments? Go for our premium plywood - carefully crafted from 13 layers of beautifully grained birchwood, it's one of the stongest materials out there."

msgid "common_plywood_info_detail"
msgstr "Maximum compartment width: 86cm"

msgid "common_poland"
msgstr "Poland"

msgid "common_popular_item"
msgstr "Popular Item"

msgid "common_portugal"
msgstr "Portugal"

msgid "common_press"
msgstr "Press &amp; Media"

msgid "common_prices_vat_0"
msgstr "All prices include 0% VAT."

msgid "common_privacy_policy"
msgstr "Privacy Policy"

msgid "common_products_single"
msgstr "Product"

msgid "common_promocode_notactive"
msgstr "Promo Code declined."

msgid "common_regular_price"
msgstr "Reg. price"

msgid "common_reisingers_pink"
msgstr "Reisinger Pink"

msgid "common_reisingers_pink_feminine"
msgstr "common_reisingers_pink_feminine"

msgid "common_reisingers_pink_masculine"
msgstr "common_reisingers_pink_masculine"

msgid "common_reisingers_pink_neuter"
msgstr "common_reisingers_pink_neuter"

msgid "common_reviews"
msgstr "Reviews"

msgid "common_romania"
msgstr "Romania"

msgid "common_s4l_rodo_checkbox_text"
msgstr "Yes, I agree to receive valuable content from Tylko in the form of occasional emails."

msgid "common_s4l_rodo_checkbox_text_2"
msgstr "The controller of your data is Tylko S.A. We use your personal data to allow you to save your design and, if you agree (by ticking the checkbox), to send you occasional Tylko marketing emails which may be of interest. You can read more in our <a href=\"https://tylko.com/privacy_policy/\" target=\"_blank\" style=\"text-decoration: none;\">Privacy Policy.</a>"

msgid "common_s4l_rodo_question_1"
msgstr "How is my data used?"

msgid "common_s4l_rodo_question_2"
msgstr " Learn more "

msgid "common_s4l_share_facebook_button_copy"
msgstr "Share"

msgid "common_s4l_share_twitter_button_copy"
msgstr "Tweet"

msgid "common_sage_green"
msgstr "Sage Green"

msgid "common_sage_green_feminine"
msgstr "common_sage_green_feminine"

msgid "common_sage_green_masculine"
msgstr "common_sage_green_masculine"

msgid "common_sage_green_neuter"
msgstr "common_sage_green_neuter"

msgid "common_sand_mustard_yellow"
msgstr "Sand + Mustard Yellow"

msgid "common_sand_mustard_yellow_feminine"
msgstr "common_sand_mustard_yellow_feminine"

msgid "common_sand_mustard_yellow_masculine"
msgstr "common_sand_mustard_yellow_masculine"

msgid "common_sand_mustard_yellow_neuter"
msgstr "common_sand_mustard_yellow_neuter"

msgid "common_save"
msgstr "Save"

msgid "common_scroll_hint"
msgstr "See more options"

msgid "common_section_label_color"
msgstr "Colour"

msgid "common_section_label_color_and_finish"
msgstr "Colour & finish"

msgid "common_section_label_function"
msgstr "Features"

msgid "common_section_label_size"
msgstr "Size"

msgid "common_section_label_style"
msgstr "Style"

msgid "common_ship"
msgstr "Shipping"

msgid "common_shipping"
msgstr "Ships in"

msgid "common_shipping_address"
msgstr "Shipping Address"

msgid "common_shipping_and_returns"
msgstr "Shipping & Returns"

msgid "common_shipping_mobile"
msgstr "Ships in <br/>"

msgid "common_sky_blue"
msgstr "Sky Blue"

msgid "common_sky_blue_feminine"
msgstr "common_sky_blue_feminine"

msgid "common_sky_blue_masculine"
msgstr "common_sky_blue_masculine"

msgid "common_sky_blue_neuter"
msgstr "common_sky_blue_neuter"

msgid "common_slovakia"
msgstr "Slovakia"

msgid "common_slovenia"
msgstr "Slovenia"

msgid "common_spain"
msgstr "Spain"

msgid "common_stone_gray"
msgstr "Stone Grey"

msgid "common_stone_gray_feminine"
msgstr "common_stone_gray_feminine"

msgid "common_stone_gray_masculine"
msgstr "common_stone_gray_masculine"

msgid "common_stone_gray_neuter"
msgstr "common_stone_gray_neuter"

msgid "common_sweden"
msgstr "Sweden"

msgid "common_switzerland"
msgstr "Switzerland"

msgid "common_switzerland_info_1_1"
msgstr "Shipping costs with handling charges and additional 8,1% of the Swiss VAT are included."

msgid "common_telenumber"
msgstr "+44 ************"

msgid "common_terms_of_service"
msgstr "Terms of Service"

msgid "common_till"
msgstr "till"

msgid "common_toolbar_dimensions_1"
msgstr "Show dimensions"

msgid "common_toolbar_dimensions_2"
msgstr "Hide dimensions"

msgid "common_toolbar_doors_1"
msgstr "Close all doors and drawers"

msgid "common_toolbar_doors_2"
msgstr "Open all doors and drawers"

msgid "common_toolbar_gallery_shalves"
msgstr "See wardrobes in real life"

msgid "common_toolbar_product_details"
msgstr "See product details"

msgid "common_toolbar_productdetails"
msgstr "See product details"

msgid "common_toolbar_share"
msgstr "Share"

msgid "common_total_price"
msgstr "Total Price"

msgid "common_tylko_shelf"
msgstr "Tylko Shelf"

msgid "common_united_kingdom"
msgstr "United Kingdom"

msgid "common_vat_incl"
msgstr "Vat incl."

msgid "common_vat_included"
msgstr "All prices include VAT"

msgid "common_veneer_info"
msgstr "Our premium veneer is crafted from particle board and covered in a layer of beautiful natural wood. Made from oak or ash, the unique grain detail is totally tactile."

msgid "common_veneer_info_detail"
msgstr "Maximum compartment width: 56cm"

msgid "common_walnut"
msgstr "Stone Grey + Walnut Veneer"

msgid "common_walnut_feminine"
msgstr "common_walnut_feminine"

msgid "common_walnut_masculine"
msgstr "common_walnut_masculine"

msgid "common_walnut_neuter"
msgstr "common_walnut_neuter"

msgid "common_white"
msgstr "White"

msgid "common_with_code"
msgstr "with code"

msgid "common_yes"
msgstr "Yes"

msgid "comparison_products_material_type02"
msgstr "Particle board"

#, python-format
msgid "complaint_service_date_expired_%(date)s_%(from_hour)s_%(to_hour)s"
msgstr "Unfortunately %(date)s %(from_hour)s - %(to_hour)s is no longer available for your Tylko Support Service. Please check the alternative dates shared in your email to see if there's a slot that works for you. If none of the dates you want are available, please click the “New date request” button to get a new set of dates."

msgid "complaint_service_dates_already_selected"
msgstr "The Tylko Support Service date for this order has already been selected. Please check your email for your confirmation."

#, python-format
msgid "complaint_service_dates_chosen_confirmation_%(date)s_%(from_hour)s_%(to_hour)s"
msgstr "Thanks! We’ve booked your Tylko Support Service for %(date)s %(from_hour)s - %(to_hour)s, and will send you an email confirmation in the next few minutes."

msgid "complaint_service_dates_confirm_question_%(date)s"
msgstr "Please click YES below to confirm %(date)s as your service date."

msgid "complaint_service_new_dates_requested"
msgstr "Thank you for requesting new Tylko Support Service dates. You will receive an email from our team with new possible slots shortly."

msgid "complaint_service_request_new_dates"
msgstr "REQUEST NEW DATES"

msgid "conf_webglnotworking_browser_text"
msgstr "Uh-oh! It seems that your device isn't compatible with our configurator."

#, python-format
msgid "conf_webglnotworking_browser_text_%(browser)s"
msgstr "You can try to download the newest version of %(browser)s. If that still doesn't work, let us know. We will try to solve this problem for you!"

msgid "conf_webglnotworking_device_header"
msgstr "Uh-oh! It seems that your device isn't compatible with our configurator."

msgid "conf_webglnotworking_device_text"
msgstr "Sorry, but your device doesn't support the technology needed to run the configurator. Please switch to a more modern or powerful device to use the configurator."

msgid "config.button.save.test"
msgstr "Save for later"

msgid "config.modal.color.line.hint"
msgstr "Want to configure shelf with these colours? Go to the {0} configurator."

msgid "config.modal.color.line.link"
msgstr "config.modal.color.line.link"

msgid "config.modal.color.sample.hint"
msgstr "Want to get a proper look and feel of our colours and finishes?"

msgid "config.modal.color.sample.link"
msgstr "Explore sample sets"

msgid "config.modal.color.section1.description"
msgstr "Our key colour range includes neutral shades trimmed with natural plywood for a timeless look and feel."

msgid "config.modal.color.section1.label"
msgstr "Plywood"

msgid "config.modal.color.section1.title"
msgstr "Core colours"

msgid "config.modal.color.section2.description"
msgstr "Our premium colour collection dials up the drama for a bold look in your space.  Perfect if you want to make a style statment."

msgid "config.modal.color.section2.label"
msgstr "Plywood"

msgid "config.modal.color.section2.title"
msgstr "Bold colours"

msgid "config.modal.color.section3.description"
msgstr "Our premium veneer is crafted from particle board and covered in a layer of beautiful natural wood. Made from oak or ash, the unique grain detail is totally tactile."

msgid "config.modal.color.section3.label"
msgstr "Veneer"

msgid "config.modal.color.section3.note"
msgstr "Note: openings wider than 56cm are unavailable in Veneer, due to its unique construction. If you're after extra wide compartments, opt for Plywood."

msgid "config.modal.color.section3.title"
msgstr "Natural finish"

msgid "config.modal.color.section4.description"
msgstr "Need furniture that belnds in beautifully? Our standard colours are also the most classic, for furniture that's truly timeless."

msgid "config.modal.color.section4.label"
msgstr "Full colour"

msgid "config.modal.color.section4.title"
msgstr "Neutral"

msgid "config.modal.color.section5.description"
msgstr "After a statement piece? Our mixed colour sets come with colouful contrasting edges for furniture that stands out in your space."

msgid "config.modal.color.section5.label"
msgstr "Contrasting edges"

msgid "config.modal.color.section5.title"
msgstr "Colour mixes"

msgid "config.modal.color.section6.description"
msgstr "Want furniture that stands out? Choose from our collection of bold all-over shades for storage that elevates your space in style."

msgid "config.modal.color.section6.label"
msgstr "Full colour"

msgid "config.modal.color.section6.title"
msgstr "Vibrant"

msgid "config.modal.color.section7.description1"
msgstr "We've teamed up with 3D artist Andrés Reisinger to create a special version of his iconic pink shade."

msgid "config.modal.color.section7.description2"
msgstr "Not just an ordinary black, our extra premium colour comes with a tactile, no-fingerprint finish."

msgid "config.modal.color.section7.label"
msgstr "Full colour"

msgid "config.modal.color.section7.link1"
msgstr "Discover more"

msgid "config.modal.color.section7.link2"
msgstr "Find out more"

msgid "config.modal.color.section7.subtitile1"
msgstr "Reisinger Pink"

msgid "config.modal.color.section7.subtitile2"
msgstr "Premium Matte Black"

msgid "config.modal.color.section7.titile"
msgstr "Premium colours"

msgid "config.modal.color.tab1"
msgstr "Classic"

msgid "config.modal.color.tab2"
msgstr "Modern"

msgid "config.modal.color.title"
msgstr "Choose your look and feel"

msgid "configurator_get_samples_body"
msgstr "Need help deciding? "

msgid "configurator_get_samples_body_promo"
msgstr "Can't decide?"

msgid "configurator_get_samples_button"
msgstr "Order samples "

msgid "configurator_get_samples_button_promo"
msgstr "Sample Sets, now on sale "

msgid "configurator_perfect_fit_desktop_link"
msgstr "Don’t like waiting?"

msgid "configurator_perfect_fit_mobile_link"
msgstr "Check Quick production configurator"

msgid "configurator_sku_desktop_link"
msgstr "Looking for the perfect fit?"

msgid "configurator_sku_mobile_body_link"
msgstr "Check out perfect fit configurator"

msgid "configurator_sku_mobile_link"
msgstr "Check out perfect fit configurator"

msgid "configurator_sku_mobile_title"
msgstr "Need more flexibility?"

msgid "configurator_taxes_FR_body"
msgstr "The price you see here is final — we'll never add extra taxes and costs without telling you.  Want to find out more about eco-fee?"

msgid "configurator_taxes_FR_cta"
msgstr "Find out more about eco-fee"

msgid "configurator_taxes_FR_header"
msgstr "Includes taxes and eco-fee"

#: frontend_cms/templates/front/account.html frontend_cms/templates/front/order-details.html
msgid "contact <NAME_EMAIL>."
msgstr "contact <NAME_EMAIL>."

msgid "contact_form_emailconfirmation_body_1"
msgstr "Hello,"

msgid "contact_form_emailconfirmation_body_2"
msgstr "Thank you for reaching out. We are here to assist you with any questions you may have. We are currently reviewing your message and will provide you with the information or assistance you need. Please bear in mind that currently, the response time is significantly extended and may take up to 3-5 business days. Thank you for your patience and understanding."

msgid "contact_form_emailconfirmation_header"
msgstr "We have received your message."

msgid "contact_form_emailconfirmation_signature"
msgstr "Tylko Customer Service"

msgid "contact_form_emailconfirmation_subheader"
msgstr "Your case number is"

msgid "contact_form_order_number"
msgstr "Order Number"

msgid "contact_form_thank_you_message_1_1"
msgstr "Thank you for your feedback! We will get back to you as soon as possible."

msgid "cookiebar_Privacy_policy"
msgstr "Privacy policy"

msgid "cookiebar_Privacy_settings"
msgstr "Privacy settings"

msgid "cookiebar_body_Advertising"
msgstr "These cookies give you access to tailored ads based on your interests."

msgid "cookiebar_body_Advertising_expand"
msgstr "These cookies will provide you with tailored ads based on your interests. They may be set through our site by our advertising partners and be used by those companies to build a profile of your interests. They do not store directly personal information, but are based on uniquely identifying your browser and internet device. If you do not allow these cookies, you will experience less targeted advertising."

msgid "cookiebar_body_Functional"
msgstr "We use these cookies to provide you with a personalised experience."

msgid "cookiebar_body_Functional_expand"
msgstr "We use these cookies to provide you with enhanced functionality, and a more personalised and smooth experience. Our website will remember your site preferences and other choices including username, region and your preferred language. These cookies are anonymous and don't track browsing activity on other websites. They can be set by us or by third party providers whose services we have added. If you deny these cookies, then some or all of these services may not function properly."

msgid "cookiebar_body_Necessary"
msgstr "These cookies are necessary for you to get the best out of our website."

msgid "cookiebar_body_Necessary_expand"
msgstr "These cookies are necessary for you to use our website properly. They are set by your actions in your request for our services. Examples include logging in, filling in forms or setting your privacy preferences. You can make your browser block these cookies, but some parts of our website may not work properly."

msgid "cookiebar_body_Performance"
msgstr "We use these cookies to keep elevating your shopping experience."

msgid "cookiebar_body_Performance_expand"
msgstr "These cookies allow us to elevate your shopping experience. They help us improve the performance of our website, by counting visits and traffic sources. All information collected by us will remain anonymous. By rejecting these cookies, we won't be able to monitor our website's performance while you are on it, and therefore cannot enhance your experience with us."

msgid "cookiebar_body_main"
msgstr "Tylko uses cookies to provide you with the smoothest shopping experience, by personalising content and ads, and gathering audience insights. If necessary, we may share relevant information with our partners for social media and analytic purposes. By clicking ‘That’s ok’, you are accepting all cookies and agreeing to our cookie policy, which applies to both the Tylko online shop and the Tylko journal. You can edit your preferences by selecting ‘Settings’. "

msgid "cookiebar_btn_accept"
msgstr "Accept all"

msgid "cookiebar_btn_ok"
msgstr "That’s OK"

msgid "cookiebar_btn_save"
msgstr "Save settings"

msgid "cookiebar_btn_settings"
msgstr "Settings"

msgid "cookiebar_domain_name"
msgstr "Domain name "

msgid "cookiebar_expiration"
msgstr "Expiration"

msgid "cookiebar_headline_Advertising"
msgstr "Advertising"

msgid "cookiebar_headline_Functional"
msgstr "Functional"

msgid "cookiebar_headline_Necessary"
msgstr "Necessary"

msgid "cookiebar_headline_Performance"
msgstr "Performance"

msgid "cookiebar_headline_main"
msgstr "Your experience matters to us. "

msgid "cookiebar_popup_headline_main"
msgstr "To provide you with a smooth experience, we would like your permission to use your data for the following purposes:"

msgid "cookiebar_popup_headline_main_cookies"
msgstr "This cookie list shows all cookies found on this website. It does not reflect the user's individual opt-out choices. "

msgid "cookiebar_povider"
msgstr "Provider"

msgid "cookiebar_tab_cookies"
msgstr "Cookies"

msgid "cookiebar_tab_settings"
msgstr "Settings"

msgid "cookiefirst-consent"
msgstr "This cookie saves your cookie preferences for this website. You can easily change these preferences or withdraw your consent."

msgid "cookiefirst-id"
msgstr "This cookie contains your unique ID so CookieFirst can identify unique visitors on this website."

msgid "corner_module"
msgstr "Corner"

msgid "corner_module_cover"
msgstr "Cover for corner"

msgid "corner_pdp_name"
msgstr "corner_sofa"

msgid "cover_pdp_name"
msgstr "cover"

msgid "covid_popup"
msgstr "This cookie is used to provide user with information on how the current COVID-19 status is affecting our production."

msgid "cplus_add_to_cart"
msgstr "Add to cart"

msgid "cplus_ash"
msgstr "White Oak Veneer"

msgid "cplus_backpanels_option1"
msgstr "OFF"

msgid "cplus_backpanels_option1\n"
msgstr "cplus_backpanels_option1\n"

msgid "cplus_backpanels_option2"
msgstr "ON"

msgid "cplus_button_share"
msgstr "Share"

msgid "cplus_cable"
msgstr "Cable Opening"

msgid "cplus_cable_option_1"
msgstr "OFF"

msgid "cplus_cable_option_2"
msgstr "ON"

msgid "cplus_color"
msgstr "Colour"

msgid "cplus_columns"
msgstr "Columns"

msgid "cplus_custom_fees"
msgstr "Customs fees included"

msgid "cplus_custom_fees_tooltip"
msgstr "No hidden fees or extra charges, either. Pick your perfect shelf and we take care of the rest."

msgid "cplus_density"
msgstr "Density"

msgid "cplus_density_info"
msgstr "Density means the number of sections or compartments within your furniture. More dense = more sections, less dense = more open and airy. Need more detail? {button} for a density deep dive."

msgid "cplus_density_info_link"
msgstr "Click here"

msgid "cplus_density_tips_url"
msgstr "https://tips.tylko.com/en/articles/11-density-what-it-is-and-how-it-works"

msgid "cplus_depth"
msgstr "Depth"

msgid "cplus_depth_tooltip_button"
msgstr "Design a different piece"

msgid "cplus_depth_tooltip_copy"
msgstr "40cm of depth makes the best use of space for sideboards."

msgid "cplus_depth_tooltip_copy_2"
msgstr "Looking for 24 or 32cm deep shelves? "

msgid "cplus_desk_height_info"
msgstr "All desks are 73cm high"

msgid "cplus_desks_back_panels"
msgstr "Back panel is added behind drawers, doors and legroom area by default."

msgid "cplus_desks_cable_option_1"
msgstr "OFF"

msgid "cplus_desks_cable_option_2"
msgstr "Left"

msgid "cplus_desks_cable_option_3"
msgstr "Right"

msgid "cplus_desktop_columns_info"
msgstr "Click on any column to adjust width with sliders."

msgid "cplus_discover"
msgstr "Discover more"

msgid "cplus_doors"
msgstr "Door Opening"

msgid "cplus_doors_option_1"
msgstr "Left"

msgid "cplus_doors_option_2"
msgstr "Right"

msgid "cplus_drawers"
msgstr "Drawers’ handles"

msgid "cplus_drawers_option_1"
msgstr "Left"

msgid "cplus_drawers_option_2"
msgstr "Right"

msgid "cplus_dressing_table_height_info"
msgstr "All dressing tables are 73cm high"

msgid "cplus_eco_tax"
msgstr "eco-fee"

msgid "cplus_eco_tax_desc"
msgstr "Includes taxes and "

msgid "cplus_emily_pdp_configurator_delivery_time_tooltip"
msgstr "Each custom piece is made to order, as we carry no stock. Due to high demand, our estimated shipping times are currently longer than usual."

msgid "cplus_features_cable_body"
msgstr "Pre-installed, colour-matched steel cable opening with magnetic cap keeps cables tidy."

msgid "cplus_features_cable_desk_body"
msgstr "Preinstalled and colour matched steel cable opening with magnetic cap keeps cables tidy at all times. For comfort's sake, desk includes two openings in the legroom area: one on the desk top and one on the back panel. "

msgid "cplus_features_cable_dressing_table_body"
msgstr "Preinstalled and colour matched steel cable opening with magnetic cap keeps cables tidy at all times. For comfort's sake, dressing table includes two openings in the legroom area: one on the dressing table top and one on the back panel. "

msgid "cplus_features_cable_header"
msgstr "Cable opening"

msgid "cplus_features_copy_1_t01p"
msgstr "Extruded aluminium full-length handles, self-closing and full extension runners for easy access."

msgid "cplus_features_copy_1_t01v"
msgstr "Self-closing, with solid wood full-length handles and extendable runners for deep access."

msgid "cplus_features_copy_1_t02"
msgstr "Extruded aluminium flush handles, self-closing and with extendable runners for deep access."

msgid "cplus_features_copy_2_t01p"
msgstr "Rounded, extruded aluminium handles that run the full height of the door. Safe and easy to grip."

msgid "cplus_features_copy_2_t01v"
msgstr "Solid wood handles designed in-house to perfectly match the veener doors. Warm and tactile."

msgid "cplus_features_copy_2_t02"
msgstr "Doors with ultra-modern flush handles and half-cylinder aluminium pulls. Tactile and understated."

msgid "cplus_features_header_1"
msgstr "Drawers"

msgid "cplus_features_header_2"
msgstr "Doors"

msgid "cplus_feet"
msgstr "Feet"

msgid "cplus_gallery_tooltip"
msgstr "See real-life shelves"

msgid "cplus_height"
msgstr "Height"

msgid "cplus_installment_payment"
msgstr "Pay in installments"

msgid "cplus_installment_payment_tooltip"
msgstr "Pick a payment plan and pay for your shelf in easy installments."

msgid "cplus_items_1"
msgstr "Hide items"

msgid "cplus_items_2"
msgstr "Show items"

msgid "cplus_legroom_placement"
msgstr "Legroom area"

msgid "cplus_legs_option_1"
msgstr "Standard"

msgid "cplus_legs_option_2"
msgstr "Legs"

msgid "cplus_legs_tooltip"
msgstr "Legs not available for 24cm depth."

msgid "cplus_legs_tooltip_2"
msgstr "Note: Legs add 10cm to the total shelf height."

msgid "cplus_loader_copy"
msgstr "Hold tight, we're putting your shelf together..."

msgid "cplus_material_ash"
msgstr "White Oak"

msgid "cplus_material_black"
msgstr "Black Plywood"

msgid "cplus_material_blue"
msgstr "Midnight Blue"

msgid "cplus_material_blue_plywood"
msgstr "Blue Plywood"

msgid "cplus_material_dark_brown"
msgstr "Dark Brown Plywood"

msgid "cplus_material_dusty_pink"
msgstr "Dusty Pink Plywood"

msgid "cplus_material_grey"
msgstr "Grey Plywood"

msgid "cplus_material_grey_darkgrey"
msgstr "Grey + Dark Grey"

msgid "cplus_material_grey_t02"
msgstr "Grey"

msgid "cplus_material_matteblack"
msgstr "Premium Matte Black"

msgid "cplus_material_moss_green"
msgstr "Moss Green"

msgid "cplus_material_oak"
msgstr "Oak"

msgid "cplus_material_orange"
msgstr "Terracotta"

msgid "cplus_material_pink"
msgstr "Dusty Pink Plywood"

msgid "cplus_material_red"
msgstr "Classic Red Plywood"

msgid "cplus_material_reisingers_pink"
msgstr "Reisinger Pink Unlimited Edition"

msgid "cplus_material_sage_green"
msgstr "Sage Green"

msgid "cplus_material_sand"
msgstr "Sand + Midnight Blue"

msgid "cplus_material_sand_mustardyellow"
msgstr "Sand + Mustard Yellow"

msgid "cplus_material_stone_grey"
msgstr "Stone Grey"

msgid "cplus_material_stone_grey_walnut"
msgstr "Stone Grey + Walnut Veneer"

msgid "cplus_material_walnut"
msgstr "Walnut"

msgid "cplus_material_white_t01"
msgstr "White Plywood"

msgid "cplus_material_white_t02"
msgstr "White"

msgid "cplus_material_yellow"
msgstr "Yellow Plywood"

msgid "cplus_mobile_columns_info"
msgstr "Tap a column to resize it using sliders."

msgid "cplus_mobile_dimensions"
msgstr "Show Dimensions (cm)"

msgid "cplus_mobile_sections"
msgstr "Segments"

msgid "cplus_mobile_sections_info"
msgstr "Tap a segment to customise it."

msgid "cplus_mobile_shelfview"
msgstr "Shelf View"

msgid "cplus_narrower"
msgstr "Narrower?"

msgid "cplus_new_slider_2"
msgstr "This cookie is used for user testing purposes."

msgid "cplus_oak"
msgstr "Oak Veneer"

msgid "cplus_pay_later"
msgstr "Buy now, pay later"

msgid "cplus_pay_later_tooltip"
msgstr "We work with Klarna to offer safe, straightforward payment plans for individuals. Get an invoice and simply pay later - easy!"

msgid "cplus_payment_details_button"
msgstr "View payment information"

msgid "cplus_payment_popup_button"
msgstr "OK"

msgid "cplus_payment_popup_header"
msgstr "Payment information"

msgid "cplus_pixel_legs_tooltip_info"
msgstr "Legs cannot be added to the Pixel style."

msgid "cplus_pixel_plinth_tooltip_info"
msgstr "Plinth cannot be added to the Pixel style."

msgid "cplus_plinth_option_1"
msgstr "Standard"

msgid "cplus_plinth_option_2"
msgstr "Plinth"

msgid "cplus_plinth_tooltip"
msgstr "Plinth not available for 24cm depth."

msgid "cplus_plinth_tooltip_2"
msgstr "Note: Plinth adds 10cm to the total shelf height."

msgid "cplus_save_for_later"
msgstr "Save my design"

msgid "cplus_save_for_later_other_region_mobile"
msgstr "Reserve Your Shipping Now"

msgid "cplus_save_other_region_header"
msgstr "Outside of Europe? Please check our FAQ for more shipping details."

msgid "cplus_save_other_region_header_mobile"
msgstr "Tylko is currently only available in Europe.<br> If you leave your email with us, we'll hold a dedicated shipping spot for you when we launch in your country!"

msgid "cplus_sidebar_close"
msgstr "Close all doors and drawers"

msgid "cplus_sidebar_dimensions_1"
msgstr "Show dimensions"

msgid "cplus_sidebar_dimensions_2"
msgstr "Hide dimensions"

msgid "cplus_sidebar_open"
msgstr "Open all doors and drawers"

msgid "cplus_sidebar_redo"
msgstr "Redo"

msgid "cplus_sidebar_reset"
msgstr "Reset"

msgid "cplus_sidebar_undo"
msgstr "Undo"

msgid "cplus_snackbar_wall_fastening"
msgstr "Safety first! Remember to fasten your shelf to the wall after assembly."

msgid "cplus_storage_columns"
msgstr "Storage columns"

msgid "cplus_style_charlie"
msgstr "Pixel "

msgid "cplus_style_frame"
msgstr "Frame"

msgid "cplus_style_gradient"
msgstr "Gradient"

msgid "cplus_style_grid"
msgstr "Grid"

msgid "cplus_style_name"
msgstr "Style"

msgid "cplus_style_pattern"
msgstr "Pattern"

msgid "cplus_style_porto"
msgstr "Mosaic"

msgid "cplus_taxes"
msgstr "Taxes included"

msgid "cplus_taxes_tooltip"
msgstr "No extra taxes, ever - the price you see is final."

msgid "cplus_taxes_tooltip_uk"
msgstr "All taxes are included in the price - with no extra hassle or headache."

msgid "cplus_taxes_uk"
msgstr "UK taxes included"

msgid "cplus_veneer_shelf_name"
msgstr "Tylko Original Classic in Veneer"

msgid "cplus_vertical_divider_label"
msgstr "Vertical divider"

msgid "cplus_vertical_divider_option_1"
msgstr "OFF"

msgid "cplus_vertical_divider_option_2"
msgstr "ON"

msgid "cplus_wider"
msgstr "Wider?"

msgid "cplus_width"
msgstr "Width"

msgid "criteo_feed_description"
msgstr "Free shipping & easy assembly"

msgid "cro_add_to_cart_new_ds_button"
msgstr "This cookie is used for user testing purposes."

msgid "cro_crow"
msgstr "This cookie is used for user testing purposes."

msgid "cro_crow_2"
msgstr "This cookie is used for user testing purposes."

msgid "cro_hero_de"
msgstr "This cookie is used for AB testing purposes."

msgid "cro_newcart"
msgstr "This cookie is used for user testing purposes."

msgid "cro_reviewgallery"
msgstr "This cookie is used for user testing purposes."

msgid "cro_shorter_pdp"
msgstr "This cookie has not yet been given a description. Our team is working to provide more information."

msgid "cro_shorter_pdp_v2"
msgstr "This cookie is used for AB testing purposes."

msgid "crow_add_to_cart"
msgstr "Add to cart"

msgid "crow_additional_storage_both"
msgstr "Both"

msgid "crow_additional_storage_bottom"
msgstr "Lower"

msgid "crow_additional_storage_disclaimer"
msgstr "For 9 and 10 rows of shelves, additional storage is unavailable"

msgid "crow_additional_storage_label"
msgstr "Additional storage"

msgid "crow_additional_storage_none"
msgstr "None"

msgid "crow_additional_storage_tooltip"
msgstr "Your design is too tall to add both upper and lower storage"

msgid "crow_additional_storage_upper"
msgstr "Upper"

msgid "crow_adjust_rows"
msgstr "Adjust rows"

msgid "crow_adjust_rows_header"
msgstr "Row Height"

msgid "crow_adjust_rows_max"
msgstr "Max"

msgid "crow_adjust_rows_none"
msgstr "None"

msgid "crow_adjust_rows_option1"
msgstr "Change row size to add doors."

msgid "crow_adjust_rows_option2"
msgstr "Drawers are only available for rows below 160cm. "

msgid "crow_adjust_rows_option3"
msgstr "Drawers are only available in 32cm and 40cm depth."

msgid "crow_adjust_rows_some"
msgstr "Some"

msgid "crow_ash"
msgstr "White Oak Veneer"

msgid "crow_backpanels"
msgstr "Back panels"

msgid "crow_backpanels_option1"
msgstr "OFF"

msgid "crow_backpanels_option2"
msgstr "ON"

msgid "crow_backpanels_tooltip_CTA"
msgstr "Try our sideboards!"

msgid "crow_backpanels_tooltip_body_1"
msgstr "Practical yet stylish: back panels make sure things don't fall behind your shelf, and help create a consistent look against colourful walls. If included, they decrease the depth of the compartments on all rows by 2cm. Please note that cable management holes are not included."

msgid "crow_backpanels_tooltip_body_2"
msgstr "All vinyl storage comes with back panels – it’s a match made in heaven. Looking for something without back panels?"

msgid "crow_backpanels_tooltip_header_1"
msgstr "Need help with back panels?"

msgid "crow_backpanels_tooltip_header_2"
msgstr "Vinyl storage is only available with back panels. "

msgid "crow_button_share"
msgstr "Share"

msgid "crow_color"
msgstr "Colour"

msgid "crow_custom_fees"
msgstr "Customs fees included"

msgid "crow_custom_fees_tooltip"
msgstr "No hidden fees or extra charges, either. Pick your perfect shelf and we take care of the rest. "

msgid "crow_density"
msgstr "Density"

msgid "crow_density_info"
msgstr "For wider shelves you can adjust the 'density' parameter. This enables you to change the number of compartments, as well as their proportions. It's this parameter that allows each style to be modified to create the unique design of your shelf. {button} for a density deep dive."

msgid "crow_density_info_link"
msgstr "Click here"

msgid "crow_density_tips_url"
msgstr "https://tips.tylko.com/en/articles/11-density-what-it-is-and-how-it-works"

msgid "crow_depth"
msgstr "Depth"

msgid "crow_depth_tooltip_body"
msgstr "A 24cm depth is perfect for narrow spots (please note, drawers are not an option with this depth). 32cm offers a good average storage depth, while 40cm is perfect for deeper items like vinyl records."

msgid "crow_depth_tooltip_header"
msgstr "Need help with depth?"

msgid "crow_discover"
msgstr "Discover more"

msgid "crow_drawers_pixel_disclaimer"
msgstr "Drawers are only available for compartments wider than 26cm."

msgid "crow_eco_tax"
msgstr "eco-fee"

msgid "crow_eco_tax_desc"
msgstr "Includes taxes and "

msgid "crow_features_header_1"
msgstr "Drawers"

msgid "crow_features_header_2"
msgstr "Doors"

msgid "crow_gallery_tooltip"
msgstr "See real-life shelves"

msgid "crow_height"
msgstr "Height"

msgid "crow_installment_payment"
msgstr "Pay in installments"

msgid "crow_installment_payment_tooltip"
msgstr "Pick a payment plan and pay for your shelf in easy installments."

msgid "crow_loader_copy"
msgstr "Hold tight, we're putting your shelf together..."

msgid "crow_material_ash"
msgstr "White Oak"

msgid "crow_material_black"
msgstr "Black Plywood"

msgid "crow_material_blue"
msgstr "Midnight Blue"

msgid "crow_material_blue_plywood"
msgstr "Blue Plywood"

msgid "crow_material_dark_brown"
msgstr "Dark Brown Plywood"

msgid "crow_material_dusty_pink"
msgstr "Dusty Pink Plywood"

msgid "crow_material_grey"
msgstr "Grey Plywood"

msgid "crow_material_grey_darkgrey"
msgstr "Grey + Dark Grey"

msgid "crow_material_grey_t02"
msgstr "Grey"

msgid "crow_material_matteblack"
msgstr "Premium Matte Black"

msgid "crow_material_moss_green"
msgstr "Moss Green"

msgid "crow_material_oak"
msgstr "Oak"

msgid "crow_material_orange"
msgstr "Terracotta"

msgid "crow_material_pink"
msgstr "Dusty Pink Plywood"

msgid "crow_material_red"
msgstr "Classic Red Plywood"

msgid "crow_material_reisingers_pink"
msgstr "Reisinger Pink Unlimited Edition"

msgid "crow_material_sage_green"
msgstr "Sage Green"

msgid "crow_material_sand"
msgstr "Sand + Midnight Blue"

msgid "crow_material_sand_mustardyellow"
msgstr "Sand + Mustard Yellow"

msgid "crow_material_stone_grey"
msgstr "Stone Grey"

msgid "crow_material_stone_grey_walnut"
msgstr "Stone Grey + Walnut Veneer"

msgid "crow_material_walnut"
msgstr "Walnut"

msgid "crow_material_white_t01"
msgstr "White Plywood"

msgid "crow_material_white_t02"
msgstr "White"

msgid "crow_material_yellow"
msgstr "Yellow Plywood"

msgid "crow_mobile_dimensions"
msgstr "Show Dimensions (cm)"

msgid "crow_mobile_shelfview"
msgstr "Shelf View"

msgid "crow_narrower"
msgstr "Narrower?"

msgid "crow_oak"
msgstr "Oak Veneer"

msgid "crow_pay_later"
msgstr "Buy now, pay later"

msgid "crow_pay_later_tooltip"
msgstr "We work with Klarna to offer safe, straightforward payment plans for individuals. Get an invoice and simply pay later - easy!"

msgid "crow_payment_details_button"
msgstr "View payment information"

msgid "crow_payment_popup_button"
msgstr "Ok"

msgid "crow_payment_popup_header"
msgstr "Payment information"

msgid "crow_pinterest_button"
msgstr "Save to Pinterest"

msgid "crow_save_for_later"
msgstr "Save my design"

msgid "crow_save_for_later_other_region_mobile"
msgstr "Reserve Your Shipping Now"

msgid "crow_save_other_region_header"
msgstr "Outside of Europe? Please check our FAQ for more shipping details."

msgid "crow_save_other_region_header_mobile"
msgstr "Tylko is currently only available in Europe.<br> If you leave your email with us, we'll hold a dedicated shipping spot for you when we launch in your country!"

msgid "crow_shipping"
msgstr "Ships in {0}-{1} weeks"

msgid "crow_shipping_tooltip"
msgstr "Each custom piece is made to order, as we carry no stock. Due to high demand, our estimated shipping times are currently longer than usual. "

msgid "crow_sidebar_dimensions_1"
msgstr "Show dimensions"

msgid "crow_sidebar_dimensions_2"
msgstr "Hide dimensions"

msgid "crow_style"
msgstr "Style"

msgid "crow_style_charlie"
msgstr "Pixel "

msgid "crow_style_dense_grid"
msgstr "Dense grid"

msgid "crow_style_option1"
msgstr "Grid"

msgid "crow_style_option2"
msgstr "Gradient"

msgid "crow_style_option3"
msgstr "Slant"

msgid "crow_style_option4"
msgstr "Pattern"

msgid "crow_style_option5"
msgstr "Standard Grid"

msgid "crow_style_option6"
msgstr "Dense Grid"

msgid "crow_style_porto"
msgstr "Mosaic"

msgid "crow_style_standard_grid"
msgstr "Standard grid"

msgid "crow_taxes"
msgstr "Taxes included"

msgid "crow_taxes_tooltip"
msgstr "No extra taxes, ever - the price you see is final. "

msgid "crow_taxes_tooltip_uk"
msgstr "All taxes are included in the price - with no extra hassle or headache."

msgid "crow_taxes_uk"
msgstr "UK taxes included"

msgid "crow_try_our_sideboards"
msgstr "Try our sideboards"

msgid "crow_veneer_shelf_name"
msgstr "Tylko Original Classic in Veneer "

msgid "crow_vinyl_design_new_sideboard"
msgstr "DESIGN A SIDEBOARD"

msgid "crow_vinyl_tooltip_body"
msgstr "A 40cm depth means perfect-fit storage for your vinyl record collection. The standard 32cm is a bit too shallow for LPs!"

msgid "crow_vinyl_tooltip_header"
msgstr "Vinyl Storage is available in 40cm depth only."

msgid "crow_wider"
msgstr "Wider?"

msgid "crow_width"
msgstr "Width"

msgid "csrftoken"
msgstr "This cookie is linked to the Django web development platform for Python. It is designed to help protect our website against a certain type of software attack on web forms."

msgid "cstep_bookcase_details"
msgstr "Whether you want a wall-to-wall library or a way to store everything from the smallest of paperbacks to heavyweight hardcovers, customise a bookcase that perfectly fits your growing collection. "

msgid "cstep_color_section_label"
msgstr "Pick the Colour"

msgid "cstep_mobile_save"
msgstr "Save"

msgid "cstep_pagination_row"
msgstr "Row"

msgid "cstep_rows_section_label"
msgstr "Adjust Rows"

msgid "cstep_select_row"
msgstr "Select row"

msgid "cstep_size_section_label"
msgstr "Choose Size"

msgid "cstep_storage_section_label"
msgstr "Additional Storage"

msgid "cstep_style_section_label"
msgstr "Personalize Style"

msgid "cstep_wall_storage_details"
msgstr "From family photos to holiday trinkets and favourite toys, design tailor-made wall storage to display all your treasured items, while keeping clutter (and all your secrets) behind closed doors."

msgid "cto_bundle"
msgstr "Send anonymous statistics for advertising use to Criteo."

msgid "custom_product_page_button_3_1"
msgstr "let us know"

msgid "custom_product_page_button_4_1"
msgstr "Go to checkout"

msgid "custom_product_page_heading_1"
msgstr "Dimensions"

msgid "custom_product_page_heading_2"
msgstr "Material"

msgid "custom_product_page_heading_3"
msgstr "Changes you ordered"

msgid "custom_product_page_paragraph_1_1"
msgstr "W"

msgid "custom_product_page_paragraph_1_2"
msgstr "H"

msgid "custom_product_page_paragraph_1_3"
msgstr "D"

msgid "custom_product_page_paragraph_3_1"
msgstr "Width increased to"

msgid "custom_product_page_paragraph_3_2"
msgstr "Depth increased to"

msgid "custom_product_page_paragraph_3_3"
msgstr "TV shelf added"

msgid "custom_product_page_paragraph_3_4"
msgstr "We want you to fall in love with your shelf. If you’d like to change something"

msgid "custom_product_page_paragraph_4_1"
msgstr "Looks great?"

msgid "custom_product_page_subparagraph_1_1"
msgstr "Includes VAT"

msgid "cvert_add_to_cart"
msgstr "ADD TO CART"

msgid "cvert_col"
msgstr "Columns"

msgid "cvert_color"
msgstr "Colour"

msgid "cvert_color_hint_label"
msgstr "Take a closer look "

msgid "cvert_columns"
msgstr "Columns"

msgid "cvert_columns_info"
msgstr "The option to add columns is only available for wardrobes with a minimum width of 156cm. "

msgid "cvert_columns_pagination"
msgstr "Column"

msgid "cvert_columns_pagination_1"
msgstr "Column"

msgid "cvert_columns_pagination_2"
msgstr "of"

msgid "cvert_crossbar_info"
msgstr "The option to add front-facing rail is only available for wardrobes with a minimum width of 53cm."

msgid "cvert_depth"
msgstr "Depth"

msgid "cvert_depth_hint_label"
msgstr "Need help picking depth?"

msgid "cvert_dooropening"
msgstr "Door direction"

msgid "cvert_doors"
msgstr "Doors"

msgid "cvert_doors_dir"
msgstr "Door direction"

msgid "cvert_doors_dir_option_1"
msgstr "Left"

msgid "cvert_doors_dir_option_2"
msgstr "Right"

msgid "cvert_doors_option_1"
msgstr "None"

msgid "cvert_doors_option_2"
msgstr "Partial "

msgid "cvert_doors_option_3"
msgstr "Full"

msgid "cvert_eco_tax"
msgstr "eco-fee"

msgid "cvert_eco_tax_desc"
msgstr "Includes taxes and "

msgid "cvert_exit_modal"
msgstr "Exterior"

msgid "cvert_gallery_tooltip"
msgstr "See real-life wardrobes"

msgid "cvert_height"
msgstr "Height"

msgid "cvert_material_black"
msgstr "Black"

msgid "cvert_material_black_plywood"
msgstr "Black Plywood"

msgid "cvert_material_clay_brown"
msgstr "Clay Brown"

msgid "cvert_material_grey"
msgstr "Grey"

msgid "cvert_material_grey_dark_grey"
msgstr "Grey + Dark Grey"

msgid "cvert_material_grey_plywood"
msgstr "Grey Plywood"

msgid "cvert_material_olive_green"
msgstr "Olive Green"

msgid "cvert_material_sand"
msgstr "Sand"

msgid "cvert_material_sand_midnight_blue"
msgstr "Sand + Midnight Blue"

msgid "cvert_material_sand_mustard_yellow"
msgstr "Sand + Mustard Yellow"

msgid "cvert_material_white"
msgstr "White"

msgid "cvert_material_white_plywood"
msgstr "White Plywood"

msgid "cvert_measuring_guide_snackbar_body"
msgstr "Need help choosing the perfect fit for your space? Use our handy"

msgid "cvert_measuring_guide_snackbar_button"
msgstr "measuring guide"

msgid "cvert_mobile_depth_tooltip_title"
msgstr "Need help picking depth?"

msgid "cvert_mobile_height_hint_body"
msgstr "Need an even taller wardrobe?"

msgid "cvert_mobile_height_hint_button"
msgstr "Shop the Tone Wardrobe"

msgid "cvert_mobile_sections_info"
msgstr "Tap a column to start customising."

msgid "cvert_payment_details_button"
msgstr "VIEW PAYMENT INFORMATION"

msgid "cvert_save_for_later"
msgstr "Save my design"

msgid "cvert_sections"
msgstr "Layout"

msgid "cvert_tab_1"
msgstr "Form"

msgid "cvert_tab_2"
msgstr "Function"

msgid "cvert_taxes"
msgstr "Taxes included"

msgid "cvert_taxes_uk"
msgstr "UK taxes included"

msgid "cvert_tooltip_depth_42cm"
msgstr "42cm of depth offers a very comfortable amount of storage, without taking up too much of your narrow space. To optimise functionality, please note that wardrobes with this depth come with a crossbar, instead of a standard hanger."

msgid "cvert_tooltip_depth_60cm"
msgstr "60cm of depth offers a very comfortable amount of space to hang clothing using a standard clothing rail. "

msgid "cvert_tooltip_depth_CTA"
msgstr "Choose this depth"

msgid "cvert_tooltip_height"
msgstr "The Edge Wardrobe is available in 4 heights, ranging from 138cm up to 238cm. Whatever the chosen height, you will find a great variety of components, including clothing rails, drawers and more.\n\nIf you're looking for an even taller wardrobe of up to 3,6m, check out our luxury product line: The Tone. "

msgid "cvert_tooltip_height_CTA"
msgstr "Design your Tone Wardrobe"

msgid "cvert_tooltip_height_url"
msgstr "https://tylko.com/furniture/wardrobe/92259,w,tall-wide-cashmere-beige-6-door-wardrobe-with-internal-drawers-and-rail-258x260x53cm"

msgid "cvert_width"
msgstr "Width"

msgid "cwatcol__depth_tooltip_copy"
msgstr "With a space-conscious 43cm depth and the ability to add drawers, this size offers comfortable and convenient storage. Please note this depth does not support the addition of a hanging rail.   With a depth of 53cm, this size offers spacious storage, the ability to add drawers, as well as the option of a hanging clothing rail for all pieces 109 cm in height and above. "

msgid "cwatcol_add_to_cart"
msgstr "Add to cart"

msgid "cwatcol_col_width"
msgstr "Column width"

msgid "cwatcol_col_width_alert"
msgstr "Adjusting column width is not possible for a (xxxcm) wide Cabinet."

msgid "cwatcol_col_width_war_width"
msgstr "Cabinet width"

msgid "cwatcol_colour"
msgstr "Colour"

msgid "cwatcol_colour_1"
msgstr "White"

msgid "cwatcol_colour_2"
msgstr "Cashmere Beige"

msgid "cwatcol_colour_3"
msgstr "Graphite Grey"

msgid "cwatcol_colour_4"
msgstr "Cashmere + Antique Pink"

msgid "cwatcol_columns_pagination"
msgstr "Column"

msgid "cwatcol_columns_pagination_2"
msgstr "of"

msgid "cwatcol_custom_fees"
msgstr "Customs fees included"

msgid "cwatcol_custom_fees_tooltip"
msgstr "No hidden fees or extra charges, either. Pick your perfect shelf and we take care of the rest. "

msgid "cwatcol_depth"
msgstr "Depth"

msgid "cwatcol_dimensions_in_cm"
msgstr "All dimensions are shown in centimetres (cm)"

msgid "cwatcol_discover"
msgstr "Discover more"

msgid "cwatcol_dooropening"
msgstr "Door Opening "

msgid "cwatcol_dooropening_left"
msgstr "​Left"

msgid "cwatcol_dooropening_right"
msgstr "​Right"

msgid "cwatcol_drawers_features_mobile"
msgstr "What's the difference?"

msgid "cwatcol_height"
msgstr "Height"

msgid "cwatcol_installment_payment"
msgstr "Pay in installments"

msgid "cwatcol_installment_payment_tooltip"
msgstr "Pick a payment plan and pay for your shelf in easy installments."

msgid "cwatcol_learn_more"
msgstr "Learn more"

msgid "cwatcol_mobile_layout"
msgstr "Layout"

msgid "cwatcol_mobile_sections"
msgstr "Interior"

msgid "cwatcol_mobile_sections_info"
msgstr "Tap a column to start customising."

msgid "cwatcol_narrower"
msgstr "Narrower?"

msgid "cwatcol_pay_later"
msgstr "Buy now, pay later"

msgid "cwatcol_pay_later_tooltip"
msgstr "We work with Klarna to offer safe, straightforward payment plans for individuals. Get an invoice and simply pay later - easy!"

msgid "cwatcol_payment_details_button"
msgstr "View payment information"

msgid "cwatcol_payment_popup_button"
msgstr "Ok"

msgid "cwatcol_payment_popup_header"
msgstr "Payment information"

msgid "cwatcol_printscreen"
msgstr "Snapshot"

msgid "cwatcol_product_name"
msgstr "Tone Cabinet  "

msgid "cwatcol_rail_alert"
msgstr "Hanging rails are available for 53cm depth Cabinets only."

msgid "cwatcol_rail_snackbar"
msgstr "A hanging rail is not available for this 43cm deep Cabinet. It has been replaced with shelf inserts."

msgid "cwatcol_rail_snackbar_ok"
msgstr "Ok, continue"

msgid "cwatcol_rail_snackbar_undo"
msgstr "Undo change"

msgid "cwatcol_save_for_later"
msgstr "Save for later"

msgid "cwatcol_shipping"
msgstr "Ships in {0}-{1} weeks"

msgid "cwatcol_shipping\n"
msgstr "Ships in {0}-{1} weeks\n"

msgid "cwatcol_sidebar_close"
msgstr "Close all doors and drawers"

msgid "cwatcol_sidebar_dimensions_1"
msgstr "Show dimensions"

msgid "cwatcol_sidebar_dimensions_2"
msgstr "Hide dimensions"

msgid "cwatcol_sidebar_open"
msgstr "Open all doors and drawers"

msgid "cwatcol_sidebar_redo"
msgstr "Redo"

msgid "cwatcol_sidebar_reset"
msgstr "Reset"

msgid "cwatcol_sidebar_undo"
msgstr "Undo"

msgid "cwatcol_tab_1"
msgstr "Exterior"

msgid "cwatcol_tab_2"
msgstr "Interior"

msgid "cwatcol_taxes"
msgstr "Taxes included"

msgid "cwatcol_taxes_tooltip"
msgstr "No extra taxes, ever - the price you see is final. "

msgid "cwatcol_universal_loader_copy"
msgstr "Loading..."

msgid "cwatcol_wider"
msgstr "Wider?"

msgid "cwatcol_width"
msgstr "Width"

msgid "cwatwarPdp_bannerlp_body"
msgstr "Organise your clothes the smart way - with a perfect-fit closet."

msgid "cwatwarPdp_bannerlp_cta"
msgstr "Discover More"

msgid "cwatwarPdp_bannerlp_header"
msgstr "The new Tylko Wardrobe"

msgid "cwatwarPdp_bannerlp_secondary_cta"
msgstr "Or Get In Touch +xx-xxxx-xxxxx"

msgid "cwatwarPdp_delivery_header_1"
msgstr "Free delivery"

msgid "cwatwarPdp_delivery_header_2"
msgstr "anywhere in the EU"

msgid "cwatwarPdp_depth_body"
msgstr "The Tone Wardrobe comes in two smart depths to help make the most of your space without sacrificing function: 53cm for slimmer storage, or 63cm for full-depth comfort.  "

msgid "cwatwarPdp_depth_headline"
msgstr "Two depth options, zero compromise."

msgid "cwatwarPdp_features_body_1"
msgstr "Colour-matched aluminium handles. Matte and textured for a tactile treat. "

msgid "cwatwarPdp_features_body_2"
msgstr "Sturdy, soft-close hinged doors for full access to wardrobe's interior."

msgid "cwatwarPdp_features_body_3"
msgstr "Drawers with full-width construction and full-extension runners for the best use of space."

msgid "cwatwarPdp_features_body_4"
msgstr "A durable aluminium bar at a comfortable height for ergonomic ease."

msgid "cwatwarPdp_features_body_5"
msgstr "Extra space for bulkier items, such as suitcases or blankets, with HEIGHT+."

msgid "cwatwarPdp_features_header"
msgstr "Discover the details that make the difference. "

msgid "cwatwarPdp_features_headline_1"
msgstr "Handles"

msgid "cwatwarPdp_features_headline_2"
msgstr "Doors"

msgid "cwatwarPdp_features_headline_3"
msgstr "Drawers"

msgid "cwatwarPdp_features_headline_4"
msgstr "Hanging Bar"

msgid "cwatwarPdp_features_headline_5"
msgstr "Overhead Storage"

msgid "cwatwarPdp_metatags_description_1"
msgstr "The smart way to organise your clothes."

msgid "cwatwarPdp_metatags_title_1"
msgstr "The Tylko Wardrobe"

msgid "cwatwarPdp_rating_header_1"
msgstr "Customer rating"

msgid "cwatwarPdp_rating_header_2_1"
msgstr "based on XXX"

msgid "cwatwarPdp_rating_header_2_2"
msgstr "reviews"

msgid "cwatwarPdp_returns_header_1"
msgstr "100 days to fall in love"

msgid "cwatwarPdp_returns_header_2"
msgstr "or return it for free"

msgid "cwatwarPdp_segments_description_1"
msgstr "A full-length hanging segment that's ideal for oversized clothing."

msgid "cwatwarPdp_segments_description_10"
msgstr "A row of shelves for stacking, plus two drawers to neatly store the smaller items. "

msgid "cwatwarPdp_segments_description_11"
msgstr "Two stacking sections and four drawers - perfect for separating by season or specific use."

msgid "cwatwarPdp_segments_description_12"
msgstr "Six drawers to keep things tidily tucked, plus handy shelves for extra space to stash. "

msgid "cwatwarPdp_segments_description_2"
msgstr "Two rows of hanging racks - perfect for keeping shorter-length clothing in order."

msgid "cwatwarPdp_segments_description_3"
msgstr "Space to hang mid-length clothes, with room top and bottom for bigger items. "

msgid "cwatwarPdp_segments_description_4"
msgstr "A dedicated space for hanging mid-length items, plus two drawers for concealed storage."

msgid "cwatwarPdp_segments_description_5"
msgstr "Hanging space for shorter clothes, with open shelves for folding, stacking and storing."

msgid "cwatwarPdp_segments_description_6"
msgstr "The do-it-all segment with a hanging rack, two low drawers, and space to stack."

msgid "cwatwarPdp_segments_description_7"
msgstr "A short hanging section plus four drawers to keep things tidy and tucked away."

msgid "cwatwarPdp_segments_description_8"
msgstr "A row of regular shelves - perfect for boxes, bulkier items and organising clothes in stacks."

msgid "cwatwarPdp_segments_description_9"
msgstr "A row of tighter shelves for more specific sorting and smaller organisation."

msgid "cwatwarPdp_segments_header"
msgstr "12 smart segments for easy organising"

msgid "cwatwarPdp_segments_headline_1"
msgstr "Extra Long Hang"

msgid "cwatwarPdp_segments_headline_10"
msgstr "Stack + 2 Drawer"

msgid "cwatwarPdp_segments_headline_11"
msgstr "Stack + 4 Drawer"

msgid "cwatwarPdp_segments_headline_12"
msgstr "Stack + 6 Drawer"

msgid "cwatwarPdp_segments_headline_2"
msgstr "Double Hang"

msgid "cwatwarPdp_segments_headline_3"
msgstr "Long Hang"

msgid "cwatwarPdp_segments_headline_4"
msgstr "Long Hang + 2 Drawer"

msgid "cwatwarPdp_segments_headline_5"
msgstr "Hang"

msgid "cwatwarPdp_segments_headline_6"
msgstr "Hang + 2 Drawer"

msgid "cwatwarPdp_segments_headline_7"
msgstr "Hang + 4 Drawer"

msgid "cwatwarPdp_segments_headline_8"
msgstr "Stack"

msgid "cwatwarPdp_segments_headline_9"
msgstr "Dense Stack"

msgid "cwatwarPdp_specs_body_1_1"
msgstr "Width"

msgid "cwatwarPdp_specs_body_1_2"
msgstr "Height"

msgid "cwatwarPdp_specs_body_1_3"
msgstr "(Including standard feet)"

msgid "cwatwarPdp_specs_body_1_3_1"
msgstr "Please ensure shelf is 10cm less than your ceiling height to allow room for assembly."

msgid "cwatwarPdp_specs_body_1_4"
msgstr "Depth"

msgid "cwatwarPdp_specs_body_1_5"
msgstr "Compartment max load"

msgid "cwatwarPdp_specs_body_1_6"
msgstr "Shelf max load"

msgid "cwatwarPdp_specs_body_2_1"
msgstr "Doors"

msgid "cwatwarPdp_specs_body_2_10"
msgstr "- Conceals assembly elements for a sleek and seamless finish - Built from durable aluminium - Powder-coated to colour match"

msgid "cwatwarPdp_specs_body_2_2"
msgstr "- Built from slim yet sturdy 12mm particle board - Weighted to self-close - Colour-matched aluminum handles with ergonomic design"

msgid "cwatwarPdp_specs_body_2_3"
msgstr "Drawers"

msgid "cwatwarPdp_specs_body_2_4"
msgstr "- Natural plywood core - Full-extension, soft-slose runners - Lowered front panels for easy access"

msgid "cwatwarPdp_specs_body_2_5"
msgstr "Overhead Compartment"

msgid "cwatwarPdp_specs_body_2_6"
msgstr "- Same material construction as main wardrobe - Push-to-open doors - Space managing insert shelves"

msgid "cwatwarPdp_specs_body_2_7"
msgstr "Hanging Rack"

msgid "cwatwarPdp_specs_body_2_8"
msgstr "- Ergonomic height across every segment for comfortable use - Aluminum core for added strength - Powder-coated and colour-matched "

msgid "cwatwarPdp_specs_body_2_9"
msgstr "Wardrobe Base"

msgid "cwatwarPdp_specs_body_3_1"
msgstr "Construction Material"

msgid "cwatwarPdp_specs_body_3_2"
msgstr "The Tylko Wardrobe is made of durable high-density particle board with a laminated top finish. The Wardrobe core elements are constructed from 18mm particle board, while the doors and frames are made from a slightly thinner 12mm board for a streamlined look with no compromise on stability or strength. The handles and hanging rack are fabricated in durable aluminum that's poweder coated and colour-matched to perfectly suit your storage."

msgid "cwatwarPdp_specs_body_3_3"
msgstr "Care Instructions"

msgid "cwatwarPdp_specs_body_3_4"
msgstr "Use gentle furniture cleaners and a dry towel to keep your Wardrobe looking great."

msgid "cwatwarPdp_specs_header"
msgstr "PRODUCT DETAILS"

msgid "cwatwarPdp_specs_headline_1"
msgstr "Your Tylko Wardrobe"

msgid "cwatwarPdp_specs_headline_2"
msgstr "Features"

msgid "cwatwarPdp_specs_headline_3"
msgstr "Materials & Quality"

msgid "cwatwarPdp_usp_body_1"
msgstr "Get it delivered direct to your door in managable flat-pack boxes – for free."

msgid "cwatwarPdp_usp_body_2"
msgstr "Save time with our expert at-home assembly to get your wardrobe built."

msgid "cwatwarPdp_usp_body_3"
msgstr "Not happy for any reason? We'll pick up your Wardrobe for free with a full refund - no questions asked. "

msgid "cwatwarPdp_usp_header"
msgstr "The Tylko Guarantee"

msgid "cwatwarPdp_usp_headline_1"
msgstr "Free home delivery"

msgid "cwatwarPdp_usp_headline_2"
msgstr "Free assembly service"

msgid "cwatwarPdp_usp_headline_3"
msgstr "100-day free returns"

msgid "cwatwar__depth_tooltip_copy"
msgstr "A 63cm depth allows you to store much more on shelves and in drawers. It's an ideal choice for hanging all of your clothes and accessories.  A 53cm depth is more compact, yet it still lets you hang all your clothes properly. This depth is ideal if you're short on space or want to put your wardrobe in a tight corner."

msgid "cwatwar_add_to_cart"
msgstr "Add to cart"

msgid "cwatwar_button_share"
msgstr "Share"

msgid "cwatwar_col_width"
msgstr "Column width"

msgid "cwatwar_col_width_door_width"
msgstr "The width of doors remain equal."

msgid "cwatwar_col_width_narrow"
msgstr "Slim"

msgid "cwatwar_col_width_regular"
msgstr "Standard"

msgid "cwatwar_col_width_unavailable_1"
msgstr "Changing column width is not possible with a wardrobe this wide ("

msgid "cwatwar_col_width_unavailable_2"
msgstr ")."

msgid "cwatwar_col_width_war_width"
msgstr "Wardrobe width"

msgid "cwatwar_col_width_wide"
msgstr "Wide"

msgid "cwatwar_color"
msgstr "Colour"

msgid "cwatwar_color_antique_pink"
msgstr "Antique Pink"

msgid "cwatwar_color_cashmere_beige"
msgstr "Cashmere Beige"

msgid "cwatwar_color_exterior"
msgstr "Exterior"

msgid "cwatwar_color_graphite_grey"
msgstr "Graphite Grey"

msgid "cwatwar_color_interior"
msgstr "Interior"

msgid "cwatwar_color_misty_blue"
msgstr "Misty Blue"

msgid "cwatwar_color_sage_green"
msgstr "Sage Green"

msgid "cwatwar_color_stone_grey"
msgstr "Stone Grey"

msgid "cwatwar_color_white"
msgstr "White"

msgid "cwatwar_colour_1"
msgstr "White"

msgid "cwatwar_colour_2"
msgstr "Cashmere Beige"

msgid "cwatwar_colour_3"
msgstr "Graphite Grey"

msgid "cwatwar_colour_4"
msgstr "Cashmere Beige + Antique Pink"

msgid "cwatwar_columns_pagination_1"
msgstr "Column"

msgid "cwatwar_columns_pagination_2"
msgstr "of"

msgid "cwatwar_custom_fees"
msgstr "Customs fees included"

msgid "cwatwar_custom_fees_tooltip"
msgstr "No hidden fees or extra charges, either. Pick your perfect shelf and we take care of the rest. "

msgid "cwatwar_depth"
msgstr "Depth"

msgid "cwatwar_depth_crossbar_info"
msgstr "Wardobes with <span class='text-bold'>53cm</span> depth or less have a front-facing rail."

msgid "cwatwar_dimensions_in_cm"
msgstr "All dimensions are shown in centimetres (cm)"

msgid "cwatwar_discover"
msgstr "Discover more"

msgid "cwatwar_doors"
msgstr "Door Opening  "

msgid "cwatwar_doors_hint"
msgstr "In some cases, changing a door's opening direction will automatically adjust door handle placement. This is to avoid collisions."

msgid "cwatwar_doors_option_1"
msgstr "​Left"

msgid "cwatwar_doors_option_2"
msgstr "​Right"

msgid "cwatwar_drawers_external"
msgstr "External"

msgid "cwatwar_drawers_external_body"
msgstr "External drawers with a push-to-open system will be added to the lower part of your wardrobe, giving you extra storage with quick and easy access."

msgid "cwatwar_drawers_external_cta"
msgstr "Switch to external drawers"

msgid "cwatwar_drawers_external_tooltip"
msgstr "External drawers"

msgid "cwatwar_drawers_feature"
msgstr "Drawers"

msgid "cwatwar_drawers_features_mobile"
msgstr "What's the difference?"

msgid "cwatwar_drawers_info"
msgstr "This changes your wardrobe layout"

msgid "cwatwar_drawers_internal"
msgstr "Internal"

msgid "cwatwar_drawers_internal_body"
msgstr "All drawers will be kept behind closed doors, allowing for a more harmonious design. "

msgid "cwatwar_drawers_internal_cta"
msgstr "Add internal drawers"

msgid "cwatwar_drawers_internal_tooltip"
msgstr "Internal drawers"

msgid "cwatwar_eco_tax"
msgstr "eco-fee"

msgid "cwatwar_eco_tax_desc"
msgstr "Includes taxes and "

msgid "cwatwar_email_popup_checkbox_error"
msgstr "Please tick the box to agree."

msgid "cwatwar_email_popup_textfield_error"
msgstr "Please enter a valid email address."

msgid "cwatwar_extra_height_1"
msgstr "Height+"

msgid "cwatwar_extra_height_2"
msgstr "Standard"

msgid "cwatwar_extra_height_warning_1"
msgstr "This wardrobe requires"

msgid "cwatwar_extra_height_warning_2"
msgstr "of ceiling height."

msgid "cwatwar_find_out_more"
msgstr "Find out more"

msgid "cwatwar_gallery_tooltip"
msgstr "See real-life shelves"

msgid "cwatwar_graphite_rose"
msgstr "Graphite Grey + Antique Pink"

msgid "cwatwar_hanging_option_availability"
msgstr "The hanging option is only available for columns wider than 55cm."

msgid "cwatwar_height"
msgstr "Height"

msgid "cwatwar_height_ceiling_info"
msgstr "This wardrobe requires a ceiling height of 248cm."

msgid "cwatwar_installment_payment"
msgstr "Pay in installments"

msgid "cwatwar_installment_payment_tooltip"
msgstr "Pick a payment plan and pay for your shelf in easy installments."

msgid "cwatwar_lighting_pill_internal"
msgstr "Internal"

msgid "cwatwar_lighting_pill_without"
msgstr "None"

msgid "cwatwar_lighting_snackbar_body"
msgstr "You'll need 4cm behind the wardrobe to connect the plug to your power socket."

msgid "cwatwar_lighting_snackbar_button"
msgstr "See details "

msgid "cwatwar_lighting_space_requirement_info"
msgstr "You'll need a total depth of 58cm from the wall to the front of the wardrobe."

msgid "cwatwar_loader_copy"
msgstr "Hold tight, we're bringing your wardrobe to life..."

msgid "cwatwar_mobile_col_width"
msgstr "Column width"

msgid "cwatwar_mobile_col_width_hint"
msgstr "About fixed widths"

msgid "cwatwar_mobile_depth_hint"
msgstr "What's the difference?"

msgid "cwatwar_mobile_depth_tooltip_title"
msgstr "Need help picking depth?"

msgid "cwatwar_mobile_exterior"
msgstr "Exterior"

msgid "cwatwar_mobile_layout"
msgstr "Layout"

msgid "cwatwar_mobile_sections"
msgstr "Interior"

msgid "cwatwar_mobile_sections_info"
msgstr "Tap a column to start customising."

msgid "cwatwar_narrower"
msgstr "Narrower?"

msgid "cwatwar_pay_later"
msgstr "Buy now, pay later"

msgid "cwatwar_pay_later_tooltip"
msgstr "We work with Klarna to offer safe, straightforward payment plans for individuals. Get an invoice and simply pay later - easy!"

msgid "cwatwar_payment_details_button"
msgstr "View payment information"

msgid "cwatwar_payment_popup_button"
msgstr "Ok"

msgid "cwatwar_payment_popup_header"
msgstr "Payment information"

msgid "cwatwar_printscreen"
msgstr "Snapshot"

msgid "cwatwar_save_for_later"
msgstr "Save my design"

msgid "cwatwar_segment_drawers"
msgstr "Tap the interior tab and select a segment to customise with drawers."

msgid "cwatwar_shipping"
msgstr "Ships in {0}-{1} weeks"

msgid "cwatwar_sidebar_close"
msgstr "Close all doors and drawers"

msgid "cwatwar_sidebar_dimensions_1"
msgstr "Show dimensions"

msgid "cwatwar_sidebar_dimensions_2"
msgstr "Hide dimensions"

msgid "cwatwar_sidebar_open"
msgstr "Open all doors and drawers"

msgid "cwatwar_sidebar_redo"
msgstr "Redo"

msgid "cwatwar_sidebar_reset"
msgstr "Reset"

msgid "cwatwar_sidebar_undo"
msgstr "Undo"

msgid "cwatwar_sidecart_colour"
msgstr "Colour:"

msgid "cwatwar_sidecart_name"
msgstr "Tylko Wardrobe"

msgid "cwatwar_sidecart_white"
msgstr "White"

msgid "cwatwar_soldout_cart_edit_active"
msgstr "You can edit your design until"

msgid "cwatwar_soldout_cart_invitation_active"
msgstr "You can place your order until "

msgid "cwatwar_soldout_configurator_CTA"
msgstr "Save design and get notified "

msgid "cwatwar_soldout_configurator_body"
msgstr "Due to popular demand, this product is not available to order right now. Don't worry, we're handling it. Save your design and we'll notify you as soon as we're able to create your custom wardrobe."

msgid "cwatwar_soldout_configurator_headline"
msgstr "Currently sold out. Join our guest list to get notified. "

msgid "cwatwar_soldout_configurator_invitation_active"
msgstr "You can place your order until "

msgid "cwatwar_soldout_configurator_invitation_active_tooltip"
msgstr "We'd love to be able to produce all orders at once, but this isn't possible because every wardrobe is custom-made. In order to maintain our quality consistent, our invitation to shop the wardrobe is valid for a limited time."

msgid "cwatwar_soldout_configurator_popup_CTA"
msgstr "Save design and get notified "

msgid "cwatwar_soldout_configurator_popup_body"
msgstr "All of our products are custom-made, therefore we don’t keep ready-made items in stock. To maintain a consistent quality, we’re currently unable to accept more orders. Please enter your email to save your design and we’ll get in touch as soon as your wardrobe can be produced."

msgid "cwatwar_soldout_configurator_popup_checkbox_1"
msgstr "When do you need it by?"

msgid "cwatwar_soldout_configurator_popup_checkbox_2"
msgstr "Optional"

msgid "cwatwar_soldout_configurator_popup_checkbox_3"
msgstr "Select time"

msgid "cwatwar_soldout_configurator_popup_checkbox_4"
msgstr "In a month"

msgid "cwatwar_soldout_configurator_popup_checkbox_5"
msgstr "In 2-4 months"

msgid "cwatwar_soldout_configurator_popup_checkbox_6"
msgstr "In over 4 months"

msgid "cwatwar_soldout_configurator_popup_confirmation_body"
msgstr "Great, thanks for signing up. We'll notify you when it's possible to order the product  — stay tuned!  You'll also receive an email with a link to your design. "

msgid "cwatwar_soldout_configurator_popup_confirmation_headline"
msgstr "You're on the waitlist "

msgid "cwatwar_soldout_configurator_popup_consent2"
msgstr "I agree to receiving emails about my saved design, as well as the occasional piece of carefully curated content from Tylko. The data controller is Custom Sp.z o.o. Your data will be processed in order to allow you to finish and save your design. The legal basis for the processing is your consent (art. 6(1)(a)(f) of the GDPR). <a href=\"https://tylko.com/privacy_policy/\">See more.</a>"

msgid "cwatwar_soldout_configurator_popup_email"
msgstr "Email "

msgid "cwatwar_soldout_configurator_popup_email2"
msgstr "Enter here"

msgid "cwatwar_soldout_configurator_popup_error_1"
msgstr "Please enter your email"

msgid "cwatwar_soldout_configurator_popup_error_2"
msgstr "Please enter a valid email. "

msgid "cwatwar_soldout_configurator_popup_error_3"
msgstr "To continue, please select the checkbox."

msgid "cwatwar_soldout_configurator_popup_headline"
msgstr "Get notified when available"

msgid "cwatwar_soldout_configurator_snackbar_body"
msgstr "Good news: we've automatically added the discount we offered you when you joined the waitlist. "

msgid "cwatwar_soldout_configurator_snackbar_promo"
msgstr "Promo"

msgid "cwatwar_soldout_popup_invitation_body"
msgstr "Oops! It looks like you're already on the waitlist, and have an active invitation to complete your order. You can still customise your previous design until you're completely happy with it.   "

msgid "cwatwar_soldout_popup_invitation_cta"
msgstr "Use your invitation"

msgid "cwatwar_soldout_popup_invitation_headline"
msgstr "You have an active invitation"

msgid "cwatwar_soldout_popup_na_CTA"
msgstr "Get notified"

msgid "cwatwar_soldout_popup_na_body"
msgstr "The Tone Wardrobe will soon be launching in <span class='text-capitalize'>%(country)s</span>. Leave us your email and we'll let you know as soon as it is available to order. "

msgid "cwatwar_soldout_popup_na_body_v2"
msgstr "Please leave us your email and we’ll let you know as soon as the Tone Wardrobe is available to order outside of Europe."

msgid "cwatwar_soldout_popup_na_confirmation_body"
msgstr "Great, thanks for signing up. You will now get notified when the wardrobe is available to order from your country."

msgid "cwatwar_soldout_popup_na_confirmation_header"
msgstr "You're on the list"

msgid "cwatwar_soldout_popup_na_consent"
msgstr "I agree to receive valuable content from Tylko in the form of occasional emails. The data controller is Custom Sp.z o.o. Your data will be processed in order to keep you informed about our products and services. The legal basis for the processing is your consent (art. 6(1)(a) of the GDPR)."

msgid "cwatwar_soldout_popup_na_email"
msgstr "Email "

msgid "cwatwar_soldout_popup_na_email_2"
msgstr "Enter here"

msgid "cwatwar_soldout_popup_na_error_1"
msgstr "Please enter your email"

msgid "cwatwar_soldout_popup_na_error_2"
msgstr "Please enter a valid email. "

msgid "cwatwar_soldout_popup_na_error_3"
msgstr "To continue, please select the checkbox."

msgid "cwatwar_soldout_popup_na_headline"
msgstr "Join the waitlist "

msgid "cwatwar_soldout_popup_noinvitation_body"
msgstr "A purchase has already been made with this invitation, or it has expired. Still keen to buy the Tone Wardrobe? Save your design again and we'll let you know we're ready to produce it for you. "

msgid "cwatwar_soldout_popup_noinvitation_cta"
msgstr "Go to your design"

msgid "cwatwar_soldout_popup_noinvitation_headline"
msgstr "The invitation is no longer valid"

msgid "cwatwar_soldout_wishlist_cta"
msgstr "Edit your design"

msgid "cwatwar_tab_1"
msgstr "Form"

msgid "cwatwar_tab_2"
msgstr "Function"

msgid "cwatwar_taxes"
msgstr "Taxes included"

msgid "cwatwar_taxes_tooltip"
msgstr "No extra taxes, ever - the price you see is final. "

msgid "cwatwar_taxes_tooltip_uk"
msgstr "All taxes are included in the price - with no extra hassle or headache."

msgid "cwatwar_taxes_uk"
msgstr "UK taxes included"

msgid "cwatwar_universal_loader_copy"
msgstr "Loading..."

msgid "cwatwar_waitlist_CTA"
msgstr "Join the waitlist"

msgid "cwatwar_waitlist_description"
msgstr "The Tylko Wardrobe will be available to shop from your location soon. Join our waiting list and we'll keep you updated."

msgid "cwatwar_white_rose"
msgstr "White + Antique Pink"

msgid "cwatwar_wider"
msgstr "Wider?"

msgid "cwatwar_width"
msgstr "Width"

#, python-format
msgid "delivery_dynamic_range_%(minRange)s_%(maxRange)s"
msgstr "Ships in %(minRange)s-%(maxRange)s weeks"

msgid "delivery_dynamic_text_with_br"
msgstr "Ships in {0}-{1} weeks"

msgid "delivery_dynamic_text_with_tick"
msgstr "Ships in {0}-{1} <br>weeks"

msgid "delivery_timeslots_CTA_1"
msgstr "Add another time slot"

msgid "delivery_timeslots_CTA_2"
msgstr "Confirm Delivery Details"

msgid "delivery_timeslots_body_1"
msgstr "Select delivery slot suitable for you. We'll confirm the most efficient date with our transportation partners and send you a confirmation email 24 hours before your shelf ships. | Select delivery slots suitable for you. We'll confirm the most efficient date with our transportation partners and send you a confirmation email 24 hours before your shelf ships."

msgid "delivery_timeslots_body_2"
msgstr "We aim to reduce our carbon footprint, so your shelf will ship alongside others in a nice, full truck. This is why same-day delivery is not available – waiting a little longer is better for the planet!"

msgid "delivery_timeslots_body_3"
msgstr "Date and time for delivery"

msgid "delivery_timeslots_body_4"
msgstr "Please let us know of any important delivery details: tricky parking, top floors with no elevator, or narrow hallways for example."

msgid "delivery_timeslots_body_5"
msgstr "Special Notes"

msgid "delivery_timeslots_body_subheader_1"
msgstr "Step 1: Tell us your availability"

msgid "delivery_timeslots_body_subheader_2"
msgstr "Step 2: Tell us more about your delivery location"

msgid "delivery_timeslots_body_subheader_2_1"
msgstr "(optional)"

msgid "delivery_timeslots_confirmation_body"
msgstr "Note: If you have selected a time slot longer than 3 hours, our courier will contact you at least 3 hours prior to your delivery. Otherwise, you'll only hear from them if there is a problem."

msgid "delivery_timeslots_confirmation_body_1"
msgstr "We'll be in touch at least 24 hours before the selected date to notify you about your delivery. You have until {date} to change your preferred date. Just get in touch if you have any questions!"

msgid "delivery_timeslots_confirmation_body_3"
msgstr "We'll be in touch at least 24 hours before the selected date to notify you about your delivery. Unfortunately, it's no longer possible to change your delivery dates. If you must change the details, please get in touch and we'll do our best to accommodate you."

msgid "delivery_timeslots_confirmation_body_4"
msgstr "Order number:"

msgid "delivery_timeslots_confirmation_body_5"
msgstr "Boxes for Type/Category/Colour/Size"

msgid "delivery_timeslots_confirmation_body_5_2"
msgstr "Boxes for Type/Category/Colour/Size"

msgid "delivery_timeslots_confirmation_body_5_3"
msgstr "Box dimensions for your {dimensions} shelf:"

msgid "delivery_timeslots_confirmation_body_5_4"
msgstr "{size} and {weight}"

msgid "delivery_timeslots_confirmation_body_6"
msgstr "Assembly Instructions"

msgid "delivery_timeslots_confirmation_body_7"
msgstr "Don't worry – they'll also be included with your delivery."

msgid "delivery_timeslots_confirmation_headline"
msgstr "Your delivery date is arranged!"

msgid "delivery_timeslots_confirmation_subtitle"
msgstr "Cheers, {user}!"

msgid "delivery_timeslots_confirmation_title"
msgstr "Delivery Date"

msgid "delivery_timeslots_confirmation_title_1"
msgstr "Thank you for submitting your availability."

msgid "delivery_timeslots_confirmation_title_3"
msgstr "Your availability for delivery"

msgid "delivery_timeslots_confirmation_title_4"
msgstr "Your Delivery Details"

msgid "delivery_timeslots_confirmation_title_5"
msgstr "Your Additional Order Notes:"

msgid "delivery_timeslots_date_placeholder"
msgstr "DD/MM/YYYY"

msgid "delivery_timeslots_dates_CTA_1"
msgstr "Next Date"

msgid "delivery_timeslots_dates_CTA_2"
msgstr "Save my dates"

msgid "delivery_timeslots_dates_CTA_3"
msgstr "Request new dates"

msgid "delivery_timeslots_dates_body_1"
msgstr "Date 1"

msgid "delivery_timeslots_dates_body_10"
msgstr "Need different dates to choose from?"

msgid "delivery_timeslots_dates_body_2"
msgstr "Date 2"

msgid "delivery_timeslots_dates_body_3"
msgstr "Date 3"

msgid "delivery_timeslots_dates_body_4"
msgstr "From"

msgid "delivery_timeslots_dates_body_5"
msgstr "Hour"

msgid "delivery_timeslots_dates_body_6"
msgstr "To"

msgid "delivery_timeslots_dates_body_7"
msgstr "From (optional)"

msgid "delivery_timeslots_dates_body_8"
msgstr "To (optional)"

msgid "delivery_timeslots_dates_body_9"
msgstr "All day"

msgid "delivery_timeslots_dates_error_1"
msgstr "Please select a date"

msgid "delivery_timeslots_dates_error_2"
msgstr "This day has already been selected"

msgid "delivery_timeslots_dates_error_3"
msgstr "The minimum time slot is 3 hours"

msgid "delivery_timeslots_dates_error_4"
msgstr "Please complete all fields"

msgid "delivery_timeslots_dates_error_5"
msgstr "This field is required"

msgid "delivery_timeslots_dates_error_6"
msgstr "This time is included in a selected time slot."

msgid "delivery_timeslots_dates_error_7"
msgstr "This time slot has already been specified."

msgid "delivery_timeslots_dates_header"
msgstr "Select your preferred date"

msgid "delivery_timeslots_dates_title_1"
msgstr "Select a delivery date"

msgid "delivery_timeslots_dates_title_2"
msgstr "Select your preferred time"

msgid "delivery_timeslots_dates_title_3"
msgstr "Select at least one slot (min. 3 hours)"

msgid "delivery_timeslots_expired_CTA"
msgstr "Request New Dates"

msgid "delivery_timeslots_expired_body_1"
msgstr "Sometimes things get overlooked, we get it. As your link was sent a while ago, all of your available dates have now been taken."

msgid "delivery_timeslots_expired_body_2"
msgstr "If you're not available to accept delivery of your Tylko shelf in the next two weeks, please contact Customer Service."

msgid "delivery_timeslots_expired_body_3"
msgstr "If you're ready for delivery within the next 2 weeks, please click below to let us know. We'll email you a link with new dates from which to select!"

msgid "delivery_timeslots_expired_headline"
msgstr "Better late than never!"

msgid "delivery_timeslots_faq_answer_1"
msgstr "We need to know your preferred date for delivery to create a smooth experience for you, and also because by bundling orders together all in one truck, we can reduce our carbon footprint."

msgid "delivery_timeslots_faq_answer_2"
msgstr "We like to offer more precise dates, but if a whole week is an option, please let us know in the Special Notes section."

msgid "delivery_timeslots_faq_answer_3"
msgstr "Anything you feel might be important concering your delivery - for example, if you live on the top floor with no elevator, have tight parking outside...or five big dogs that might want to eat the courier! :)"

msgid "delivery_timeslots_faq_answer_4"
msgstr "Please try to create adequate space for your packages – more is always better than less! Clear any obstacles out of the way, too, so it's easy to bring your boxes in."

msgid "delivery_timeslots_faq_answer_5"
msgstr "We're here anytime! Pop us a <NAME_EMAIL>"

msgid "delivery_timeslots_faq_question_1"
msgstr "Why can I pick only one date?"

msgid "delivery_timeslots_faq_question_2"
msgstr "Why can't I choose the entire week?"

msgid "delivery_timeslots_faq_question_3"
msgstr "What do I need to write in Special Notes?"

msgid "delivery_timeslots_faq_question_4"
msgstr "What do I need to do before my delivery?"

msgid "delivery_timeslots_faq_question_5"
msgstr "Who can I talk to if I have more questions?"

msgid "delivery_timeslots_faq_subheader"
msgstr "Frequently Asked Questions"

msgid "delivery_timeslots_header"
msgstr "Your Tylko Delivery"

msgid "delivery_timeslots_moredates_CTA"
msgstr "Request new dates"

msgid "delivery_timeslots_moredates_body"
msgstr "If the dates we've suggested don't work for you, no worries. We'll email you a link with new dates for a different 14-day time period. Keep in mind, your current date selections will be lost."

msgid "delivery_timeslots_moredates_headline"
msgstr "Need more dates?"

msgid "delivery_timeslots_new_dates_confirm"
msgstr "Are you sure you'd like to request new dates?"

msgid "delivery_timeslots_newdates_body_1"
msgstr "Our team will email you a link with fresh delivery dates right away."

msgid "delivery_timeslots_newdates_body_2"
msgstr "If you're unable to accept delivery within the next 2 weeks (or have any questions or concerns) please get in touch!"

msgid "delivery_timeslots_newdates_headline"
msgstr "New dates are on the way!"

msgid "delivery_timeslots_shelf"
msgstr "Shelf"

msgid "delivery_timeslots_time_placeholder"
msgstr "HH:MM"

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/pdp.html
msgid "depth"
msgstr "Depth"

msgid "dixa_contact_form_subject_%(contact_id)s"
msgstr "Your contact submission no. %(contact_id)s to Tylko"

msgid "domestic_injection_body_%(carrier)s_%(days)s"
msgstr "Great news! Your order has been produced and will soon be picked up from our warehouse. Your delivery will be organised by our carrier, %(carrier)s. Please expect %(carrier)s to contact you directly within the next %(days)s days to coordinate your Tylko delivery date and details."

msgid "domestic_injection_goodbye"
msgstr "Speak soon,<br>Your Tylko Team"

msgid "domestic_injection_hello_%(user)s"
msgstr "Hi %(user)s,"

msgid "domestic_injection_pre_header"
msgstr "Get ready to arrange your delivery"

msgid "domestic_injection_subject"
msgstr "Your Tylko order update"

msgid "dorothy_assembly_service_billing_address_text"
msgstr "Assembly service"

msgid "dorothy_assembly_service_cart_for"
msgstr "For"

msgid "dorothy_assembly_service_cart_popup_button"
msgstr "Add"

msgid "dorothy_assembly_service_cart_popup_header"
msgstr "At-home assembly"

msgid "dorothy_assembly_service_cart_popup_list"
msgstr "<li>Professional assembly and set-up by our trusted team </li><li>We’ll take away all the leftover packaging</li><li>Total price includes the items in your cart</li>"

msgid "dorothy_assembly_service_cart_popup_text_0"
msgstr "Let us do all the heavy lifting for you. "

msgid "dorothy_assembly_service_cart_popup_text_1"
msgstr "This service is currently available for customers in Germany, the Netherlands, Luxembourg, Austria, Belgium, France, Switzerland, Poland, Denmark, England and Scotland."

msgid "dorothy_assembly_service_cart_popup_text_1.1"
msgstr "For more info,"

msgid "dorothy_assembly_service_cart_popup_text_1.2"
msgstr "please read our FAQ"

msgid "dorothy_assembly_service_cart_popup_text_1.3"
msgstr "This service is currently available for customers in Germany, the Netherlands, Luxembourg, Austria, Belgium, France, Switzerland, Poland, Denmark, England and Scotland. For France, shipping times for orders with an assembly service depend on the location. Usual delivery times apply for Paris and Banlieue. <a href='https://tylko.com/faq/?section=assembly-service&question=0' class=\"text-orange\" target=\"_blank\">Click here</a> to check delivery times for other locations."

msgid "dorothy_assembly_service_cart_ribbon_2"
msgstr "Assembly service added"

msgid "dorothy_assembly_service_cart_ribbon_4"
msgstr "Your cart has been updated"

msgid "dorothy_assembly_service_cart_shelf_assembly_1"
msgstr "Add Assembly service"

msgid "dorothy_assembly_service_cart_shelf_assembly_2"
msgstr "Assembly service"

msgid "dorothy_assembly_service_cart_t03_assembly"
msgstr "Wardrobe assembly"

msgid "dorothy_assembly_service_cart_t03_popup_list"
msgstr " <li>Professional assembly and set-up by our trusted team </li> <li>We’ll take away all the leftover packaging</li><li>Our assembly service for the Tone Wardrobe is free of charge. The total price includes assembly for all items in your cart, excluding the Tone Wardrobe.</li>"

msgid "dorothy_assembly_service_cart_tooltip_1"
msgstr "Our at-home service includes professional assembly, set-up and removal of leftover packaging. For more info, "

msgid "dorothy_assembly_service_cart_tooltip_3"
msgstr "Applies to all items in your cart excluding The Tone Wardrobe. Assembly for the Tone Wardrobe is free of charge and has been automatically added to your cart."

msgid "dorothy_assembly_service_popup_more_items"
msgstr "items in your cart"

msgid "dorothy_assembly_service_popup_one_item"
msgstr "item in your cart"

msgid "dorothy_assembly_service_promocode"
msgstr "Promocode"

msgid "dorothy_promocode_popup_content"
msgstr "Invalid promocode. Please review and re-enter your code."

msgid "dorothy_promocode_popup_title"
msgstr "Invalid promocode"

msgid "doube_optin_confirmation_mail_button_1"
msgstr "Yes, subscribe me"

msgid "doube_optin_confirmation_mail_headline_1"
msgstr "Please confirm your subscription"

msgid "doube_optin_confirmation_mail_paragraph_1_1"
msgstr "You’ve signed up to receive email updates about our latest products, special offers and inspiration."

msgid "doube_optin_confirmation_mail_paragraph_2_1"
msgstr "But before you can receive all that good stuff, you need to confirm your subscription."

msgid "doube_optin_confirmation_mail_paragraph_2_2"
msgstr "Just click the button below!"

msgid "doube_optin_confirmation_mail_paragraph_3_1"
msgstr "If you received this email by mistake, please delete it. You won't be subscribed if you don't click the confirmation link above."

msgid "doube_optin_confirmation_mail_preheader"
msgstr "Thanks for signing up to our newsletter for more info on our latest products, special offers and inspiration! Confirm your subscription to start receiving the good stuff."

msgid "doube_optin_confirmation_mail_subject_line"
msgstr "Please confirm your subscription"

msgid "ecoshare_body_tooltip"
msgstr "On May 1, 2013, an eco-participation law was set up in France for the management and finance of sorting, recycling and recovery of used furniture (furniture, bedding and box springs).<br><br>From October 1, 2018, eco-participation also began being applied to duvets, pillows, bolsters and sleeping bags. The scope of the eco-participation was last extended to many other products on January 1, 2020, including: baskets, boxes, bathroom and kitchen accessories, planters, cable covers, phone / computer mounts, bins, mirrors with storage, hangers and rods.<br><br>These amounts are entirely donated to Eco-Meubles, an organization approved by the State, and devoted to the development of solutions for the collection, recycling and recovery of these used products. Eco-Meubles works in partnership with local authorities, social and solidarity economy associations (Réseau des Ressourceries and Emmaüs) and furniture entities such as Tylko.<br><br>Framed by law, eco-contribution applies in the same way to all furniture, new bedding, lighting, textiles and accessories throughout France. The amount is defined according to a scale common to all manufacturers and distributors, and corresponds to the cost of waste management for these used products."

msgid "ecoshare_ecoshare_modal"
msgstr "Eco-fee"

msgid "ecoshare_price_modal"
msgstr "Price"

msgid "ecoshare_title_modal"
msgstr "What is eco-fee?"

msgid "ecoshare_total_modal"
msgstr "Total"

msgid "emily_cart_buton_1"
msgstr "Checkout"

msgid "emily_cart_buton_2"
msgstr "APPLY"

msgid "emily_cart_buton_3"
msgstr "Shop now"

msgid "emily_cart_header_1_1"
msgstr "Your Cart"

msgid "emily_cart_header_3_1"
msgstr "Price"

msgid "emily_cart_header_3_2"
msgstr "Delivery"

msgid "emily_cart_header_3_3"
msgstr "Free"

msgid "emily_cart_header_3_4"
msgstr "Total"

msgid "emily_cart_header_4_1"
msgstr "Enter Promocode"

msgid "emily_cart_header_4_2"
msgstr "Promocode:"

msgid "emily_cart_header_4_3"
msgstr "Add Promocode"

msgid "emily_cart_header_5_1"
msgstr "All prices include VAT."

msgid "emily_cart_header_5_2"
msgstr "VAT incl."

msgid "emily_cart_icon_1_header_1"
msgstr "100-day<br>free returns"

msgid "emily_cart_icon_1_header_2"
msgstr "100-day free returns"

msgid "emily_cart_input_code"
msgstr "Enter Promocode"

msgid "emily_cart_item_color"
msgstr "Color"

msgid "emily_cart_item_edit_buton_1"
msgstr "EDIT DESIGN"

msgid "emily_cart_item_edit_buton_2"
msgstr "Edit design"

msgid "emily_cart_item_load_header_1"
msgstr "Max. load"

msgid "emily_cart_remove_header_1_1"
msgstr "Are you sure you want to remove this item from your cart?"

msgid "emily_cart_remove_header_2_1"
msgstr "Want to reconsider? Your space deserves a great custom piece like this!"

msgid "emily_category_LP_beedside_name_singular"
msgstr "Bedside Table"

msgid "emily_category_LP_bookcase_name_plural"
msgstr "Bookcases"

msgid "emily_category_LP_bookcase_name_singular"
msgstr "Bookcase"

msgid "emily_category_LP_chestofdrawers_name_singular"
msgstr "Chest of drawers"

msgid "emily_category_LP_shoerack_name_plural"
msgstr "Shoe Racks"

msgid "emily_category_LP_shoerack_name_singular"
msgstr "Shoe Rack"

msgid "emily_category_LP_sideboard_name_plural"
msgstr "Sideboards"

msgid "emily_category_LP_sideboard_name_singular"
msgstr "Sideboard"

msgid "emily_category_LP_tvstand_name_plural"
msgstr "TV Stands"

msgid "emily_category_LP_tvstand_name_singular"
msgstr "TV Stand"

msgid "emily_category_LP_wallstorage_name_plural"
msgstr "Wall Storage"

msgid "emily_category_LP_wallstorage_name_singular"
msgstr "Wall Storage"

msgid "emily_checkout_address_input_lname"
msgstr "Last Name"

msgid "emily_checkout_alert_1"
msgstr "Uh-oh, please check carefully if the credit card details were entered correctly. Otherwise, please contact the bank or our Customer Service."

msgid "emily_exit_popup_sharing_buton_1"
msgstr "Copy link"

msgid "emily_exit_popup_sharing_fb_input_description_1"
msgstr "Check out this perfect-fit shelf I designed myself with Tylko’s fun & easy configurator."

msgid "emily_exit_popup_sharing_fb_input_headline_1"
msgstr "Here's my Tylko Shelf. Thoughts?"

msgid "emily_exit_popup_sharing_header_1_1"
msgstr "Show off your shelf"

msgid "emily_exit_popup_sharing_header_2_1"
msgstr "How exciting is ordering new furniture? Especially a shelf that fits so perfectly! Share your design with your friends and get their opinion."

msgid "emily_exit_popup_sharing_twitter_input_headline_1"
msgstr "I designed this Tylko Shelf myself! What do you think?"

msgid "emily_exit_popup_step1_header_1_1"
msgstr "Need to leave?"

msgid "emily_exit_popup_step1_header_2_1"
msgstr "Save your design so you can come back and keep editing anytime!"

msgid "emily_exit_popup_step2_header_1_1"
msgstr "Your design is safe in your inbox."

msgid "emily_exit_popup_step3_header_1_1"
msgstr "Thanks for signing up!"

msgid "emily_exit_popup_step3_header_2_1"
msgstr "Follow us on..."

msgid "emily_footer_contact_header_1_1"
msgstr "Mon - Fr 10:00 - 22:00 CET"

msgid "emily_footer_contact_header_1_2"
msgstr "Sa - Sun 10:00 - 18:00 CET"

msgid "emily_footer_contact_header_2"
msgstr "Contact form"

msgid "emily_footer_contact_header_3"
msgstr "Live chat"

msgid "emily_footer_newsletter_alert_1"
msgstr "This email address is already subscribed!"

#, python-format
msgid "emily_footer_newsletter_buton_1_%(amount)s"
msgstr "Get %(amount)s"

msgid "emily_footer_newsletter_confirmation_1"
msgstr "Please check your inbox to confirm your subscription."

msgid "emily_footer_newsletter_header_1_1"
msgstr "Inspiration, special offers and more"

#, python-format
msgid "emily_footer_newsletter_header_2_1_%(amount)s"
msgstr "Sign up and get a %(amount)s gift before it’s gone"

msgid "emily_footer_newsletter_tooltip_1_1"
msgstr "The data administrator is Tylko S.A."

msgid "emily_footer_newsletter_tooltip_2_1"
msgstr "The data administrator is Tylko S.A. with headquarters in Warsaw at Czerska 8/10 (hereinafter also referred to as \"\"Administrator\"\"). The administrator can be contacted <NAME_EMAIL> email address. You can contact the Data Administrator on all matters relating to the processing of personal data and the use of data processing rights.<br><br>Your data will be processed in order to inform about the Company's products.<br><br>Your personal data will be made available to entities that process personal data at the request of the administrator (including IT service providers, marketing agencies) - these entities process data on the basis of a contract with the administrator and only on the administrator's request.<br><br>Your personal data will be stored no longer than until you find out that you do not want to receive these materials.<br><br>You have the right to access your data and the right to demand their rectification, removal or restriction of their processing. At your request, the administrator will provide a copy of the personal data to be processed, but for any subsequent copies you request, the administrator may charge a reasonable amount resulting from administrative costs.<br><br>You have the right to withdraw your consent. Withdrawal of consent does not affect the lawfulness of the processing that was made on the basis of consent before its withdrawal.<br><br>To the extent that your data is processed in an automated manner in order to conclude and perform the contract or processed on the basis of consent - you also have the right to transfer personal data, i.e. to receive personal data from the controller, in a structured, commonly used machine-readable format. You may send this data to another data administrator.<br><br>You also have the right to lodge a complaint with the supervisory body dealing with the protection of personal data.<br><br>In order to exercise the above rights, please contact the data controller. Contact details are indicated above.<br><br>Providing personal data for the above purposes is voluntary."

msgid "emily_hp_buton_2"
msgstr "Configure yours"

msgid "emily_hp_quotes_header_1"
msgstr "\"Une petite révolution\""

msgid "emily_hp_quotes_header_2"
msgstr "\"A bridge between craftsmanship and high-tech\""

msgid "emily_hp_quotes_header_3"
msgstr "\"Very powerful and easy-to-use\""

msgid "emily_hp_quotes_header_4"
msgstr "\"Slashes production costs and time\""

msgid "emily_hp_quotes_header_5"
msgstr "\"Customizing furniture is as easy as a swipe\""

msgid "emily_hp_quotes_header_6"
msgstr "\"Innovative contemporary designers\""

msgid "emily_mailing_cartabandoner_mail_1a_buton_1"
msgstr "Shop on"

msgid "emily_mailing_cartabandoner_mail_1a_buton_2"
msgstr "Join us!"

msgid "emily_mailing_cartabandoner_mail_1a_headline_1"
msgstr "We kept your cart,<br>no worries."

msgid "emily_mailing_cartabandoner_mail_1a_paragraph_1_1"
msgstr "We know how it goes - sometimes you just don't have the time. You had to leave your cart behind, but we kept it for you - no sweat!"

msgid "emily_mailing_cartabandoner_mail_1a_paragraph_2_1"
msgstr "Want great design inspo and to connect with other awesome people who love their Tylko? Check out the Tylko Facebook group!"

msgid "emily_mailing_cartabandoner_mail_1a_preheader"
msgstr "You left your cart hanging..."

msgid "emily_mailing_cartabandoner_mail_1a_subject_line"
msgstr "Oops, don’t leave this behind!"

msgid "emily_mailing_cartabandoner_mail_2a_buton_1"
msgstr "Shop Tylko"

msgid "emily_mailing_cartabandoner_mail_2a_buton_2"
msgstr " Learn more here."

msgid "emily_mailing_cartabandoner_mail_2a_headline_1"
msgstr "Your cart misses you..."

msgid "emily_mailing_cartabandoner_mail_2a_paragraph_1_1"
msgstr "But it’s still safe and sound. It’s here waiting for you when you're ready!"

msgid "emily_mailing_cartabandoner_mail_2a_paragraph_2_1"
msgstr "Your cart:"

msgid "emily_mailing_cartabandoner_mail_2a_paragraph_3_1"
msgstr "Did you know we offer awesome and totally free 100-day returns? If for any reason you don't like it we'll take it back - no questions asked..."

msgid "emily_mailing_cartabandoner_mail_2a_preheader"
msgstr "We've saved your cart until you come back."

msgid "emily_mailing_cartabandoner_mail_2a_subject_line"
msgstr "Did you forget about your cart?"

msgid "emily_mailing_cartabandoner_mail_3a_buton_0"
msgstr "happy-ever-after stories"

msgid "emily_mailing_cartabandoner_mail_3a_buton_1"
msgstr "your cart"

msgid "emily_mailing_cartabandoner_mail_3a_buton_2"
msgstr "checkout"

msgid "emily_mailing_cartabandoner_mail_3a_header_1_1"
msgstr "Hey friend,"

msgid "emily_mailing_cartabandoner_mail_3a_paragraph_1_1"
msgstr "Your totally-custom Tylko design is almost complete… so why the pause?<br><br>We know what you might be thinking: will it be love at first sight? Will we grow old together? Luckily our customers have lots of "

msgid "emily_mailing_cartabandoner_mail_3a_paragraph_2_1_a"
msgstr "to share to help you commit."

msgid "emily_mailing_cartabandoner_mail_3a_paragraph_2_1_b"
msgstr "All you need to do now is head over to "

msgid "emily_mailing_cartabandoner_mail_3a_paragraph_2_2"
msgstr "to complete your order."

msgid "emily_mailing_cartabandoner_mail_3a_paragraph_3_1"
msgstr "P.S – When you're ready to "

#, python-format
msgid "emily_mailing_cartabandoner_mail_3a_paragraph_3_2_%(amount)s"
msgstr "you can use this sweet promo code to save<span style=\"color: #FF3C00;\"> %(amount)s </span> on your order too:"

#, python-format
msgid "emily_mailing_cartabandoner_mail_3a_paragraph_3_2_%(name)s"
msgstr "<span style=\"color: #FF3C00;\"> %(name)s </span>"

#, python-format
msgid "emily_mailing_cartabandoner_mail_3a_paragraph_3_4_%(value)s"
msgstr "Your code is only valid for five days for all orders above %(value)s."

msgid "emily_mailing_cartabandoner_mail_3a_paragraph_4_1"
msgstr "Love,<br>Team Tylko"

#, python-format
msgid "emily_mailing_cartabandoner_mail_3a_preheader_%(amount)s"
msgstr "(and how you can save %(amount)s, too!)"

msgid "emily_mailing_cartabandoner_mail_3a_sender"
msgstr "Tylko"

msgid "emily_mailing_cartabandoner_mail_3a_subject_line"
msgstr "This is how much you’ll love your Tylko."

msgid "emily_mailing_cartabandoner_mail_4a_buton_1"
msgstr "Checkout now"

msgid "emily_mailing_cartabandoner_mail_4a_headline_1"
msgstr "Your cart is calling..."

msgid "emily_mailing_cartabandoner_mail_4a_paragraph_1_1"
msgstr "That design you made a while back? We've been holding onto it for you. But now we want to see it in your space."

#, python-format
msgid "emily_mailing_cartabandoner_mail_4a_paragraph_2_1_%(amount)s"
msgstr "The cost of sourcing the best wood changes from time to time, so sometimes we need to adjust our pricing too. Luckily now is the best time to snag your Tylko for a great price! How about <span style=\"color: #FF3C00;\"> %(amount)s </span> off your order to help make it happen? "

#, python-format
msgid "emily_mailing_cartabandoner_mail_4a_paragraph_2_1_%(name)s"
msgstr "Just enter<span style=\"color: #FF3C00;\"> %(name)s </span>"

msgid "emily_mailing_cartabandoner_mail_4a_paragraph_2_2"
msgstr "when you make your order - but be quick! It's only valid for 3 days."

msgid "emily_mailing_cartabandoner_mail_4a_preheader"
msgstr "Shop your cart and save!"

#, python-format
msgid "emily_mailing_cartabandoner_mail_4a_subject_line_%(amount)s"
msgstr "Hey, here's %(amount)s off. On us."

msgid "emily_mailing_checkout_abandoner_mail_1a_buton_1"
msgstr "click here"

msgid "emily_mailing_checkout_abandoner_mail_1a_buton_2"
msgstr "Trusted Shop verified"

msgid "emily_mailing_checkout_abandoner_mail_1a_header_1_1"
msgstr "Hey There!"

msgid "emily_mailing_checkout_abandoner_mail_1a_paragraph_1_1"
msgstr "You took off in a hurry - and forgot to finish up your transaction!<br><br>Luckily we kept it for you. Just"

msgid "emily_mailing_checkout_abandoner_mail_1a_paragraph_1_2"
msgstr "to pick up where you left off! It's totally safe and secure - we're"

msgid "emily_mailing_checkout_abandoner_mail_1a_paragraph_1_3"
msgstr "and even offer free 100-day returns. Easy peasy."

msgid "emily_mailing_checkout_abandoner_mail_1a_paragraph_2_1"
msgstr "See You Soon,<br>The Tylko Team"

msgid "emily_mailing_checkout_abandoner_mail_1a_preheader"
msgstr "You’re just one (or two) clicks away from furniture bliss!"

msgid "emily_mailing_checkout_abandoner_mail_1a_subject_line"
msgstr "Let’s do this together...you ready?"

msgid "emily_mailing_checkout_abandoner_mail_1b_preheader"
msgstr "A few more clicks and you're done!"

msgid "emily_mailing_checkout_abandoner_mail_1b_subject_line"
msgstr "There's just one more step..."

msgid "emily_mailing_checkout_abandoner_mail_2a_buton_1"
msgstr "Complete your order today!"

msgid "emily_mailing_checkout_abandoner_mail_2a_header_1_1"
msgstr "Hey There!"

#, python-format
msgid "emily_mailing_checkout_abandoner_mail_2a_paragraph_1_1_%(amount)s"
msgstr "How does<span style=\"color: #FF3C00;\"> %(amount)s </span>off your order sound?"

#, python-format
msgid "emily_mailing_checkout_abandoner_mail_2a_paragraph_1_2_%(name)s"
msgstr "Just enter code<span style=\"color: #FF3C00;\"> %(name)s </span>"

msgid "emily_mailing_checkout_abandoner_mail_2a_paragraph_1_3"
msgstr "in the next 3 days and we'll take it off your order."

msgid "emily_mailing_checkout_abandoner_mail_2a_paragraph_3_1_signature"
msgstr "<br>See You Soon,<br>The Tylko Team"

msgid "emily_mailing_checkout_abandoner_mail_2a_preheader"
msgstr "You pay us, we'll pay you. Deal."

msgid "emily_mailing_checkout_abandoner_mail_2a_subject_line"
msgstr "Checkout now and save a bundle!"

msgid "emily_mailing_checkout_abandoner_mail_3a_buton_1"
msgstr "Checkout now"

msgid "emily_mailing_checkout_abandoner_mail_3a_headline_1"
msgstr "3...2...1...DONE."

msgid "emily_mailing_checkout_abandoner_mail_3a_paragraph_1_1"
msgstr "You're steps away from finishing your order...why not seal the deal today? We can't guarantee prices won't rise, or that your order will still be available, so it's time for action!"

msgid "emily_mailing_checkout_abandoner_mail_3a_paragraph_2_1"
msgstr "Here's what you had in your cart:"

#, python-format
msgid "emily_mailing_checkout_abandoner_mail_3a_paragraph_3_2_%(name)s"
msgstr "Just enter the promo code <span style=\"color: #FF3C00;\"> %(name)s</span> - valid for the next 3 days!"

#, python-format
msgid "emily_mailing_checkout_abandoner_mail_3a_paragraph_3_3_%(amount)s"
msgstr "Complete your order right away and we'll give you<span style=\"color: #FF3C00;\"> %(amount)s </span>off."

msgid "emily_mailing_checkout_abandoner_mail_3a_preheader"
msgstr "Let's get this order done, shall we?"

msgid "emily_mailing_checkout_abandoner_mail_3a_subject_line"
msgstr "Snag your price, and some savings!"

msgid "emily_mailing_checkout_abandoner_mail_3b_buton_1"
msgstr "Click here"

msgid "emily_mailing_checkout_abandoner_mail_3b_header_1_1"
msgstr "Hi there,"

msgid "emily_mailing_checkout_abandoner_mail_3b_paragraph_1_1"
msgstr "Our wood supply prices can rise in an instant, and we'd hate for you to miss out on your order's low cost. We also can't guarantee your order's availability much longer, so now's the time for action!"

#, python-format
msgid "emily_mailing_checkout_abandoner_mail_3b_paragraph_2_2_%(name)s"
msgstr "Just enter the promo code<span style=\"color: #FF3C00;\"> %(name)s </span>- valid for the next 3 days!"

#, python-format
msgid "emily_mailing_checkout_abandoner_mail_3b_paragraph_2_3_%(amount)s"
msgstr "Complete your order right away and we'll give you<span style=\"color: #FF3C00;\"> %(amount)s </span>."

msgid "emily_mailing_checkout_abandoner_mail_3b_paragraph_3_1"
msgstr "to close up your order and snag the savings!"

msgid "emily_mailing_checkout_abandoner_mail_3b_paragraph_4_1"
msgstr "See You Soon,<br>The Tylko Team"

msgid "emily_mailing_checkout_abandoner_mail_3b_preheader"
msgstr "Avoid a cost increase now!"

msgid "emily_mailing_checkout_abandoner_mail_3b_subject_line"
msgstr "Secure your cart price, quick!"

msgid "emily_mobile_footer_header_1"
msgstr "We are certified by Trusted Shops Buyer Protection & Data Protection"

msgid "emily_mobile_footer_header_2"
msgstr "We accept"

msgid "emily_mobile_footer_header_3"
msgstr "Contact Customer Service"

msgid "emily_mobile_menu_header_1_3"
msgstr "Language and Regions"

#, python-format
msgid "emily_mobile_pdp_promo_code_alert_1_%(amount)s"
msgstr "Go bigger and get up to %(amount)s off!"

#, python-format
msgid "emily_mobile_pdp_promo_code_alert_2_%(amount)s"
msgstr "Get %(amount)s off with the promocode MYSHELF"

#, python-format
msgid "emily_mobile_pdp_promo_code_alert_3_%(amount)s"
msgstr "Get %(amount)s off with the promocode MYSHELF"

msgid "emily_mobile_region_menu_hearder_1_1"
msgstr "Language"

msgid "emily_mobile_region_menu_hearder_1_2"
msgstr "Regions"

msgid "emily_pdp_common_rotate_alert_1"
msgstr "Turn your device horizontal"

msgid "emily_pdp_common_rotate_alert_2"
msgstr "Turn your device vertical"

msgid "emily_pdp_configurator_delivery_time_tooltip"
msgstr "Each custom piece is made to order, as we carry no stock. Due to high demand, our estimated shipping times are currently longer than usual."

msgid "emily_pdp_configurator_depth_header_1"
msgstr "Depth of<br>the shelf"

msgid "emily_pdp_configurator_description_button"
msgstr "Show shipment date"

msgid "emily_pdp_configurator_description_desc"
msgstr "We constantly update shipping time based on our factory production capacity and stock level."

msgid "emily_pdp_configurator_load_header_1"
msgstr "Load on single compartment"

msgid "emily_pdp_configurator_load_header_2"
msgstr "Max. load<br>of your shelf"

msgid "emily_pdp_configurator_load_header_2_tooltip"
msgstr "To avoid sagging, always load the shelves evenly."

msgid "emily_pdp_section_2_video_buton_2"
msgstr "Replay"

msgid "emily_pdp_section_5_sufix_1"
msgstr "reviews"

msgid "emily_pdp_specification_section_1_list_3"
msgstr "Depth"

msgid "emily_pdp_specification_section_1_list_5"
msgstr "Load bearing"

msgid "emily_save_other_region_buton_1"
msgstr "Check availability"

msgid "emily_save_other_region_header_1_1"
msgstr "Outside of Europe? Please check our FAQ for more shipping details."

msgid "emily_save_other_region_popup_step1_buton_1"
msgstr "Check availability"

msgid "emily_save_other_region_popup_step1_header_1_1"
msgstr "Country not listed? We can still get your Tylko to you!"

msgid "emily_save_other_region_popup_step1_header_2_1"
msgstr "We deliver Europe-wide for free, but in many cases we can arrange to ship outside of the EU as well."

msgid "emily_save_other_region_popup_step1_header_2_2"
msgstr "Please leave your email and postcode and we'll let you know if it's possible."

msgid "emily_save_other_region_popup_step1_input_country"
msgstr "Country"

msgid "emily_save_other_region_popup_step1_input_email"
msgstr "E-mail"

msgid "emily_save_other_region_popup_step1_input_postcode"
msgstr "Postcode"

msgid "emily_save_other_region_popup_step2_buton_1"
msgstr "Get the Tylko app"

msgid "emily_save_other_region_popup_step2_header_1_1"
msgstr "Your quote will be ready in just a moment!"

msgid "emily_save_other_region_popup_step2_header_2_1"
msgstr "Check your inbox soon.<br><br>While we work out your quote, why not explore how your shelves will look in your space with our awesome augmented reality app?"

msgid "emily_save_popup_step1_header_1_1"
msgstr "Smart thinking!"

msgid "emily_save_popup_step1_header_2_1"
msgstr "We’ll hang onto your design and send you occasional reminders until you’re ready to finish it up. Please enter your email below to agree:"

msgid "error_404_body"
msgstr "This page may be lost, but you don't need to be! <br class=\"desktop-air-visible\"> Click <a class=\"link text-orange-900\" href=\"https://tylko.com/\">here</a> to visit our homepage, or below to keep exploring:"

msgid "error_404_headline"
msgstr "Hmm, that didn't <span class=\"error-page__title-span\">quite work.</span>"

msgid "extended_chaise_longue_module"
msgstr "Ottoman chaise longue"

msgid "extended_chaise_longue_module_cover"
msgstr "Cover for ottoman chaise longue"

msgid "extended_delivery_dynamic_text_with_br"
msgstr "Will be delivered in {0}-{1} weeks"

msgid "extended_delivery_dynamic_text_with_tick"
msgstr "Will be delivered in <br>{0}-{1} weeks"

msgid "facebook_user_id"
msgstr "This cookie is set by Facebook and can be used for tracking conversions and advertising purposes."

msgid "fast_delivery"
msgstr "This cookie is used for AB testing purposes"

msgid "fasttrack_service_available_tooltip"
msgstr "Don’t feel like waiting 3-4 weeks for your Tylko? Good news: order any product from the Tylko Original Modern line today and it will be shipped in 5 days for an extra 79 euro."

msgid "fasttrack_service_cart_button"
msgstr "Add Fast Track"

msgid "fasttrack_service_cart_for"
msgstr "For all Tylko Original Modern items in your cart"

msgid "fasttrack_service_cart_popup_header"
msgstr "Fast Track"

msgid "fasttrack_service_cart_popup_list"
msgstr "<li>Ships in 5 days </b></li><li>One price no matter how many items you buy</li><li>No compromise on quality</li>"

msgid "fasttrack_service_cart_popup_text_0"
msgstr "Enjoy faster production for all Tylko Original Modern products in your basket."

msgid "fasttrack_service_cart_popup_text_1"
msgstr "This service is available until Monday and currently only works for customers in the Netherlands."

msgid "fasttrack_service_cart_ribbon_2"
msgstr "Fast Track added"

msgid "fasttrack_service_cart_ribbon_4"
msgstr "Fast Track removed"

msgid "fasttrack_service_cart_shelf_assembly_2"
msgstr "Fast Track"

msgid "fasttrack_service_unavailable_tooltip"
msgstr "Sorry, it's not possible to add Fast Track to your order as this service is currently available for Tylko Original Modern products only. "

msgid "feature_samples"
msgstr "This Cookie has been set up to provide users with the ability to order material samples for all product lines."

msgid "feed_sofa_large_prefix"
msgstr "Large"

msgid "feed_sofa_modular_prefix"
msgstr "Modular"

msgid "filter_menu_section_1_label_6_pill"
msgstr "White"

msgid "filter_menu_section_1_label_7_pill"
msgstr "Terracotta"

msgid "filter_menu_section_1_label_8_pill"
msgstr "Midnight&nbsp;Blue"

msgid "filter_menu_section_1_label_9_pill"
msgstr "Sand&nbsp;+&nbsp;Midnight&nbsp;Blue"

msgid "find_more"
msgstr "Find out more"

msgid "footer_header_badges"
msgstr "Secure Shopping"

msgid "footer_header_left"
msgstr "Tylko"

msgid "footer_header_middle"
msgstr "Buying Guide"

msgid "footer_header_trustedshop"
msgstr "Certified by Trusted Shops"

msgid "footer_paymentmethods_more"
msgstr "and more"

msgid "footrest_and_modules_pdp_name"
msgstr "ottomans_and_modules"

msgid "form_address"
msgstr "Street Address"

msgid "form_billingaddress"
msgstr "Billing Address"

msgid "form_city"
msgstr "City"

msgid "form_companyname"
msgstr "Company name"

msgid "form_country"
msgstr "Country"

msgid "form_faild_value_invalid"
msgstr "This field is invalid."

msgid "form_faild_value_required"
msgstr "This value is required."

msgid "form_name"
msgstr "Name and Surname"

msgid "form_phone"
msgstr "Phone number"

msgid "form_postcode"
msgstr "Postcode"

msgid "four_plus_seater_pdp_name"
msgstr "four_plus_seater"

msgid "fr"
msgstr "This cookie has been set up by Facebook. The gathered information is used in their advertising products, for example real time bidding from third party advertisers."

msgid "free-of-charge"
msgstr "Free of charge"

msgid "grid_button_see_all_products"
msgstr "See all products"

msgid "grid_category_bedroom"
msgstr "Bedroom"

msgid "grid_category_hallway"
msgstr "Hallway"

msgid "grid_category_living_room"
msgstr "Living room"

msgid "grid_category_office"
msgstr "Office"

msgid "grid_headline_browse"
msgstr "Browse by room"

msgid "grid_label_cable_management"
msgstr "+ Cable Management"

msgid "grid_label_extra_storage"
msgstr "+ Extra Storage"

msgid "grid_label_legs"
msgstr "+ Legs"

msgid "grid_label_new"
msgstr "New"

msgid "grid_label_plinth"
msgstr "+ Plinth"

msgid "grid_test_splus_aggresive"
msgstr "This cookie is used to differentiate the number of Sideboards displayed in the product grid on the website."

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/pdp.html
msgid "height"
msgstr "Height"

msgid "hotline_annotation"
msgstr "Calls may be charged at service provider rates."

msgid "hp_banner_newcolours_cta"
msgstr "Explore new colours"

msgid "hp_matte_black_learn_more"
msgstr "Learn More"

msgid "hp_matte_black_section_alt"
msgstr "The new Tylko Original Modern Bookcase in premium Matte Black."

msgid "hp_matte_black_section_body"
msgstr "Our much-loved shelves, upgraded with a smooth, lush finish."

msgid "hp_matte_black_section_title"
msgstr "The Tylko Original Modern in premium Matte Black"

msgid "i18n_redirected"
msgstr "This cookie is set to keep detected/chosen language version of site."

msgid "included"
msgstr "Included"

msgid "installment_available_common"
msgstr "Shop now, <span class=\"inline-block\">pay in installments.<span>"

msgid "instalp-button"
msgstr "Get Yours"

msgid "intercom-id"
msgstr "Anonymous visitor identifier cookie for Intercom Messenger. These performance and functionality cookies allow interaction with the Messenger (on domains widget.intercom.io and api-iam.intercom.io). These cookies allow this interaction by remembering who you are and providing access to previous conversations you've had through the Messenger."

msgid "intercom-session"
msgstr "Identifier for each unique browser session. The user can access their conversations and have data communicated on logged out pages for 1 week, as long as the session isn't intentionally terminated with `Intercom('shutdown');`, which usually happens on logout."

msgid "invalid_voucher_fallback"
msgstr "Promo code does not apply to the item(s) in your cart"

msgid "is_grid_v3"
msgstr "This cookie is used for AB testing purposes"

msgid "ivy_ash_plywood"
msgstr "Natural Plywood"

msgid "ivy_black"
msgstr "Black"

msgid "ivy_black_plywood"
msgstr "Black Plywood"

msgid "ivy_black_plywood_feminine"
msgstr "ivy_black_plywood_feminine"

msgid "ivy_black_plywood_masculine"
msgstr "ivy_black_plywood_masculine"

msgid "ivy_black_plywood_neuter"
msgstr "ivy_black_plywood_neuter"

msgid "ivy_blue"
msgstr "Blue"

msgid "ivy_blue_plywood"
msgstr "Blue Plywood"

msgid "ivy_dark_brown"
msgstr "Brown"

msgid "ivy_dark_brown_plywood"
msgstr "Dark Brown Plywood"

msgid "ivy_grey"
msgstr "Grey"

msgid "ivy_grey_plywood"
msgstr "Grey Plywood"

msgid "ivy_grey_plywood_feminine"
msgstr "ivy_grey_plywood_feminine"

msgid "ivy_grey_plywood_masculine"
msgstr "ivy_grey_plywood_masculine"

msgid "ivy_grey_plywood_neuter"
msgstr "ivy_grey_plywood_neuter"

msgid "ivy_mossgreen"
msgstr "Moss Green"

msgid "ivy_mossgreen_plywood"
msgstr "Moss Green"

msgid "ivy_pink"
msgstr "Dusty Pink"

msgid "ivy_pink_plywood"
msgstr "Dusty Pink Plywood"

msgid "ivy_purple_plywood"
msgstr "Aubergine Plywood"

msgid "ivy_red"
msgstr "Red"

msgid "ivy_red_plywood"
msgstr "Classic Red Plywood"

msgid "ivy_shelf_configurator_height_button"
msgstr "Adjust rows"

msgid "ivy_white"
msgstr "White"

msgid "ivy_white_plywood"
msgstr "White Plywood"

msgid "ivy_white_plywood_feminine"
msgstr "ivy_white_plywood_feminine"

msgid "ivy_white_plywood_masculine"
msgstr "ivy_white_plywood_masculine"

msgid "ivy_white_plywood_neuter"
msgstr "ivy_white_plywood_neuter"

msgid "ivy_yellow"
msgstr "Yellow"

msgid "ivy_yellow_plywood"
msgstr "Yellow Plywood"

msgid "ku1-sid"
msgstr "This cookie is used by payment system Klarna. It contains a unique user ID which enables Klarna to provide the user with personalised Klarna ads when revisiting a merchant using Klarna services. It is also used for analytic purposes. The information collected by Klarna through this cookie will never, under no circumstance, be shared by any third party."

msgid "ku1-vid"
msgstr "This cookie is used by payment system Klarna. It contains a unique user ID which enables Klarna to provide the user with personalised Klarna ads when revisiting a merchant using Klarna services. It is also used for analytic purposes. The information collected by Klarna through this cookie will never, under no circumstance, be shared by any third party."

msgid "ku3-vid"
msgstr "This cookie is used by payment system Klarna. It contains a unique user ID which enables Klarna to provide the user with personalised Klarna ads when revisiting a merchant using Klarna services. It is also used for analytic purposes. The information collected by Klarna through this cookie will never, under no circumstance, be shared by any third party."

msgid "last_visit"
msgstr "This cookie is used to gather information about the user's last visit."

msgid "left_corner_module"
msgstr "Left corner"

msgid "left_corner_module_cover"
msgstr "Cover for left corner"

msgid "lp_newsletter_body_1_%(amount)s"
msgstr "lp_newsletter_body_1_%(amount)s"

#, python-format
msgid "lp_newsletter_body_1_%(amount)s "
msgstr "Sign up to stay updated on all things Tylko and enjoy a %(amount)s as an exclusive welcome gift — get it before it's gone! "

msgid "lp_promo_error"
msgstr "Oops! Sorry,  we can't find this email in our customer base. This discount is applicable to Tylko customers only. "

msgid "lp_promo_error_2"
msgstr "Oops! An error occurred. To receive your special gift code, please reach out to our customer service team at +44 ************"

msgid "lp_shoerack_explore_reset"
msgstr "Reset filter"

msgid "lp_shoerack_explore_type01p"
msgstr "Tylko Original Classic in Plywood"

msgid "lp_shoerack_explore_type01v"
msgstr "Tylko Original Classic in Veneer"

msgid "lp_shoerack_explore_type02"
msgstr "Tylko Original Modern"

msgid "lp_shoerack_product_lines_body_1"
msgstr "The original Tylko shelf - timeless design made to last a lifetime."

msgid "lp_shoerack_product_lines_body_2"
msgstr "A classic reinvented. A thoughtfully-made shelf inspired by nature."

msgid "lp_shoerack_product_lines_body_3"
msgstr "A bold and modern shelf for spaces with a lot to say."

msgid "lp_shoerack_product_lines_cta_1"
msgstr "Create now"

msgid "lp_shoerack_product_lines_cta_2"
msgstr "Create now"

msgid "lp_shoerack_product_lines_cta_3"
msgstr "Create now"

msgid "lp_shoerack_product_lines_headline_1"
msgstr "Tylko Original Classic in Plywood"

msgid "lp_shoerack_product_lines_headline_2"
msgstr "Tylko Original Classic in Veneer"

msgid "lp_shoerack_product_lines_headline_3"
msgstr "Tylko Original Modern"

msgid "lp_splusall_gallery_name"
msgstr "in"

msgid "lp_t03wardrobe_grid_colour_5"
msgstr "White + Antique Pink"

msgid "lp_t03wardrobe_grid_colour_6"
msgstr "Graphite Grey + Antique Pink"

msgid "lp_t03wardrobe_grid_toggle_1"
msgstr "Narrow"

msgid "lp_t03wardrobe_grid_toggle_1_1"
msgstr "Less than 150cm"

msgid "lp_t03wardrobe_grid_toggle_2"
msgstr "Medium"

msgid "lp_t03wardrobe_grid_toggle_2_1"
msgstr "150-250cm"

msgid "lp_t03wardrobe_grid_toggle_3"
msgstr "Wide"

msgid "lp_t03wardrobe_grid_toggle_3_1"
msgstr "More than 250cm"

msgid "mail24_intro_body_1"
msgstr "We recently sent you an e-mail regarding your delivery details. If you still haven't received your tracking number, please be patient – its on the way! If you’d like to correct the delivery address or any other detail, please contact our <a href='tel:+44 ************' class='email-24-summary__link text-error'>Customer Service.</a>"

msgid "mail24_shelf"
msgstr "Shelf"

msgid "mail24_subheadline_1"
msgstr "Your furniture is ready to ship"

msgid "mail24_summary_1"
msgstr "Delivery Details"

msgid "mail24_summary_2"
msgstr "Included Packages"

msgid "mail24_summary_3"
msgstr "Assembly Instructions"

msgid "mail24_summary_body_1"
msgstr "Box dimensions for your {dimensions} shelf:"

msgid "mail24_summary_body_2"
msgstr "{size} and {weight}"

msgid "mail24_survey_CTA"
msgstr "Let's Go"

msgid "mail24_survey_answer_1_1"
msgstr "Yes"

msgid "mail24_survey_answer_1_2"
msgstr "No"

msgid "mail24_survey_answer_2_1"
msgstr "Number of flights of stairs"

msgid "mail24_survey_answer_2_2"
msgstr "Select"

msgid "mail24_survey_answer_2_3"
msgstr "Exact number of floors"

msgid "mail24_survey_answer_2_4"
msgstr "Enter here"

msgid "mail24_survey_answer_2_5"
msgstr "More than 9"

msgid "mail24_survey_answer_3_1"
msgstr "Add your notes here"

msgid "mail24_survey_body"
msgstr "We want to make sure your shelf arrives with no hassle. Please take a few minutes to answer a few helpful questions."

msgid "mail24_survey_body_mobile"
msgstr "These questions concern the delivery details for:"

msgid "mail24_survey_delivery_info_placeholder"
msgstr "i.e. Gate/door code (optional)"

msgid "mail24_survey_finish"
msgstr "Finished"

msgid "mail24_survey_headline"
msgstr "Your Delivery Details"

msgid "mail24_survey_next_1"
msgstr "Next"

msgid "mail24_survey_next_2"
msgstr "Next"

msgid "mail24_survey_question_1"
msgstr "Is there a functioning elevator at the address?"

msgid "mail24_survey_question_2"
msgstr "How many flights of stairs will our courier have to climb?"

msgid "mail24_survey_question_2_body"
msgstr "You’ll be able to add more notes for the courier in the next step."

msgid "mail24_survey_question_3"
msgstr "Please feel free to add any details you think our Delivery Planning Department should know:"

msgid "mail24_survey_question_3_1"
msgstr "Further info for the courier:"

msgid "mail24_survey_question_3_body"
msgstr "(optional)"

msgid "mail24_survey_question_3_edd_paragraph_1"
msgstr "Great news! Your order is ready and will be handed over to the courier company on {pickup_date}. The estimated transit time for your delivery is between 3 to 5 working days."

msgid "mail24_survey_question_3_edd_paragraph_2"
msgstr "Once your order has been received by the courier company, you'll receive an email with a link to track the progress of your delivery. On the day of delivery, your courier will send an email or SMS with an estimated delivery time window, as well as a link so you can manage the final details of your shipment. If needed, you can adjust the delivery date, forward the shipment to a different address or neighbor, or arrange for the package to be left at your doorstep if you're not available."

msgid "mail24_survey_sidebar_body_1"
msgstr "Yes"

msgid "mail24_survey_sidebar_body_2"
msgstr "No"

msgid "mail24_survey_sidebar_headline"
msgstr "Delivery Details"

msgid "mail24_survey_sidebar_subheadline_1"
msgstr "Elevator:"

msgid "mail24_survey_sidebar_subheadline_2"
msgstr "Flights of stairs:"

msgid "mail24_survey_sidebar_subheadline_3"
msgstr "Phone number:"

msgid "mail24_survey_sidebar_tooltip"
msgstr "To change your delivery address or phone number, please call Customer Service at +44 ************."

msgid "mail24_survey_summary_body"
msgstr "Tylko delivery is a breeze! Your shipment will now be arranged, and you'll be notified about the next steps no later than {date}. Meanwhile, please look over the summary below. If you’d like to correct anything, please contact our <a href='tel:+44 ************' class='email-24-summary__link text-error'>Customer Service.</a>"

msgid "mail24_survey_summary_edd_body_1"
msgstr "Your Tylko delivery is now underway. Unless you've otherwise asked for your shipment to be postponed, your order will be handed over to the courier for delivery within 3-5 working days. Please review your summary below, and in the case of any changes, contact Customer Service <a href='tel:+44 ************' class='email-24-summary__link text-error'>here</a>."

msgid "mail24_survey_summary_headline"
msgstr "Delivery Details"

msgid "mail24_survey_summary_subheadline_1"
msgstr "Thanks for letting us know!"

msgid "mail24_survey_summary_subheadline_2"
msgstr "Your Delivery Details"

msgid "mail24_survey_summary_subheadline_3"
msgstr "Delivery Notes:"

msgid "mail24_survey_summary_subheadline_4"
msgstr "Included Packages"

msgid "mail24_survey_summary_subheadline_5"
msgstr "Assembly Instructions"

msgid "mail24_survey_summary_subheadline_6"
msgstr "Don't worry – they'll also be included with your delivery."

msgid "mail24_survey_summary_title_1"
msgstr "Order Number"

msgid "mail24_survey_summary_title_2"
msgstr "Delivery address"

msgid "mail24_survey_title_1"
msgstr "Order Number"

msgid "mail24_survey_title_2"
msgstr "Delivery address"

msgid "mail_assembly_service_choose_date_estimated_delivery_date_paragraph_2_1_%(duration)s"
msgstr "Our team will arrive on the date you choose at the address you provided, and we estimate your assembly service will take approximately %(duration)s hours."

msgid "mail_assembly_service_choose_date_estimated_delivery_date_paragraph_2_2_%(date)s_%(time)s"
msgstr "You have until %(time)s on %(date)s to let us know what works best for you (otherwise your dates might be taken). If none of these dates are suitable, just click “Request New Dates” for more options, but please be aware that the next available slots might be in a week's time or possibly even later."

msgid "mail_assembly_service_choose_date_paragraph_1_1"
msgstr "We're ready to schedule your Tylko assembly service. Just click to confirm the time and date that works for you below:"

#, python-format
msgid "mail_assembly_service_choose_date_paragraph_2_1_%(duration)s"
msgstr "Our team will arrive on the date you selected at the address you provided. We estimate your assembly service will take approximately %(duration)s hours."

#, python-format
msgid "mail_assembly_service_choose_date_paragraph_2_2_%(date)s_%(time)s"
msgstr "You have until %(time)s on %(date)s to select a date. If you would like a different date, click \"Request New Dates\" below and our team will reach out via email as soon as possible with new options. (Please be aware that the next available slot(s) might be in a week's time, or possibly even later.)"

msgid "mail_assembly_service_choose_date_paragraph_2_3"
msgstr "Feel free to contact us if you have any questions at all."

msgid "mail_assembly_service_choose_date_paragraph_estimated_delivery_date_1_1"
msgstr "Your furniture is being built as we speak, so now is the perfect time to plan your assembly service while your order makes its way through production.<br><br>Please pick a date and time that work for you below:"

msgid "mail_assembly_service_choose_date_preheader"
msgstr "It's time to pick an assembly service date!"

msgid "mail_assembly_service_choose_date_reminder_paragraph_1_1"
msgstr "Hope you’re well! We haven’t heard back from you about scheduling your Tylko assembly service, and wanted to remind you to pick a date. "

msgid "mail_assembly_service_choose_date_reminder_paragraph_1_2"
msgstr "We still have the following slots available:"

msgid "mail_assembly_service_choose_date_reminder_paragraph_2_1"
msgstr "Please choose one that works best for you so we can arrange our expert assembly team to pop by. If you don’t pick one of these dates within the next 24 hours, the available slots may be taken. If none of these dates are suitable, click “Request New Dates” to ask for more options. Please be aware that the next available slots might be in a week's time or possibly even later."

msgid "mail_assembly_service_choose_date_reminder_paragraph_2_2"
msgstr "Any questions in the meantime, please ask."

msgid "mail_assembly_service_choose_date_reminder_preheader"
msgstr "You have 24 hours to choose your ideal date."

msgid "mail_assembly_service_choose_date_reminder_signature"
msgstr "Thanks kindly!<br>The Tylko Team"

msgid "mail_assembly_service_choose_date_reminder_subject"
msgstr "It’s time to schedule your Tylko assembly"

#, python-format
msgid "mail_assembly_service_choose_date_reminder_welcome_%(user)s"
msgstr "Hello again %(user)s,"

msgid "mail_assembly_service_choose_date_signature"
msgstr "Thanks kindly!<br>The Tylko Team"

#, python-format
msgid "mail_assembly_service_choose_date_subject_%(order_id)s"
msgstr "It's time to set assembly service for your order %(order_id)s"

#, python-format
msgid "mail_assembly_service_choose_date_welcome_%(user)s"
msgstr "Hey %(user)s,"

#, python-format
msgid "mail_assembly_service_date_chosen_paragraph_1_1_%(date)s_%(from_hour)s_%(to_hour)s"
msgstr "Thanks for choosing your Tylko assembly date. Our expert team arrives on %(date)s between %(from_hour)s and %(to_hour)s to the address you provided."

msgid "mail_assembly_service_date_chosen_paragraph_1_2"
msgstr "If for some reason this date no longer works, have a chat with us here or call our Service Team at +44 ************ to arrange a better time. "

msgid "mail_assembly_service_date_chosen_preheader"
msgstr "Mark your calendar - your service is set."

msgid "mail_assembly_service_date_chosen_signature"
msgstr "Thanks kindly!<br>The Tylko Team"

msgid "mail_assembly_service_date_chosen_subject"
msgstr "Your Tylko assembly service is set!"

#, python-format
msgid "mail_assembly_service_date_chosen_welcome_%(user)s"
msgstr "Hello %(user)s,"

msgid "mail_assembly_service_estimated_delivery_date_choose_date_preheader"
msgstr "Let's plan your assembly service."

msgid "mail_assembly_service_new_date_request_cta"
msgstr "REQUEST NEW DATES"

msgid "mail_assembly_service_new_date_request_paragraph_1_1"
msgstr "We didn’t hear back from you in time and unfortunately the available slots for your assembly service were taken. No worries though - just click below to generate new available dates, and then pick the one that works best:"

msgid "mail_assembly_service_new_date_request_paragraph_2_1"
msgstr "If you have any questions about your assembly service, feel free to have a chat with us here or give us a ring at +44 ************."

msgid "mail_assembly_service_new_date_request_preheader"
msgstr "It’s time to pick from some new dates..."

msgid "mail_assembly_service_new_date_request_signature"
msgstr "Thanks!<br>The Tylko Team"

msgid "mail_assembly_service_new_date_request_subject"
msgstr "Notice: Your assembly service requires action"

#, python-format
msgid "mail_assembly_service_new_date_request_welcome_%(user)s"
msgstr "Hello again %(user)s."

#, python-format
msgid "mail_assembly_service_question_for_many_dates_paragraph_2_2_%(date)s_%(from_hour)s_%(to_hour)s"
msgstr "%(date)s between %(from_hour)s - %(to_hour)s"

msgid "mail_common_disclaimer"
msgstr "Please note: This promo code does not apply to the Tone Wardrobe and&nbsp;new Tylko Original Modern colours: Premium Matte Black, Sky Blue, Cotton Beige and Burgundy Red. This&nbsp;promo code is limited to one order and can’t be used in combination with&nbsp;another ongoing&nbsp;promotion."

msgid "mail_complaint_created_after_service_ending"
msgstr "Speak soon,<br>Team Tylko"

msgid "mail_complaint_created_after_service_paragraph_1_%(order_id)s"
msgstr "We're sorry to hear everything isn't 100%% perfect with your order. We've logged your issue for order # %(order_id)s and will be in touch soon regarding the next steps to resolve your complaint."

msgid "mail_complaint_created_after_service_paragraph_2"
msgstr "We appreciate your understanding and look forward to making your order right. If you have any questions meanwhile, feel free to contact <NAME_EMAIL>."

msgid "mail_complaint_created_after_service_preheader"
msgstr "Let's get this sorted."

msgid "mail_complaint_created_after_service_subject"
msgstr "Your Tylko order issue has been logged"

msgid "mail_complaint_created_after_service_title_%(user)s"
msgstr "Dear %(user)s,"

msgid "mail_complaint_service_choose_date_paragraph_1_1"
msgstr "We’re ready to schedule your Tylko Support Service. Please pick a date and time that works for you:"

msgid "mail_complaint_service_choose_date_paragraph_2_1_%(date_from)s_%(date_to)s"
msgstr "Our team will arrive on the date you choose between %(date_from)s and %(date_to)s at the address you provided."

msgid "mail_complaint_service_choose_date_paragraph_2_1_%(duration)s"
msgstr "Our team will arrive on the date you choose at the address you provided."

msgid "mail_complaint_service_choose_date_paragraph_2_1_%(from_hour)s_%(to_hour)s"
msgstr "Our team will arrive on the date you choose between %(from_hour)s and %(to_hour)s at the address you provided. We estimate your assembly service will take approximately %(duration)s hours."

#, python-format
msgid "mail_complaint_service_choose_date_paragraph_2_2_%(date)s_%(time)s"
msgstr "Please let us know your preference until %(time)s on %(date)s. If none of these dates are suitable, click “Request new dates”. Please be aware that the next available slots might be in a week's time or possibly even later. "

msgid "mail_complaint_service_choose_date_paragraph_2_3"
msgstr "Got any questions? Feel free to reach out at any time. "

msgid "mail_complaint_service_choose_date_preheader"
msgstr "It'll only take a sec. "

msgid "mail_complaint_service_choose_date_reminder_paragraph_1_1"
msgstr "Since we haven't heard back from you, here's a friendly reminder to set your ideal date for your Tylko Support Service. "

msgid "mail_complaint_service_choose_date_reminder_paragraph_1_2"
msgstr "We still have the following slots available:"

msgid "mail_complaint_service_choose_date_reminder_paragraph_2_1"
msgstr "Please pick an option that works best for you so we can get everything set up. If you don’t pick a date within the next 24 hours, the available slots might be taken. If none of these dates are suitable, please click “Request new dates”. Please be aware that the next available slots might be in a week's time or possibly even later. "

msgid "mail_complaint_service_choose_date_reminder_paragraph_2_2"
msgstr "Got questions? Feel free to reach out at any time.  "

msgid "mail_complaint_service_choose_date_reminder_preheader"
msgstr "You have 24 hours to choose your ideal date."

msgid "mail_complaint_service_choose_date_reminder_signature"
msgstr "Thanks,<br>Team Tylko "

msgid "mail_complaint_service_choose_date_reminder_subject"
msgstr "It’s time to schedule your Tylko Support Service."

#, python-format
msgid "mail_complaint_service_choose_date_reminder_welcome_%(user)s"
msgstr "Hello again %(user)s,"

msgid "mail_complaint_service_choose_date_signature"
msgstr "Thanks,<br>Team Tylko"

msgid "mail_complaint_service_choose_date_subject"
msgstr "Book your Tylko Support Service here. "

#, python-format
msgid "mail_complaint_service_choose_date_welcome_%(user)s"
msgstr "Hey %(user)s,"

#, python-format
msgid "mail_complaint_service_date_chosen_paragraph_1_1_%(date)s_%(from_hour)s_%(to_hour)s"
msgstr "Thanks for choosing your Tylko Support Service date. Our team of friendly experts will arive on %(date)s between %(from_hour)s and %(to_hour)s to the address you provided."

msgid "mail_complaint_service_date_chosen_paragraph_1_2"
msgstr "If for some reason this date no longer works, you can chat with us here or call our Service Team at +44 ************ to arrange a better time."

msgid "mail_complaint_service_date_chosen_preheader"
msgstr "Your Tylko Support Service is confirmed. "

msgid "mail_complaint_service_date_chosen_signature"
msgstr "Thanks,<br>Team Tylko "

msgid "mail_complaint_service_date_chosen_subject"
msgstr "Your Tylko Support Service is all set. "

#, python-format
msgid "mail_complaint_service_date_chosen_welcome_%(user)s"
msgstr "Hello %(user)s,"

msgid "mail_complaint_service_new_date_request_cta"
msgstr "REQUEST NEW DATES"

msgid "mail_complaint_service_new_date_request_paragraph_1_1"
msgstr "We didn’t hear back from you in time and the available slots for your Tylko Support Service have been taken. No worries: just click below to get new available dates and pick the one that works best for you:"

msgid "mail_complaint_service_new_date_request_paragraph_2_1"
msgstr "If you have any questions about your Tylko Support Service, feel free to chat with us here or give us a ring at +44 ************."

msgid "mail_complaint_service_new_date_request_preheader"
msgstr "Here are some new available dates. "

msgid "mail_complaint_service_new_date_request_signature"
msgstr "Thanks,<br>Team Tylko"

msgid "mail_complaint_service_new_date_request_subject"
msgstr "Notice: your Tylko Support Service still isn't booked."

#, python-format
msgid "mail_complaint_service_new_date_request_welcome_%(user)s"
msgstr "Hello again %(user)s,"

#, python-format
msgid "mail_complaint_service_question_for_many_dates_paragraph_2_2_%(date)s_%(from_hour)s_%(to_hour)s"
msgstr "%(date)s between %(from_hour)s - %(to_hour)s"

msgid "mail_complaints_tbs_v1_body"
msgstr "Just a quick message to let you know that your Tylko replacement is ready to go. It will ship within the next business day, so keep an eye on your inbox for a follow-up message from us with your tracking details.<br><br>If you have any questions in the meantime, give us a call us on +44 ************ or simply reply to this mail."

msgid "mail_complaints_tbs_v1_headline"
msgstr "Ready, set...ship!"

msgid "mail_complaints_tbs_v1_preheader"
msgstr "We’re prepping your shipment as we speak..."

msgid "mail_complaints_tbs_v1_signature"
msgstr "Speak Soon,<br>The Tylko Team"

msgid "mail_complaints_tbs_v1_subject"
msgstr "Your Tylko replacement is ready to go"

#, python-format
msgid "mail_complaints_tbs_v1_welcome_%(user)s"
msgstr "Hi %(user)s,"

msgid "mail_complaints_tbs_v2_body"
msgstr "Great news - your Tylko replacement is ready to go!<br><br>If you’re unavailable to accept delivery in the next few days, please let us know right away so we can schedule your shipment for a time that works better for you. <br><br>If you are available for delivery in the next few days, there’s no need to do anything further. We’ll automatically ship your Tylko out in 48 hours.<br><br>If you have any questions, give us a call us on +44 ************ or simply reply to this mail. We’re here to help."

msgid "mail_complaints_tbs_v2_headline"
msgstr "Ready for your Tylko?"

msgid "mail_complaints_tbs_v2_preheader"
msgstr "We’re prepping your shipment. Will you be home?"

msgid "mail_complaints_tbs_v2_signature"
msgstr "Speak Soon,<br>The Tylko Team"

msgid "mail_complaints_tbs_v2_subject"
msgstr "Your Tylko replacement is ready!"

#, python-format
msgid "mail_complaints_tbs_v2_welcome_%(user)s"
msgstr "Hi %(user)s,"

msgid "mail_correction_after_free_return_klarna_paragraph_1_0_%(order)s_%(order_items_id)s"
msgstr "Great news: your return from order number %(order)s (Item ID: %(order_items_id)s) has been picked up, which means we can now process your refund."

msgid "mail_correction_after_free_return_klarna_paragraph_2_0"
msgstr "Your refund will be resolved directly through Klarna, which can take up to 10 working days. You'll find a corrected invoice attached below."

msgid "mail_correction_after_free_return_klarna_paragraph_3_0"
msgstr "We look forward to helping you find your next piece of perfect fit furniture in the future, and remain at your disposal should you need any further assistance at all."

msgid "mail_correction_after_free_return_klarna_preheader"
msgstr "Your return is processing."

msgid "mail_correction_after_free_return_klarna_signature"
msgstr "All the best,<br>The Tylko Team"

msgid "mail_correction_after_free_return_klarna_subject_%(order)s"
msgstr "Your free Tylko return for order #%(order)s"

msgid "mail_correction_after_free_return_klarna_welcome_%(user)s"
msgstr "Hello %(user)s,"

msgid "mail_correction_after_free_return_normal_paragraph_1_0_%(order)s_%(order_items_id)s"
msgstr "Great news: your return from order number %(order)s (Item ID: %(order_items_id)s) has been picked up, which means we can now process your refund."

msgid "mail_correction_after_free_return_normal_paragraph_2_0"
msgstr "Your refund will be returned to the account used for payment within 10 working days, and you'll find a corrected invoice attached below."

msgid "mail_correction_after_free_return_normal_paragraph_3_0"
msgstr "We look forward to helping you find your next piece of perfect fit furniture in the future, and remain at your disposal should you need any further assistance at all."

msgid "mail_correction_after_free_return_normal_preheader"
msgstr "Your return is processing."

msgid "mail_correction_after_free_return_normal_signature"
msgstr "All the best,<br>The Tylko Team"

msgid "mail_correction_after_free_return_normal_subject_%(order)s"
msgstr "Your free Tylko return for order #%(order)s"

msgid "mail_correction_after_free_return_normal_welcome_%(user)s"
msgstr "Hello %(user)s,"

msgid "mail_footer_free_return"
msgstr "100 days of free returns"

msgid "mail_footer_free_shipping"
msgstr "Free shipping"

msgid "mail_free_return_complaint_choose_date_body_1"
msgstr "We're ready to arrange pick up for your Tylko return. Your pick up date is as follows:"

msgid "mail_free_return_complaint_choose_date_body_2"
msgstr "Please be sure your Tylko is ready for transport, cleared of all your decor and that our team has clear access to the furniture so they can disassemble and remove it. Make sure any obstacles (like that expensive vase or designer lamp in the hallway, for example) are tucked safely out of the way to prevent any damage, too."

msgid "mail_free_return_complaint_choose_date_body_3_%(date)s_%(time)s"
msgstr "You have until %(date)s %(time)s to confirm your date and lock in your return pick up. Need a different time or day? Just request a new date using the button below:"

msgid "mail_free_return_complaint_choose_date_body_4"
msgstr "Please be aware that your new date could be in a week or possibly later, depending on our team's availability."

msgid "mail_free_return_complaint_choose_date_body_5"
msgstr "Any questions in the meantime, just reach out and ask."

msgid "mail_free_return_complaint_choose_date_body_ending"
msgstr "Thanks kindly,<br>\nThe Tylko Team"

msgid "mail_free_return_complaint_choose_date_hello_%(user)s"
msgstr "Hello %(user)s,"

msgid "mail_free_return_complaint_choose_date_pre_header"
msgstr "Important info about your return inside."

msgid "mail_free_return_complaint_choose_date_subject"
msgstr "Your Tylko return: confirm your pick up date"

msgid "mail_journal_entry_1_link"
msgstr "Check out our three simple tips"

msgid "mail_journal_entry_1_text"
msgstr "Want some tips on making bold colour work for you?"

msgid "mail_journal_entry_2_link"
msgstr "Check out Studio UAU’s stunning sculptures"

msgid "mail_journal_entry_2_text"
msgstr "Or check out this creative studio making magic with 3D printed objects:"

msgid "mail_system_password_change_buton_1"
msgstr "Reset Password"

msgid "mail_system_password_change_headline_1"
msgstr "Let's get your new<br>password sorted!"

msgid "mail_system_password_change_paragraph_1"
msgstr "It happens, no worries. Just click below to get your new password:"

msgid "mail_system_password_change_paragraph_2"
msgstr "The link's only valid once and expires 3 hours after this email was sent. If you changed your mind and don’t want a new password, just ignore this email."

msgid "mail_system_password_change_preheader_1"
msgstr "Here's how to reset it."

msgid "mail_system_password_change_subject_line_1"
msgstr "Pssst...get your new password!"

msgid "mail_system_welcome_desktop_buton_1"
msgstr "Get started"

msgid "mail_system_welcome_desktop_new_buton_1"
msgstr "mail_system_welcome_desktop_new_buton_1"

msgid "mail_system_welcome_desktop_new_buton_2"
msgstr "Learn more."

msgid "mail_system_welcome_desktop_new_buton_3"
msgstr "here."

msgid "mail_system_welcome_desktop_new_buton_4"
msgstr "here."

msgid "mail_system_welcome_desktop_new_header_1_1"
msgstr "Now let us show you the secret handshake..."

msgid "mail_system_welcome_desktop_new_header_2_1"
msgstr "mail_system_welcome_desktop_new_header_2_1"

msgid "mail_system_welcome_desktop_new_header_2_2"
msgstr "Free Delivery and Returns"

msgid "mail_system_welcome_desktop_new_header_2_3"
msgstr "Easy Assembly"

msgid "mail_system_welcome_desktop_new_header_2_4"
msgstr "Premium Quality"

msgid "mail_system_welcome_desktop_new_paragraph_1_1"
msgstr "Ok, so there isn't a handshake. But when you registered with Tylko you joined a great group of savvy shoppers who know what to look for when shopping for furniture:"

msgid "mail_system_welcome_desktop_new_paragraph_2_1"
msgstr "mail_system_welcome_desktop_new_paragraph_2_1"

msgid "mail_system_welcome_desktop_new_paragraph_2_2"
msgstr "Let us deliver right to your door. If you're not 100% happy, we’ll pick it up again from the same place with our 100-day free returns guarantee."

msgid "mail_system_welcome_desktop_new_paragraph_2_3"
msgstr "Buying furniture should be a pleasure, not a pain. Snap together assembly and no tools required? Check. See how easy it is"

msgid "mail_system_welcome_desktop_new_paragraph_2_4"
msgstr "Strong and sturdy, our shelves never sag or sway. Check our specs"

msgid "mail_system_welcome_desktop_new_paragraph_3_1"
msgstr "Now that you've registered with Tylko, why not take the next step and get creating? We can't wait to see what you come up with!"

msgid "mail_system_welcome_desktop_new_preheader_1"
msgstr "Welcome to the Tylko family - we promise to never make you pose for awkward photos."

msgid "mail_system_welcome_desktop_new_subject_1"
msgstr "Thanks for registering with Tylko - we love our newbies!"

#, python-format
msgid "mail_transaction_correction_invoice_attached_subject_1%(order_pretty_id)s"
msgstr "The correction invoice for your recent order %(order_pretty_id)s"

msgid "mail_transaction_correction_invoice_attached_text_1_1"
msgstr "In the attachment below, you’ll find your corrected invoice. Please check if the details on the invoice are correct, and in the event of error contact our customer service by replying to this email.<br><br>Please read below to learn how to proceed in the case of additional payment due (A) or a refund (B)."

msgid "mail_transaction_correction_invoice_attached_text_1_2"
msgstr "<strong>A. Additional payment</strong>"

msgid "mail_transaction_correction_invoice_attached_text_1_3"
msgstr "<strong>Please transfer your payment to the following bank account:</strong>"

#, python-format
msgid "mail_transaction_correction_invoice_attached_text_1_4_%(iban)s"
msgstr "Pekao SA<br>IBAN: %(iban)s<br>SWIFT: PKOPPLPW<br><br>We will confirm your payment within 10 business days of receipt. To help speed up the process, we kindly ask you to provide us with a copy of your payment confirmation."

msgid "mail_transaction_correction_invoice_attached_text_2_1"
msgstr "<strong>B. Refund</strong>"

msgid "mail_transaction_correction_invoice_attached_text_2_2"
msgstr "If you are due a refund, it will be processed automatically and issued to the original method of payment. We process refunds once a week, so please be patient and allow up to 14 working days for your refund to appear."

msgid "mail_transaction_klarna_new_invoice_attached_header_2_1"
msgstr "Please keep this message for your records."

msgid "mail_transaction_klarna_new_invoice_attached_header_3_1%(owner_name)s"
msgstr "Hello Again %(owner_name)s,"

msgid "mail_transaction_klarna_new_invoice_attached_text_1_1_%(order_id)s"
msgstr "Please find attached your invoice for Tylko order number %(order_id)s."

msgid "mail_transaction_klarna_new_invoice_attached_text_2_1"
msgstr "Any questions? Use the contact form <a href=\"https://tylko.com/contact/\">here</a> to get in touch, or email <NAME_EMAIL>."

msgid "mail_transaction_klarna_new_invoice_attached_text_3_1"
msgstr "Thanks kindly,<br>Team Tylko"

msgid "mail_transaction_klarna_payment_confirmation_header_1_1"
msgstr "Your invoice is attached."

msgid "mail_transaction_new_bank_transfer_reminder_buton_1"
msgstr "Finalise the order"

msgid "mail_transaction_new_bank_transfer_reminder_buton_2"
msgstr "FAQs."

#, python-format
msgid "mail_transaction_new_bank_transfer_reminder_header_1_1_%(name)s"
msgstr "Hi %(name)s,"

msgid "mail_transaction_new_bank_transfer_reminder_paragraph_1_1"
msgstr "You recently placed an order with Tylko using a bank transfer as your method of payment. As of this moment, we've not received a payment confirmation from your bank. We know it can sometimes take a few working days before the confirmation appears, so if you've already finalised your order, please reply to this email and attach your bank's confirmation receipt so we can pass your order along to our production team right away."

msgid "mail_transaction_new_bank_transfer_reminder_paragraph_2_1"
msgstr "If you haven't finalised your order yet, there's still time to do it. You can choose via bank transfer or any of the payment methods we offer on our site:"

msgid "mail_transaction_new_bank_transfer_reminder_paragraph_3_1"
msgstr "If you have any questions at all, we're happy to help. Just reply to this email, or check out our "

msgid "mail_transaction_new_bank_transfer_reminder_paragraph_4_1"
msgstr "<br><br>Thanks again for choosing Tylko - we can’t wait to get your order built!"

msgid "mail_transaction_new_bank_transfer_reminder_paragraph_5_1"
msgstr "Kindest Regards,<br>The Tylko Team"

msgid "mail_transaction_new_bank_transfer_reminder_subject_line"
msgstr "About your Tylko order bank transfer..."

msgid "mail_transaction_new_dedicated_transport_UK_text_1_1"
msgstr "Great news - your Tylko order is ready to ship! The final step is to let us know when you’ll be available to accept delivery. Please let us know which (minimum three-hour) time slots between 6am and 11pm you’ll be available on the following days:"

msgid "mail_transaction_new_dedicated_transport_UK_text_3_1"
msgstr "Please indicate all the possible timeframes on your preferred day, and we ask that you also choose at least one additional time-frame on another day for 'just in case'. Our logistics team will then book a courier that will deliver your order, and we'll send you another email to confirm the exact time and date to expect your order's arrival at least two days in advance."

#, python-format
msgid "mail_transaction_new_dedicated_transport_header_1_1_%(name)s"
msgstr "Hi %(name)s,"

msgid "mail_transaction_new_dedicated_transport_sender"
msgstr "Tylko"

msgid "mail_transaction_new_dedicated_transport_text_1_1"
msgstr "Great news - your Tylko order is ready to ship! The final step is letting us know when you’ll be able to accept delivery. Please let us know what times (between 6am and 11pm) you’ll be available on the following days:"

msgid "mail_transaction_new_dedicated_transport_text_2_1"
msgstr "DAY"

msgid "mail_transaction_new_dedicated_transport_text_2_2"
msgstr "TIME FRAME"

msgid "mail_transaction_new_dedicated_transport_text_3_1"
msgstr "Please indicate three or more possible days (with at least three-hour time slots) that would work for you, and once you’ve selected at least three time frames you’re available we’ll arrange delivery with our courier. We’ll then send you another email to confirm the exact time and date you can expect your order."

msgid "mail_transaction_new_dedicated_transport_text_4_1"
msgstr "Thanks Kindly,<br>Team Tylko"

msgid "mail_transaction_new_failed_payment_buton_1"
msgstr "Retry your order"

msgid "mail_transaction_new_failed_payment_buton_2"
msgstr "+44 ************"

msgid "mail_transaction_new_failed_payment_buton_3"
msgstr "+49 0800 0010484"

msgid "mail_transaction_new_failed_payment_header"
msgstr "Let’s sort your payment out."

#, python-format
msgid "mail_transaction_new_failed_payment_header_1_1_%(name)s"
msgstr "Hello %(name)s,"

msgid "mail_transaction_new_failed_payment_paragraph_1_1"
msgstr "It seems your recent order from tylko.com isn't working as it should. Sometimes payments don't go through, and it’s normally for one of the following reasons:"

msgid "mail_transaction_new_failed_payment_paragraph_1_2_1"
msgstr "<li><strong style=\"font-weight: 500;color:#000;\">Card Error</strong><ul style=\"list-style-type: disc; padding: 5px 0 5px 20px;\"><li>Please check your card number, name, expiration date and the CVV carefully. One mistyped number and it doesn’t work!</li></ul><li><strong style=\"font-weight: 500;color:#000;\">Balance Exceeded</strong><ul style=\"list-style-type: disc; padding: 5px 0 5px 20px;\"><li>Please check with your bank regarding your daily transaction limit and to confirm you have sufficient balance.</li></ul><li><strong style=\"font-weight: 500;color:#000;\">Banking Issues</strong><ul style=\"list-style-type: disc; padding: 5px 0 5px 20px;\"><li>Please check with your bank that international transfers are allowed or if there is any other cause for the payment to be denied.</li></ul></li>"

msgid "mail_transaction_new_failed_payment_paragraph_1_2_2"
msgstr "<li><strong style=\"font-weight: 500;color:#000;\">Card Error</strong><ul style=\"list-style-type: disc; padding: 5px 0 5px 20px;\"><li>Please check your card number, name, expiration date and the CVV carefully. One mistyped number and it doesn’t work!</li></ul><li><strong style=\"font-weight: 500;color:#000;\">Balance Exceeded</strong><ul style=\"list-style-type: disc; padding: 5px 0 5px 20px;\"><li>Please check with your bank regarding your daily transaction limit and to confirm you have sufficient balance.</li><li>We now work with Klarna to arrange easy 24-month installment plans so you can have your shelves now and pay later!</li></ul><li><strong style=\"font-weight: 500;color:#000;\">Banking Issues</strong><ul style=\"list-style-type: disc; padding: 5px 0 5px 20px;\"><li>Please check with your bank that international transfers are allowed or if there is any other cause for the payment to be denied.</li></ul></li>"

msgid "mail_transaction_new_failed_payment_paragraph_1_3"
msgstr "There are a few different ways to pay, so click below and chose the best method for you: "

msgid "mail_transaction_new_failed_payment_paragraph_2_1"
msgstr "We're here to help - if the order still isn't working, just let us know!"

msgid "mail_transaction_new_failed_payment_paragraph_2_2"
msgstr "We now work with Klarna, so we can even offer you an easy 24-month payment plan for buy now, pay later simplicity."

msgid "mail_transaction_new_failed_payment_paragraph_2_3"
msgstr "You can reply to this email or give us a call at"

msgid "mail_transaction_new_failed_payment_paragraph_2_4"
msgstr " or "

msgid "mail_transaction_new_failed_payment_paragraph_2_5"
msgstr ", and we'll walk you through it."

msgid "mail_transaction_new_failed_payment_paragraph_3_1"
msgstr "Thanks Kindly!<br>Team Tylko"

msgid "mail_transaction_new_failed_payment_preheader"
msgstr "Open up to complete your order."

#, python-format
msgid "mail_transaction_new_failed_payment_subject_line_%(order_pretty_id)s"
msgstr "Order %(order_pretty_id)s update: your payment didn’t go through. "

msgid "mail_transaction_new_invoice_attached_header_1_1"
msgstr "Your invoice is attached."

msgid "mail_transaction_new_invoice_attached_header_2_1"
msgstr "Please keep this message for your records."

msgid "mail_transaction_new_invoice_attached_header_3_1"
msgstr "Hello Again"

msgid "mail_transaction_new_invoice_attached_preheader_1"
msgstr "Thanks for choosing Tylko!"

#, python-format
msgid "mail_transaction_new_invoice_attached_subject_1_%(order_pretty_id)s"
msgstr "Your recent order  #  %(order_pretty_id)s invoice."

msgid "mail_transaction_new_invoice_attached_text_1_1"
msgstr "Please find attached your invoice for order number "

msgid "mail_transaction_new_invoice_attached_text_1_2"
msgstr ". Meanwhile, we’re busily working on the details of your order."

msgid "mail_transaction_new_invoice_attached_text_2_1"
msgstr "Keep an eye out for any updates on delivery times – we’ll let you know the minute your order status changes and is ready to ship!<br><br>Any questions? Use the easy contact form <a href=\"https://tylko.com/contact/\">here</a> to get in touch."

msgid "mail_transaction_new_invoice_attached_text_3_1"
msgstr "Thanks Kindly!<br>Team Tylko"

msgid "mail_transaction_new_order_placed_buton_1"
msgstr "View full order details"

msgid "mail_transaction_new_order_placed_buton_2"
msgstr " +44 ************"

msgid "mail_transaction_new_order_placed_buton_3"
msgstr " +49 0800 0010484"

msgid "mail_transaction_new_order_placed_header_1_1"
msgstr "We’re standing by to start building."

msgid "mail_transaction_new_order_placed_header_2_1"
msgstr "Once we receive payment, we’ll get building."

msgid "mail_transaction_new_order_placed_header_3_1"
msgstr "Hi there"

msgid "mail_transaction_new_order_placed_header_3_2"
msgstr "Your order:"

msgid "mail_transaction_new_order_placed_preheader_1"
msgstr "As soon as payment completes, we’ll get started!"

msgid "mail_transaction_new_order_placed_sample_set_header_2_1"
msgstr "Almost there – we just need to receive your payment!"

#, python-format
msgid "mail_transaction_new_order_placed_subject_1_%(order_pretty_id)s"
msgstr "Your Tylko order # %(order_pretty_id)s"

msgid "mail_transaction_new_order_placed_text_1_1"
msgstr "Your order number"

msgid "mail_transaction_new_order_placed_text_1_2"
msgstr " has been submitted. As soon as payment is complete, our team will begin processing your order so we can get it to you as soon as possible. "

msgid "mail_transaction_new_order_placed_text_2_1"
msgstr "Thanks again for choosing Tylko - we can’t wait to get started on your order!"

msgid "mail_transaction_new_order_placed_text_3_1"
msgstr "Any questions or concerns? Get in touch with <a href=\"https://tylko.com/contact/\" style=\"color: #FF3C00; text-decoration: none;\">this form</a>."

msgid "mail_transaction_new_order_placed_text_3_1_1"
msgstr " or"

msgid "mail_transaction_new_order_placed_text_3_1_sample"
msgstr "Any questions or concerns? Get in touch with <a href=\"https://tylko.com/contact/\" style=\"color: #FF3C00; text-decoration: none;\">this form</a>."

msgid "mail_transaction_new_order_placed_text_3_2"
msgstr ", or just reply to this email."

msgid "mail_transaction_new_order_placed_text_4_1"
msgstr "All The Best,<br>Team Tylko"

msgid "mail_transaction_new_order_placed_text_4_1_sample"
msgstr "All The Best,<br>Team Tylko"

msgid "mail_transaction_new_order_placed_text_sample_1_2"
msgstr " has been received! As soon as your payment is complete, our team will begin processing your order so we can get it to you as soon as possible. You can easily track the status by clicking <a href=\"https://tylko.com/contact/?topic=order_status\" style=\"color: #FF3C00; text-decoration: none;\">here</a>."

msgid "mail_transaction_new_payment_confirmation_RAF_buton_payment_1"
msgstr "Start referring friends"

#, python-format
msgid "mail_transaction_new_payment_confirmation_RAF_text_3_1_%(amount)s_%(value)s"
msgstr "Want to earn a full refund for your order?<br><br>With our referral program, the more friends you tell (and who place an order), the more cash you can earn. For every referred friend who places and keeps an order you can <span style=\"color: #FF3C00;\">earn %(amount)s back</span> - and they’ll get %(value)s off their order, too. Win-win!"

msgid "mail_transaction_new_payment_confirmation_header_1_1"
msgstr "We're ready for you now"

msgid "mail_transaction_new_payment_confirmation_header_3_1"
msgstr "Hi There "

msgid "mail_transaction_new_payment_confirmation_headline_1"
msgstr "We're ready to start building"

msgid "mail_transaction_new_payment_confirmation_headline_2"
msgstr "We've received your payment— now for the fun part."

msgid "mail_transaction_new_payment_confirmation_paragraph_1_%(order_id)s"
msgstr "We've just received payment for your order number: %(order_id)s."

msgid "mail_transaction_new_payment_confirmation_paragraph_2_%(delivery_range)s"
msgstr "Your furniture will now be made just for you! At the moment, it looks like your order will be ready for delivery between %(delivery_range)s.<br>As soon as your furniture is packed and ready, we'll get in touch to schedule a specific delivery day."

msgid "mail_transaction_new_payment_confirmation_paragraph_3"
msgstr "If your order is ready sooner we'll let you know right away, too."

msgid "mail_transaction_new_payment_confirmation_preheader"
msgstr "Time to make some furniture..."

msgid "mail_transaction_new_payment_confirmation_preheader_1"
msgstr "Now we can get to work."

#, python-format
msgid "mail_transaction_new_payment_confirmation_sample_set_text_1_2_%(date)s"
msgstr "Thanks so much! At the moment, it looks like your order will be ready on %(date)s and handed over to the shipping company right after that."

msgid "mail_transaction_new_payment_confirmation_sample_set_text_1_3"
msgstr "If your order is going to be ready any sooner we’ll let you know right away!"

msgid "mail_transaction_new_payment_confirmation_signature"
msgstr "Speak soon,<br>Team Tylko"

msgid "mail_transaction_new_payment_confirmation_subject_%(order_pretty_id)s"
msgstr "Order #%(order_pretty_id)s: Thanks for your payment!"

msgid "mail_transaction_new_payment_confirmation_text_1_1"
msgstr "We just received payment for your order number "

msgid "mail_transaction_new_payment_confirmation_text_1_1_2"
msgstr ". "

#, python-format
msgid "mail_transaction_new_payment_confirmation_text_1_2_%(delivery_range)s"
msgstr "Thanks so much! At the moment, it looks like your order will be built between %(delivery_range)s."

msgid "mail_transaction_new_payment_confirmation_text_1_3"
msgstr "As each and every piece is custom built, it takes a little longer than one-size-fits-all furniture to create. If your order looks like it might be built sooner (or in very rare cases, a little later) we’ll let you know right away.<br><br>You can easily track your order status by clicking <a href=\"https://tylko.com/contact/?topic=order_status\">here</a>.<br><br>Once your order is ready, we’ll contact you to arrange delivery, too. "

#, python-format
msgid "mail_transaction_new_payment_confirmation_text_1_with_%(order)s"
msgstr "We just received payment for your order number %(order)s."

msgid "mail_transaction_new_payment_confirmation_text_4_1"
msgstr "We’ll keep in touch as your order progresses. If you have any last-minute changes you want to make, contact us <a href=\"https://tylko.com/contact/\">here</a> with the easy contact form."

msgid "mail_transaction_new_payment_confirmation_text_5_1"
msgstr "Speak soon,<br>Team Tylko"

msgid "mail_transaction_new_payment_confirmation_welcome_%(user)s"
msgstr "Hi %(user)s,"

msgid "mail_transaction_new_payment_reminder_buton_2"
msgstr "<EMAIL>"

msgid "mail_transaction_new_payment_reminder_buton_3"
msgstr "+44 ************"

msgid "mail_transaction_new_payment_reminder_buton_5"
msgstr "Finalise order"

msgid "mail_transaction_new_payment_reminder_header_1_1"
msgstr "Let’s finish up this order."

msgid "mail_transaction_new_payment_reminder_header_3_1"
msgstr "Hello Again"

msgid "mail_transaction_new_payment_reminder_preheader_1"
msgstr "Open up to finish up your order."

#, python-format
msgid "mail_transaction_new_payment_reminder_subject_1_%(order_pretty_id)s"
msgstr "Heads up: your order %(order_pretty_id)s is incomplete."

msgid "mail_transaction_new_payment_reminder_text_1_1"
msgstr "You recently placed an order with Tylko, but it seems the payment was never finalised. Is there anything we can help you with? We’re ready to get your order completed, so contact us directly at "

msgid "mail_transaction_new_payment_reminder_text_2_2"
msgstr "or give us a call on "

msgid "mail_transaction_new_payment_reminder_text_3_1"
msgstr "Once you finalise your piece, we’ll start building right away."

msgid "mail_transaction_new_payment_reminder_text_5_1"
msgstr "All The Best,<br>Team Tylko"

msgid "mail_transaction_new_product_manufactured0_button_1"
msgstr "here"

msgid "mail_transaction_new_product_manufactured0_button_2"
msgstr "Get inspired"

msgid "mail_transaction_new_product_manufactured0_button_3"
msgstr "form"

msgid "mail_transaction_new_product_manufactured0_header_1"
msgstr "Hi"

msgid "mail_transaction_new_product_manufactured0_headline_1"
msgstr "Your order is progressing nicely"

msgid "mail_transaction_new_product_manufactured0_preheader_1"
msgstr "Your friendly status update."

#, python-format
msgid "mail_transaction_new_product_manufactured0_subject_1_%(order_pretty_id)s"
msgstr "About your recent Tylko order number %(order_pretty_id)s..."

#, python-format
msgid "mail_transaction_new_product_manufactured0_text_1_1_%(weeks)s"
msgstr "Your Tylko is still currently in production and will take approximately %(weeks)s weeks to be completed."

#, python-format
msgid "mail_transaction_new_product_manufactured0_text_1_2_%(delivery_range)s"
msgstr "Your estimated delivery date is between %(delivery_range)s."

msgid "mail_transaction_new_product_manufactured0_text_2_1"
msgstr "We don’t keep piles of stock in a warehouse - your furniture is being made especially for you. While we’re busy with that, why don’t you hit up our Instagram feed to see some Tylko furniture in real spaces to inspire you?"

msgid "mail_transaction_new_product_manufactured0_text_3_1"
msgstr "Or while we work on your order, you can check out the Tylko Journal for some really great ideas for your interior. From pro tips on small space living to expert advice on clutter calming, there’s something for everyone to help create a lifestyle to love."

msgid "mail_transaction_new_product_manufactured0_text_4_1"
msgstr "Any questions or concerns? Get in touch with this"

msgid "mail_transaction_new_product_manufactured0_text_5_1"
msgstr "Speak soon,<br>Team Tylko"

msgid "mail_transaction_new_product_manufacturedA_button_1"
msgstr "here"

msgid "mail_transaction_new_product_manufacturedA_button_2"
msgstr "Take a peek"

msgid "mail_transaction_new_product_manufacturedA_button_3"
msgstr "form"

msgid "mail_transaction_new_product_manufacturedA_header_1"
msgstr "Hello"

msgid "mail_transaction_new_product_manufacturedA_headline_1"
msgstr "The wheels are in motion"

msgid "mail_transaction_new_product_manufacturedA_preheader_1"
msgstr "Just a quick update on your order…"

msgid "mail_transaction_new_product_manufacturedA_subject_1"
msgstr "It’s looking good!"

#, python-format
msgid "mail_transaction_new_product_manufacturedA_text_1_1_%(order_id)s"
msgstr "We're excited to tell you that we've started producing your order number %(order_id)s."

#, python-format
msgid "mail_transaction_new_product_manufacturedA_text_1_2_%(delivery_range)s"
msgstr "Your estimated date of delivery is between %(delivery_range)s."

msgid "mail_transaction_new_product_manufacturedA_text_2_1"
msgstr "Right now, we’re building your one-of-a-kind furniture. As we make each piece individually, this takes a little time - but we think the results are worth it!"

msgid "mail_transaction_new_product_manufacturedA_text_2_2"
msgstr "."

msgid "mail_transaction_new_product_manufacturedA_text_3_1"
msgstr "While we’re busy working on your order, maybe you’d like to peek behind the scenes at Tylko. From the design geniuses who make our configurator flawless, to the detail-obsessed production managers in our factory - all have a fascinating story!"

msgid "mail_transaction_new_product_manufacturedA_text_4_1"
msgstr "Thanks again for shopping with Tylko. As soon as your order has been triple-checked, we’ll carefully pack it up and send it to your address. If you have any questions in the meantime, give us a call at "

msgid "mail_transaction_new_product_manufacturedA_text_5_1"
msgstr "Any questions or concerns? Get in touch with this"

msgid "mail_transaction_new_product_manufacturedA_text_6_1"
msgstr "Speak soon,<br>Team Tylko"

msgid "mail_transaction_new_product_manufacturedB_button_1"
msgstr "here"

msgid "mail_transaction_new_product_manufacturedB_button_2"
msgstr "form"

msgid "mail_transaction_new_product_manufacturedB_header_1"
msgstr "Hello"

msgid "mail_transaction_new_product_manufacturedB_headline_1"
msgstr "Let's stay connected"

msgid "mail_transaction_new_product_manufacturedB_preheader_1"
msgstr "You are part of something bigger, and we’d love it if you came over and said hello."

msgid "mail_transaction_new_product_manufacturedB_subject_1"
msgstr "There’s more just like you."

#, python-format
msgid "mail_transaction_new_product_manufacturedB_text_1_1_%(order_id)s"
msgstr "We’ve checked on your order number %(order_id)s"

#, python-format
msgid "mail_transaction_new_product_manufacturedB_text_1_2_%(delivery_range)s"
msgstr "and you can expect your product to be delivered between %(delivery_range)s. Hold tight! "

msgid "mail_transaction_new_product_manufacturedB_text_2_1"
msgstr "Whether you have a question, suggestion, or just want to chat about design, our social media channels are the best place to get connected with us and the Tylko community worldwide. Why not come and say hello?"

msgid "mail_transaction_new_product_manufacturedB_text_2_2"
msgstr "."

msgid "mail_transaction_new_product_manufacturedB_text_3_1"
msgstr "Join us"

msgid "mail_transaction_new_product_manufacturedB_text_4_1"
msgstr "Follow us"

msgid "mail_transaction_new_product_manufacturedB_text_5_1"
msgstr "See our photos"

msgid "mail_transaction_new_product_manufacturedB_text_6_1"
msgstr "Get inspired"

msgid "mail_transaction_new_product_manufacturedB_text_7_1"
msgstr "Speak Soon,<br>Team Tylko"

msgid "mail_transaction_new_product_manufacturedB_text_8_1"
msgstr "Any questions or concerns? Get in touch with this"

msgid "mail_transaction_new_product_manufacturedB_text_9_1"
msgstr "Speak Soon,<br>Team Tylko"

msgid "mail_transaction_new_product_shipped_button_1"
msgstr "Track your package"

msgid "mail_transaction_new_product_shipped_button_1_ups"
msgstr "Track your package"

msgid "mail_transaction_new_product_shipped_button_2"
msgstr "+44 ************"

msgid "mail_transaction_new_product_shipped_button_3"
msgstr "+49 0800 0010484"

msgid "mail_transaction_new_product_shipped_header_1_1"
msgstr "Ship ship...hooray!"

msgid "mail_transaction_new_product_shipped_header_2_1"
msgstr "Your order is on the way!"

msgid "mail_transaction_new_product_shipped_header_3_1"
msgstr "Hello "

msgid "mail_transaction_new_product_shipped_header_4_1"
msgstr "To help you pass the time:"

msgid "mail_transaction_new_product_shipped_preheader_1"
msgstr "Coming soon to a front door near you..."

msgid "mail_transaction_new_product_shipped_subject_1"
msgstr "Your order has been shipped!"

msgid "mail_transaction_new_product_shipped_text_1_1"
msgstr "That’s right! Your order number "

msgid "mail_transaction_new_product_shipped_text_2_1"
msgstr " has been shipped with TNT. You can follow its progress every step of the way with tracking number "

msgid "mail_transaction_new_product_shipped_text_2_1_1"
msgstr "mail_transaction_new_product_shipped_text_2_1_1"

msgid "mail_transaction_new_product_shipped_text_2_1_dpd"
msgstr " has been shipped with DPD. You can follow its progress every step of the way with tracking number"

msgid "mail_transaction_new_product_shipped_text_2_1_fedex"
msgstr " has been shipped with FEDEX. You can follow its progress every step of the way with tracking number"

msgid "mail_transaction_new_product_shipped_text_2_1_ups"
msgstr "has been shipped with UPS. You can follow its progress every step of the way with tracking number"

msgid "mail_transaction_new_product_shipped_text_2_2"
msgstr "If&nbsp;necessary, the courier may contact you directly to arrange delivery, so keep an eye out."

msgid "mail_transaction_new_product_shipped_text_3_1_klarna"
msgstr "Please be aware that if you chose to pay with Klarna, you will receive a payment link soon. "

msgid "mail_transaction_new_product_shipped_text_3_1_uk"
msgstr "Important: Due to Brexit regulations, the packages in your shipment may be separated at the border and undergo customs control. In some instances, it's possible that they may incur delays of 2-5 days and be delivered at different times. For more information about the status of your order, we suggest contacting the courier company directly. You can also contact us at <a href=\"mailto:<EMAIL>\" style=\"text-decoration: underline;color:#FF3C00;\"><EMAIL></a> and we'll share any relevant updates as soon as we're informed."

msgid "mail_transaction_new_product_shipped_text_3_1_ups_uk"
msgstr "Important: due to Brexit regulations, parts of your orders may be separated at the border and undergo longer customs control. Therefore, it's possible that they get delivered at different times, with delays of 2-5 days. For more information about the status of your order, we suggest getting in touch with the courier company directly. If you prefer, contact us at <a href=\"mailto:<EMAIL>\" style=\"text-decoration: underline; color: #FF3C00;\"><EMAIL></a> and we'll forward you any relevant updates as soon as we're informed."

msgid "mail_transaction_new_product_shipped_text_4_1"
msgstr "Please check your packages very carefully before signing with the courier. Transit damage can happen from time to time, and we need to know if your order was affected so we can correct it. If anything is damaged, ask the courier for a complaint form (they should have them on hand) and note the damage immediately. Keep the affected parcels (don’t return them to the courier) and take photos of the damaged packaging and product. Once you contact us with this information, we’ll arrange pick-up immediately and replace any elements as needed."

msgid "mail_transaction_new_product_shipped_text_5_1"
msgstr "Once you’ve got your furniture unpacked and set up, we’d love to hear how it’s settling in! If you have any questions in the meantime, give us a call on "

msgid "mail_transaction_new_product_shipped_text_5_1_2"
msgstr " or "

msgid "mail_transaction_new_product_shipped_text_5_2"
msgstr " or simply reply to this mail."

msgid "mail_transaction_new_product_shipped_text_6_1"
msgstr "Speak Soon,<br>Team Tylko"

msgid "mail_transaction_new_product_shipped_text_7_1"
msgstr "There’s plenty more Tylko content to explore while you wait for your beautiful piece to arrive. Take a flip through our journal."

msgid "mail_transaction_new_product_tobeshipped_header_1_1"
msgstr "Your order is almost out the door."

msgid "mail_transaction_new_product_tobeshipped_header_2_1"
msgstr "We’re just getting it ready to travel."

msgid "mail_transaction_new_product_tobeshipped_header_3_1"
msgstr "Hello "

msgid "mail_transaction_new_product_tobeshipped_preheader_1"
msgstr "We’re doing our pre-flight checks and packing up."

msgid "mail_transaction_new_product_tobeshipped_subject_1"
msgstr "That’s a wrap...literally!"

msgid "mail_transaction_new_product_tobeshipped_text_1_1"
msgstr "It’s time! Your order number "

msgid "mail_transaction_new_product_tobeshipped_text_1_2"
msgstr "  is being carefully packaged as we speak. It will be triple-checked and sent out shortly."

msgid "mail_transaction_new_product_tobeshipped_text_2_1"
msgstr "So let’s talk delivery. You have <strong>24 hours</strong> to let us know any important information. The most important: do you live above the ground floor? If so, is there an elevator in your building?<br>You can also share any additional information that might be useful for the driver: tight parking, tricky access and any door codes, for example. The more detail you provide, the smoother your delivery will go. A win-win! Simply let us know the details by replying to this email.<br><br>You’ll receive a tracking number within 2 working days so you can watch your shipment’s status (the number activates when our delivery partner scans the package). The delivery time depends on the country, but shipping time to most European countries is 2-4 business days. Please keep in mind that deliveries only take place during workdays."

msgid "mail_transaction_new_product_tobeshipped_text_2_1_ups"
msgstr "So let’s talk delivery. You have <strong>24 hours</strong> to let us know any important information. The most important: do you live above the ground floor? If so, is there an elevator in your building?<br>You can also share any additional information that might be useful for the driver: tight parking, tricky access and any door codes, for example. The more detail you provide, the smoother your delivery will go. A win-win! Simply let us know the details by replying to this email.<br><br>You’ll receive a tracking number within 2 working days so you can watch your shipment’s status (the number activates when our delivery partner scans the package). The delivery time depends on the country, but shipping time to most European countries is 2-4 business days. Please keep in mind that deliveries only take place during workdays."

msgid "mail_transaction_new_product_tobeshipped_text_2_2"
msgstr "Here’s a list of the packages you’ll receive:"

msgid "mail_transaction_new_product_tobeshipped_text_2_5"
msgstr "Total"

msgid "mail_transaction_new_product_tobeshipped_text_3_1"
msgstr "We can’t wait to get your order to you.<br><br>Can’t wait to assemble your shelves? OK eager beaver! Get a head start by watching our <a style=\"text-decoration: none;color:#FF3C00;\" href=\"http://tips.tylko.com/en/articles/4472983-assembly-video-tutorials\">videos</a> featuring every pro-tip you need to know. When your shelves arrive you’ll be more than ready to get to work. "

msgid "mail_transaction_new_product_tobeshipped_text_4_1"
msgstr "Speak Soon,<br>Team Tylko"

msgid "mail_transaction_newest_product_delivered_text_4_1"
msgstr "Speak Soon,<br>Team Tylko"

msgid "mail_transaction_order_placed_sameas"
msgstr "Same as Shipping Address"

msgid "mail_transaction_orderded_to_be_canceled_text_4"
msgstr "Speak Soon, <br>The Tylko team."

msgid "mail_transaction_payment_confirmation_assembly_service_button_1"
msgstr "Start referring friends"

msgid "mail_transaction_payment_confirmation_assembly_service_header_2"
msgstr "We’ve received your payment – now we get to the good stuff."

msgid "mail_transaction_payment_confirmation_assembly_service_header_2_sample"
msgstr "We’ve received your payment – now for the fun stuff."

msgid "mail_transaction_payment_confirmation_assembly_service_header_assembly_2"
msgstr "We've received your payment – now we get to the good stuff."

msgid "mail_transaction_payment_confirmation_assembly_service_header_klarna"
msgstr "Your order is officially ready to be produced."

#, python-format
msgid "mail_transaction_payment_confirmation_assembly_service_p2_%(delivery_range)s"
msgstr "Your furniture is scheduled to be produced between %(delivery_range)s."

msgid "mail_transaction_payment_confirmation_assembly_service_p2_klarna"
msgstr "Please be aware that if you chose to pay with Klarna, you will receive a payment link soon. "

msgid "mail_transaction_payment_confirmation_assembly_service_p3"
msgstr "As each and every piece is custom built, it takes a little longer than one-size-fits-all furniture to create. If your order looks like it might be built sooner (or in very rare cases, a little later) we’ll let you know right away."

msgid "mail_transaction_payment_confirmation_assembly_service_p4"
msgstr "Once it’s finished, we'll be in touch within a few days to arrange a date and time for a Tylko expert to assemble your order."

#, python-format
msgid "mail_transaction_payment_confirmation_assembly_service_p4_%(amount)s_%(value)s"
msgstr "Want to earn a full refund for your order?<br><br> With our referral program, the more friends you tell (and who place an order), the more cash you can earn. For every referred friend who places and keeps an order, you can  <span style=\"color: #FF3C00;\">earn %(amount)s back</span> - and they’ll get %(value)s off their order, too. Win-win!"

msgid "mail_transaction_payment_confirmation_assembly_service_p5"
msgstr "We’ll keep in touch as your order progresses, and you can track its progress <a href=\"https://tylko.com/contact/?topic=order_status\" class=\"link\" style=\"color: #FF3C00; text-decoration: none;\">here</a>. If you have any last-minute changes you want to make, please use the easy contact form to <a href=\"https://tylko.com/contact/\" class=\"link\" style=\"color: #FF3C00; text-decoration: none;\">get in touch</a>. "

msgid "mail_transaction_payment_confirmation_assembly_service_preheader"
msgstr "Now we get to work."

msgid "mail_transaction_payment_confirmation_assembly_service_signature"
msgstr "Speak Soon,<br>The Tylko Team"

msgid "mail_transaction_payment_confirmation_header_1_1"
msgstr "Your invoice is attached."

msgid "mail_transaction_product_delivered_assembly_service_signature"
msgstr "Speak Soon,<br>The Tylko Team"

msgid "mail_transaction_proforma_invoice_header_1_1"
msgstr "Here it is - your proforma invoice."

msgid "mail_transaction_proforma_invoice_header_2_1"
msgstr "Your last payment step awaits…"

msgid "mail_transaction_proforma_invoice_header_3_1"
msgstr "Hello"

msgid "mail_transaction_proforma_invoice_preheader_1"
msgstr "Time to finish up your payment."

#, python-format
msgid "mail_transaction_proforma_invoice_subject_1_%(order_pretty_id)s"
msgstr "Order # %(order_pretty_id)s: Here it is, your proforma invoice from Tylko."

msgid "mail_transaction_proforma_invoice_text_1_1"
msgstr "Attached you’ll find the proforma invoice for your recent Tylko order no "

msgid "mail_transaction_proforma_invoice_text_1_2"
msgstr "Once you've completed your bank transfer, we can begin to process your order. Please be sure to enter your individual proforma invoice number in the reference field when you make the transfer."

msgid "mail_transaction_proforma_invoice_text_2_1"
msgstr "We can't wait to get started on your one-of-a-kind piece - we'll send you a confirmation as soon as your payment is complete and all systems are go."

msgid "mail_transaction_proforma_invoice_text_3_1"
msgstr "Thanks Kindly!<br>Team Tylko"

msgid "mail_transaction_trustpilot_reviews_button"
msgstr "Click to share"

msgid "mail_transaction_trustpilot_reviews_header"
msgstr "We’d love for you to share your review!"

msgid "mail_transaction_trustpilot_reviews_p1"
msgstr "Thanks so much for leaving your great review - you really made our day! We’d be thrilled if you’d take a moment to share it with Trustpilot, too. "

msgid "mail_transaction_trustpilot_reviews_p2"
msgstr "We’ve made it super easy. All you have to do is:"

msgid "mail_transaction_trustpilot_reviews_p3"
msgstr "<strong>1.</strong> Copy your review"

msgid "mail_transaction_trustpilot_reviews_p4"
msgstr "<strong>2.</strong> Click the link below"

msgid "mail_transaction_trustpilot_reviews_p5"
msgstr "<strong>3.</strong> Paste it right on the Trustpilot site!"

msgid "mail_transaction_trustpilot_reviews_preheader"
msgstr "Care to share it?"

msgid "mail_transaction_trustpilot_reviews_subject"
msgstr "Your review made our day."

msgid "mail_transaction_trustpilot_reviews_thankyou"
msgstr "Thanks again - you’re so appreciated! "

msgid "mail_visit_facebook"
msgstr "Visit Tylko Facebook Page"

msgid "mail_visit_instagram"
msgstr "Visit Tylko Instagram Profile"

msgid "mail_visit_pinterest"
msgstr "Visit Tylko Pinterest Page"

msgid "mail_visit_twitter"
msgstr "Visit Tylko Twitter Profile"

msgid "mailchimp_form_extra"
msgstr "Feel free to drop the names, Instagram handles, or emails of any other designers who would love this too!"

msgid "mailchimp_form_newsletter_subscription"
msgstr "I agree to receive valuable content from Tylko in the form of occasional emails."

msgid "mailchimp_form_privacy_policy"
msgstr "Your data is managed by Custom Sp. z o.o. To find out more, visit our "

msgid "mailchimp_form_profession"
msgstr "What is your design profession? (eg. Interior Designer, Home Stager, Architect)"

msgid "mailchimp_form_website"
msgstr "Your Website (or Instagram profile or any other online presence)"

msgid "mailing_cartabandoner_mail_5b_preheader"
msgstr "Sped-up shipping, on us."

msgid "mailing_common_footer_address_1_1"
msgstr "Hotline:"

msgid "mailing_common_footer_address_1_2"
msgstr "Email:"

msgid "mailing_common_footer_address_1_3"
msgstr "Website:"

msgid "mailing_common_footer_header_1_1"
msgstr "Perfectly-fit to you and your life"

msgid "mailing_common_footer_header_2_1"
msgstr "Simple<br>configuration"

msgid "mailing_common_footer_header_2_2"
msgstr "Free delivery<br>and returns"

msgid "mailing_common_footer_header_2_3"
msgstr "Easy<br>assembly"

msgid "mailing_common_footer_phone_1"
msgstr "+44 ************"

msgid "mailing_common_footer_phone_2"
msgstr "+448001026747"

msgid "mailing_dtf_confirmation_body_1_%(date)s_%(start_hour)s_%(end_hour)s"
msgstr "We’re pleased to confirm that your Tylko delivery has been scheduled for %(date)s, between %(start_hour)s and %(end_hour)s.<br><br>Please note that all times are approximate and may be influenced by cancellations/traffic delays and other unforeseen events.<br><br>If delays occur, our drivers may contact you before delivery from a number starting with +48."

msgid "mailing_dtf_confirmation_body_1_klarna"
msgstr "mailing_dtf_confirmation_body_1_klarna"

#, python-format
msgid "mailing_dtf_confirmation_body_2_%(date)s_%(start_hour)s_%(end_hour)s"
msgstr "You can expect your order to be delivered on %(date)s, between %(start_hour)s and %(end_hour)s."

msgid "mailing_dtf_confirmation_body_3_klarna"
msgstr "Please be aware that if you have chosen to pay with Klarna, you wil receive a payment link soon. "

msgid "mailing_dtf_confirmation_header"
msgstr "Dear customer,"

msgid "mailing_dtf_confirmation_preheader_%(order_number)s"
msgstr "Order number %(order_number)s"

msgid "mailing_dtf_confirmation_signature"
msgstr "Team Tylko"

msgid "mailing_dtf_confirmation_subheader_%(order_number)s"
msgstr "Order no. %(order_number)s"

msgid "mailing_dtf_confirmation_subject"
msgstr "Your Tylko delivery confirmation"

msgid "mailing_dtf_delivery_settled_estimated_delivery_date_subject"
msgstr "Your Tylko delivery confirmation"

msgid "mailing_dtf_estimated_delivery_date_delivery_settled_1_%(date)s_%(start_hour)s_%(end_hour)s"
msgstr "We’re pleased to confirm that your Tylko delivery has been scheduled for %(date)s, between %(start_hour)s and %(end_hour)s."

msgid "mailing_dtf_estimated_delivery_date_delivery_settled_2"
msgstr "Please note that all times are approximate and may be influenced by cancellations/traffic delays and other unforeseen events."

msgid "mailing_dtf_estimated_delivery_date_delivery_settled_3"
msgstr "If delays occur, our drivers may contact you before delivery from a number starting with +48."

msgid "mailing_dtf_estimated_delivery_date_delivery_settled_order_no_%(order_id)s"
msgstr "Order no. %(order_id)s"

msgid "mailing_dtf_estimated_delivery_date_delivery_settled_signature"
msgstr "Team Tylko"

msgid "mailing_dtf_estimated_delivery_date_delivery_settled_welcome"
msgstr "Dear customer,"

msgid "mailing_dtf_estimated_delivery_date_proposal_headline"
msgstr "Your Tylko order delivery"

msgid "mailing_dtf_estimated_delivery_date_proposal_signature"
msgstr "Kindest Regards, <br>Your Tylko Team"

msgid "mailing_dtf_estimated_delivery_time_proposal_button_text"
msgstr "Plan Your Delivery"

#, python-format
msgid "mailing_dtf_proposal_after_email24_body_1_%(minimal_timeslots)s"
msgstr "To make sure your delivery goes perfectly, we need one last bit of info from you.<br><br>Please choose %(minimal_timeslots)s (or more) time slots that will work for delivery by clicking below and marking them in the calendar."

msgid "mailing_dtf_proposal_after_email24_body_2"
msgstr "Our Logistics Team will then confirm your dedicated delivery time at least 24 hours prior to the chosen date. Easy!"

msgid "mailing_dtf_proposal_after_email24_preheader"
msgstr "Just let us know when you'll be available for delivery!"

msgid "mailing_dtf_proposal_after_email24_subject"
msgstr "One final step - tell us when to deliver your Tylko"

#, python-format
msgid "mailing_dtf_proposal_body_1_%(minimal_timeslots)s"
msgstr "We're prepping our courier to make sure your Tylko shipment goes perfectly to plan.<br><br>To arrange your delivery, we'll need a little bit of info from you:<br><br>Please choose %(minimal_timeslots)s (or more) time slots that will work for delivery by clicking below and marking them in the calendar."

msgid "mailing_dtf_proposal_body_2"
msgstr "Our Logistics Team will then take care of the details and confirm your dedicated delivery time at least 24 hours prior to the chosen date."

#, python-format
msgid "mailing_dtf_proposal_body_3_%(order_id)s"
msgstr "Meanwhile, if you have any questions or concerns about your Order Number %(order_id)s, please reply to this email (with the content attached) and we'll get right back to you."

msgid "mailing_dtf_proposal_button_text"
msgstr "Plan Your Delivery"

msgid "mailing_dtf_proposal_estimated_delivery_date_body_1_%(minimal_timeslots)s"
msgstr "Please choose %(minimal_timeslots)s time slots that will work for you by clicking below and marking them in the calendar."

msgid "mailing_dtf_proposal_estimated_delivery_date_body_1_0"
msgstr "Your order is making its way down our production line, so we thought it would be a great time to arrange your future delivery."

msgid "mailing_dtf_proposal_estimated_delivery_date_body_1_single_time_slot"
msgstr "Please choose 1 time slot that will work for you by clicking below and marking them in the calendar."

msgid "mailing_dtf_proposal_estimated_delivery_date_body_2"
msgstr "Our Logistics Team will then confirm your dedicated delivery date and time at least 24 hours prior to the chosen date. Easy!"

msgid "mailing_dtf_proposal_estimated_delivery_date_body_3_%(order_id)s"
msgstr "Meanwhile, if you have any questions or concerns about your Order Number %(order_id)s, please reply to this email (with the content attached) and we'll get right back to you."

msgid "mailing_dtf_proposal_estimated_delivery_date_preheader"
msgstr "Your order will be ready soon – let's get prepped!"

msgid "mailing_dtf_proposal_headline"
msgstr "We're almost ready to deliver..."

msgid "mailing_dtf_proposal_one_day_body_1"
msgstr "To make sure your delivery goes perfectly, we need one last bit of info from you. <br><br>Please choose your preferred delivery date by clicking below and marking it in the calendar."

msgid "mailing_dtf_proposal_one_day_body_2"
msgstr "Our Logistics Team will then take care of the details and confirm your dedicated delivery time at least 24 hours prior to the chosen date."

msgid "mailing_dtf_proposal_one_day_preheader"
msgstr "Just let us know when you'll be available for delivery!"

msgid "mailing_dtf_proposal_preheader"
msgstr "Your shelf is packed and almost ready to go... just let us know when you'll be available to accept delivery."

msgid "mailing_dtf_proposal_signature"
msgstr "Kindest Regards,<br>Your Tylko Team"

msgid "mailing_dtf_proposal_subject"
msgstr "One last step - tell us when to deliver your Tylko"

#, python-format
msgid "mailing_dtf_welcome_%(user)s"
msgstr "Hi %(user)s,"

#, python-format
msgid "mailing_mail24_filled_body_1_%(user)s"
msgstr "Hi %(user)s,<br><br>Thanks for sharing your details! We'll now arrange your super smooth delivery and be in touch with the next details soon.<br><br>You can check out the details of your shipment   "

#, python-format
msgid "mailing_mail24_filled_body_2_%(order_number)s"
msgstr "as well as see how many packages to expect (and their sizes), too.<br><br>Meanwhile, if you have any questions or concerns about your Order Number %(order_number)s, please reply to this email (with the content attached) and we'll get right back to you.<br><br>Kindest,<br><br>Your Tylko Team   "

msgid "mailing_mail24_filled_btn"
msgstr "here,"

msgid "mailing_mail24_filled_headline"
msgstr "Coming soon..."

msgid "mailing_mail24_filled_preheader"
msgstr "Thanks for sharing your delivery details. Here's what happens&nbsp;next..."

msgid "mailing_mail24_filled_subject_1"
msgstr "Delivery details received. Get ready to meet your Tylko!"

#, python-format
msgid "mailing_mail24_question_body_1_%(user)s"
msgstr "Hi %(user)s,<br><br>Your Tylko order is ready to ship! We just need a few delivery details from you to make sure it all goes as smoothly as possible.<br><br>Please click below to take a few moments within the next 24 hours to answer a few questions regarding your order:"

#, python-format
msgid "mailing_mail24_question_body_2_%(order_number)s"
msgstr "Once you've shared your delivery info, our Logistics Team will wrap up your order - keeping you informed every step of the way, of course.<br><br>(If you didn't have time to respond within 24 hours, no worries - we've gone ahead and arranged shipment based on what we thought would work best.)<br><br> Meanwhile, if you have any questions or concerns about your Order Number %(order_number)s, please reply to this email (with the content attached) and we'll get right back to you.<br><br>Be Well,<br><br>Your Tylko Team "

msgid "mailing_mail24_question_btn"
msgstr "My Delivery"

msgid "mailing_mail24_question_edd_body_1_%(user)s_%(hour)s_%(date)s"
msgstr "Hi %(user)s,<br><br>Your Tylko order has entered production, so we're reaching out in advance to make sure we have the details we need to make your shipment go smoothly. Please take a few moments to help us plan your delivery so we can get your order to you in the best possible way.<br><br>Please click below to answer a few questions regarding your order before %(hour)s on %(date)s:"

msgid "mailing_mail24_question_edd_body_2_%(order_number)s_%(hour)s_%(date)s"
msgstr "Once you've shared your delivery info, our Logistics Team will follow up and keep you informed every step of the way.<br><br>(If you didn't have time to respond before %(hour)s %(date)s, no worries – we've gone ahead and arranged shipment based on what we thought would work best.)<br><br>Meanwhile, if you have any questions or concerns about your Order Number %(order_number)s, please reply to this email (with the content attached) and we'll get right back to you.<br><br>Be Well,<br>Your Tylko Team"

msgid "mailing_mail24_question_edd_headline"
msgstr "We're finishing up your Tylko order for delivery..."

msgid "mailing_mail24_question_edd_preheader"
msgstr "Let's get your delivery details sorted."

msgid "mailing_mail24_question_headline"
msgstr "We're preparing your Tylko shipment..."

msgid "mailing_mail24_question_preheader"
msgstr "Please share a few delivery details as we pack and prep your order."

msgid "mailing_mail24_question_subject_1"
msgstr "We need a few details to wrap up your order"

msgid "mailing_resend_accepted_dtf_body_1"
msgstr "We are truly sorry for any incovenience caused, but unfortunately we're not able to make it on the dates you have provided. We promise we are doing our best to deliver your Tylko as soon as possible!"

#, python-format
msgid "mailing_resend_accepted_dtf_choose_many_dates_%(slots)s"
msgstr "In order to arrange a new delivery date, please choose %(slots)s (or more) time slots by clicking below and marking them in the calendar."

msgid "mailing_resend_accepted_dtf_choose_one_date"
msgstr "In order to arrange a new delivery date, please choose your preferred delivery date by clicking below and marking it in the calendar."

msgid "mailing_resend_accepted_dtf_goodbye"
msgstr "Thank you for your understanding, and see you soon.<br><br>Kindest regards,<br>Tylko Team"

msgid "mailing_resend_accepted_dtf_preheader_1"
msgstr "Thank you for waiting ❤️"

msgid "mailing_resend_accepted_dtf_preheader_2"
msgstr "Your package will be with you shortly..."

msgid "mailing_resend_accepted_dtf_subject"
msgstr "About your Tylko delivery"

#, python-format
msgid "mailing_resend_dtf_body_2_%(order_id)s"
msgstr "Our logistics team will then take care of the details and confirm your dedicated delivery time at least 24 hours prior to the chosen date.<br><br>Meanwhile, if you have any questions or concerns about your order number %(order_id)s please reply to this email and we'll get right back to you."

msgid "mailing_resend_not_accepted_dtf_body_1"
msgstr "But we're still waiting for you to provide us with your preferred delivery dates so we can plan accordingly."

msgid "mailing_resend_not_accepted_dtf_choose_one_date"
msgstr "In order to arrange a delivery date, please choose your preferred delivery date by clicking below and marking it in the calendar."

msgid "mailing_resend_not_accepted_dtf_goodbye"
msgstr "See you soon!<br><br>Kindest regards,<br>Team Tylko"

msgid "mailing_resend_not_accepted_dtf_preheader_1"
msgstr "We're waiting for your preferred delivery dates."

msgid "mailing_resend_not_accepted_dtf_preheader_2"
msgstr "Your Tylko furniture is ready to be delivered..."

msgid "mailing_resend_not_accepted_dtf_subject"
msgstr "Your Tylko is ready!"

msgid "mailing_unsubscribe"
msgstr "Unsubscribe"

msgid "manual_adding_backs"
msgstr "Adding backs"

msgid "manual_adding_doors"
msgstr "Adding doors"

msgid "manual_adding_drawers"
msgstr "Adding drawers"

msgid "manual_adding_inserts"
msgstr "Adding inserts"

msgid "manual_adding_legs"
msgstr "Adding legs"

msgid "manual_adding_plinth"
msgstr "Adding Plinth"

msgid "manual_adding_supports"
msgstr "Adding supports"

msgid "manual_ash_veneer"
msgstr "White Oak"

msgid "manual_attaching_desktop"
msgstr "Attaching the desktop"

msgid "manual_attaching_vertical"
msgstr "Attaching the vertical support"

msgid "manual_aubergine_plywood"
msgstr "Aubergine plywood"

msgid "manual_black_particle_board"
msgstr "Black"

msgid "manual_black_plywood"
msgstr "Black plywood"

msgid "manual_blue_plywood"
msgstr "Blue Plywood"

msgid "manual_burgundy_red_particle_board"
msgstr "Burgundy Red particle board"

msgid "manual_classic_red"
msgstr "Classic Red"

msgid "manual_connecting_parts"
msgstr "Connecting the parts together"

msgid "manual_cotton_beige_particle_board"
msgstr "Cotton Beige particle board"

msgid "manual_dark_brown_plywood"
msgstr "Dark Brown Plywood"

msgid "manual_dark_oak_veneer"
msgstr "Walnut"

msgid "manual_dusty_Pink"
msgstr "Dusty Pink"

msgid "manual_fixing"
msgstr "Fixing The Shelf to The Wall"

msgid "manual_grey"
msgstr "Grey"

msgid "manual_grey_dark_grey"
msgstr "Grey + Dark Grey"

msgid "manual_grey_plywood"
msgstr "Grey plywood"

msgid "manual_grey_walnut_particle_board"
msgstr "Grey + Walnut Veneer"

msgid "manual_inserting_pins"
msgstr "Inserting the pins"

msgid "manual_installing_backpanel"
msgstr "Installing the back panel"

msgid "manual_legs"
msgstr "legs"

msgid "manual_matte_black_particle_board"
msgstr "Premium Matte Black particle board"

msgid "manual_midnight_blue_particle_board"
msgstr "Midnight Blue particle board"

msgid "manual_mint_forest_green_particle_board"
msgstr "Mint + Forest Green particle board"

msgid "manual_natural_plywood"
msgstr "Natural plywood"

msgid "manual_oak_veneer"
msgstr "Oak veneer"

msgid "manual_package"
msgstr "package"

msgid "manual_packages"
msgstr "packages"

msgid "manual_part"
msgstr "Part"

msgid "manual_parts_need"
msgstr "the parts you need:"

msgid "manual_plinth"
msgstr "The last part of the plinth"

msgid "manual_protective_cover1"
msgstr "Tip: dont assemble your shelf directly on the floor!"

msgid "manual_protective_cover2"
msgstr "Avoid scratches by placing the Tylko cardboard,"

msgid "manual_protective_cover3"
msgstr "a protective mat or a rug underneath it."

msgid "manual_raising_shelf"
msgstr "Raising the shelf from the floor"

msgid "manual_reisingers_pink_particle_board"
msgstr "Reisinger Pink particle board"

msgid "manual_sage_green_particle_board"
msgstr "Sage Green particle board"

msgid "manual_sand_midnight_blue_particle_board"
msgstr "Sand + Midnight Blue particle board"

msgid "manual_sand_mustard_yellow"
msgstr "Sand + Mustard Yellow"

msgid "manual_shelf_id"
msgstr "shelf id: "

msgid "manual_sky_blue_particle_board"
msgstr "Sky Blue particle board"

msgid "manual_solid_sycamore"
msgstr "Solid Sycamore"

msgid "manual_stone_grey_particle_board"
msgstr "Stone Grey particle board"

msgid "manual_terracotta_particle_board"
msgstr "Terracotta particle board"

msgid "manual_tylko_front"
msgstr "TYLKO SHELF MANUAL"

msgid "manual_white_particle_board"
msgstr "White particle board"

msgid "manual_white_plywood"
msgstr "White plywood"

msgid "manual_yellow"
msgstr "Yellow"

msgid "martin_category_description_all_shelves"
msgstr "Our thoughtfully designed storage furniture encourages effortless organisation and a more harmonious living. Customise every detail to fulfil all your individual needs."

msgid "martin_category_description_bedside"
msgstr "With a customisable width starting at 30cm, the Tylko Bedside Table fits the smallest of bedrooms whilst offering enough space to keep all your most necessary items at arm’s reach."

msgid "martin_category_description_bookcase"
msgstr "Create a Bookcase that fits every story and meets your every need. From size to style, doors to drawers, you can customise every detail to create a totally functional library that looks great too."

msgid "martin_category_description_chest_drawers"
msgstr "With drawers for all three row heights and all rows under 160cm, there's a sliding solution to extra storage for every style!"

msgid "martin_category_description_desk"
msgstr "Tackle any task with a stylish desk that makes the most of your space, and your time. Add storage, cable management and drawers, plus adjust size for optimal organisation."

msgid "martin_category_description_shoe_rack"
msgstr "Perfect for the hallway, these customizable shelves don't stick out...or stub toes!"

msgid "martin_category_description_sideboard"
msgstr "Design your one-of-a-kind Sideboard to match your style and your space in just a few clicks. Customise size, colour and more to create storage that stands out."

msgid "martin_category_description_tv_stand"
msgstr "Starting from 23cm in height, the customizable TV Stand is the real star in the room."

msgid "martin_category_description_vinyl_storage"
msgstr "With a 40cm shelf depth and three row heights perfectly-proportioned for your records, your collection has a home that's sturdy and stylish."

msgid "martin_category_description_wall_storage"
msgstr "Make more space at home by designing your ideal Wall Storage. Customise size and colour, plus add open or closed segments to show off objects and keep extra storage tucked away. This is Wall Storage, but better."

msgid "martin_category_description_wardrobe"
msgstr "With the possibility to create a wardrobe as big as 3,6m in height and 6m in width, your storage possibilities are endless."

msgid "martin_common_all_furniture"
msgstr "All products"

msgid "martin_common_armchair_plural"
msgstr "Armchairs"

msgid "martin_common_armchair_singular"
msgstr "Armchair"

msgid "martin_common_bedside_plural"
msgstr "Bedside Tables"

msgid "martin_common_bedside_singular"
msgstr "Bedside Table"

msgid "martin_common_bookcase_plural"
msgstr "Bookcases"

msgid "martin_common_bookcase_singular"
msgstr "Bookcase"

msgid "martin_common_chaise_longue_plural"
msgstr "Chaise longues"

msgid "martin_common_chaise_longue_singular"
msgstr "Chaise longue"

msgid "martin_common_chest_drawers_plural"
msgstr "Chests of Drawers"

msgid "martin_common_chest_drawers_singular"
msgstr "Chest of Drawers"

msgid "martin_common_corner_plural"
msgstr "Corner sofas"

msgid "martin_common_corner_singular"
msgstr "Corner sofa"

msgid "martin_common_cover_plural"
msgstr "Covers"

msgid "martin_common_cover_singular"
msgstr "Cover"

msgid "martin_common_description_tricolor"
msgstr "Explore our range of styles and features, and design a personalised shelf that makes a statement. Now available in three new contemporary hues: Burgundy Red, Sky Blue and Cotton Beige."

msgid "martin_common_desk_plural"
msgstr "Desks"

msgid "martin_common_desk_singular"
msgstr "Desk"

msgid "martin_common_dressing_table_plural"
msgstr "Dressing tables"

msgid "martin_common_dressing_table_singular"
msgstr "Dressing table"

msgid "martin_common_footrest_and_modules_plural"
msgstr "Ottomans and modules"

msgid "martin_common_footrest_and_modules_singular"
msgstr "Ottoman and module"

msgid "martin_common_four_plus_seater_plural"
msgstr "Large sofas"

msgid "martin_common_four_plus_seater_singular"
msgstr "Large sofa"

msgid "martin_common_shoe_rack_plural"
msgstr "Shoe Racks"

msgid "martin_common_shoe_rack_singular"
msgstr "Shoe Rack"

msgid "martin_common_sideboard_plural"
msgstr "Sideboards"

msgid "martin_common_sideboard_singular"
msgstr "Sideboard"

msgid "martin_common_three_seater_plural"
msgstr "3-seater sofas"

msgid "martin_common_three_seater_singular"
msgstr "3-seater sofa"

msgid "martin_common_tricolor"
msgstr "Meet the new Tylko Original Modern palette"

msgid "martin_common_tv_stand_plural"
msgstr "TV Stands"

msgid "martin_common_tv_stand_singular"
msgstr "TV Stand"

msgid "martin_common_two_seater_plural"
msgstr "2-seater sofas"

msgid "martin_common_two_seater_singular"
msgstr "2-seater sofa"

msgid "martin_common_vinyl_storage_plural"
msgstr "Vinyl Storage"

msgid "martin_common_vinyl_storage_plural_nobr"
msgstr "Vinyl Storage"

msgid "martin_common_vinyl_storage_singular"
msgstr "Vinyl Storage"

msgid "martin_common_wall_storage_plural"
msgstr "Wall Storage"

msgid "martin_common_wall_storage_singular"
msgstr "Wall Storage"

msgid "martin_common_wardrobe_new"
msgstr "New"

msgid "martin_common_wardrobe_plural"
msgstr "Wardrobes"

msgid "martin_common_wardrobe_plural_nobr"
msgstr "Wardrobes"

msgid "martin_common_wardrobe_singular"
msgstr "Wardrobe"

msgid "martin_filter_menu_section_1_label_1"
msgstr "White"

msgid "martin_filter_menu_section_1_label_2"
msgstr "Grey"

msgid "martin_filter_menu_section_1_label_3"
msgstr "Black"

msgid "martin_filter_menu_section_4_label_2"
msgstr "Tylko Original Modern "

msgid "martin_grid_configurator_slot_1_buton_1"
msgstr "Create your own"

msgid "martin_grid_configurator_slot_1_header_1"
msgstr "Haven&apos;t seen the perfect shelf yet?"

msgid "martin_grid_configurator_slot_2_buton_1"
msgstr "Filters"

msgid "martin_grid_configurator_slot_2_buton_2"
msgstr "CREATE YOUR OWN"

msgid "martin_grid_configurator_slot_2_header_1"
msgstr "We've got loads more styles to choose from! Find your perfect fit product using"

msgid "martin_grid_configurator_slot_2_header_2"
msgstr "or"

msgid "martin_grid_content_slot_1_header_1"
msgstr "Flexible design"

msgid "martin_grid_content_slot_1_paragraph_1"
msgstr "Make maximum use of your space with shelves that fit perfectly. Tailor a Tylko Shelf to your home and unique storage needs."

msgid "martin_grid_content_slot_2_header_1"
msgstr "Free delivery"

msgid "martin_grid_content_slot_2_paragraph_1"
msgstr "Your new Tylko Shelves will be delivered to your door. Settle them in for up to 100 days or the return is free on us."

msgid "martin_grid_content_slot_3_header_1"
msgstr "Easy assembly"

msgid "martin_grid_content_slot_3_paragraph_1"
msgstr "Our simple, snap-together shelves will save you time and energy. The color-coded system makes assembly a breeze."

msgid "martin_grid_header_1_1"
msgstr "Explore"

msgid "martin_grid_material_slot_1_header_1"
msgstr "Scratch-free coating"

msgid "martin_grid_material_slot_1_header_2"
msgstr "Coated against wear and tear"

msgid "martin_grid_material_slot_2_header_1"
msgstr "Durable Materials"

msgid "martin_grid_material_slot_2_header_2"
msgstr "13 layers of compressed birchwood"

msgid "martin_grid_material_slot_3_header_1"
msgstr "Natural Plywood"

msgid "martin_grid_material_slot_3_header_2"
msgstr "Hand-polished with toxin-free plant oil"

msgid "martin_grid_product_slot_1_header_1"
msgstr "All styles are available from 70-450cm in width."

msgid "martin_grid_product_slot_2_header_1"
msgstr "All styles are available in all 5 colors."

msgid "martin_grid_product_slot_3_header_1"
msgstr "<b>Adjust</b> and adapt <b>any shelf</b> easily with the configurator."

msgid "martin_grid_product_slot_4_header_1"
msgstr "Delivered free, with free <b>100-day returns</b>."

msgid "martin_social_feed_post_10_name"
msgstr "Annemarie van den Boomen"

msgid "martin_social_feed_post_10_nick"
msgstr "commeamie"

msgid "martin_social_feed_post_11_name"
msgstr "Claudi [doitbutdoitnow]"

msgid "martin_social_feed_post_11_nick"
msgstr "doitbutdoitnow"

msgid "martin_social_feed_post_12_name"
msgstr "M A R G O  H U P E R T"

msgid "martin_social_feed_post_12_nick"
msgstr "margo.hupert.art"

msgid "martin_social_feed_post_13_name"
msgstr "NEWNIQ Interior Blog | Berlin"

msgid "martin_social_feed_post_13_nick"
msgstr "newniqberlin"

msgid "martin_social_feed_post_14_name"
msgstr "POLIENNE by Paulien Riemis"

msgid "martin_social_feed_post_14_nick"
msgstr "paulienriemis"

msgid "martin_social_feed_post_15_name"
msgstr "2lgstudio"

msgid "martin_social_feed_post_15_nick"
msgstr "2lgstudio"

msgid "martin_social_feed_post_16_name"
msgstr "carla | minimalistic interior"

msgid "martin_social_feed_post_16_nick"
msgstr "carla_august"

msgid "martin_social_feed_post_17_name"
msgstr "Miriam Stimpfl"

msgid "martin_social_feed_post_17_nick"
msgstr "thewaveshavecome"

msgid "martin_social_feed_post_18_name"
msgstr "Tekla Evelina Severin"

msgid "martin_social_feed_post_18_nick"
msgstr "teklan"

msgid "martin_social_feed_post_19_name"
msgstr "2lgstudio"

msgid "martin_social_feed_post_19_nick"
msgstr "2lgstudio"

msgid "martin_social_feed_post_1_name"
msgstr "Liebesbotschaft"

msgid "martin_social_feed_post_1_nick"
msgstr "liebesbotschaft"

msgid "martin_social_feed_post_20_name"
msgstr "Oliver Hooson"

msgid "martin_social_feed_post_20_nick"
msgstr "olvh"

msgid "martin_social_feed_post_21_name"
msgstr "Camea"

msgid "martin_social_feed_post_21_nick"
msgstr "camea"

msgid "martin_social_feed_post_22_name"
msgstr "LU ↠ LUCIE SKOTNICOVÁ"

msgid "martin_social_feed_post_22_nick"
msgstr "luciescott"

msgid "martin_social_feed_post_25_name"
msgstr "Lisa"

msgid "martin_social_feed_post_25_nick"
msgstr "wohnprojekt"

msgid "martin_social_feed_post_26_name"
msgstr "Maria Miklaszewska"

msgid "martin_social_feed_post_26_nick"
msgstr "maria_miklaszewska"

msgid "martin_social_feed_post_27_name"
msgstr "Ilona"

msgid "martin_social_feed_post_27_nick"
msgstr "mamoesjka_nl"

msgid "martin_social_feed_post_28_name"
msgstr "Kasia"

msgid "martin_social_feed_post_28_nick"
msgstr "my_full_house"

msgid "martin_social_feed_post_29_name"
msgstr "Cubicle by Shini Park"

msgid "martin_social_feed_post_29_nick"
msgstr "cubicle"

msgid "martin_social_feed_post_2_name"
msgstr "K U L E S Z A  &  P I K"

msgid "martin_social_feed_post_2_nick"
msgstr "agnieszka_kulesza"

msgid "martin_social_feed_post_30_name"
msgstr "Wink Déco 🇫🇷 Paris/Tours"

msgid "martin_social_feed_post_30_nick"
msgstr "winkdeco"

msgid "martin_social_feed_post_31_name"
msgstr "Hélène Rebelo"

msgid "martin_social_feed_post_31_nick"
msgstr "helene_rebelo"

msgid "martin_social_feed_post_32_name"
msgstr "Wouter Kaan"

msgid "martin_social_feed_post_32_nick"
msgstr "wouterkaan"

msgid "martin_social_feed_post_33_name"
msgstr "YOUROWNAGE"

msgid "martin_social_feed_post_33_nick"
msgstr "yourownage"

msgid "martin_social_feed_post_34_name"
msgstr "Oliver Hooson"

msgid "martin_social_feed_post_34_nick"
msgstr "olvh"

msgid "martin_social_feed_post_35_name"
msgstr "𝗨𝗿𝗯𝗮𝗻 𝗗𝗲𝘀𝗶𝗴𝗻 𝗦𝗽𝗮𝗰𝗲"

msgid "martin_social_feed_post_35_nick"
msgstr "blnspace"

msgid "martin_social_feed_post_36_name"
msgstr "numeroventi"

msgid "martin_social_feed_post_36_nick"
msgstr "_numeroventi_"

msgid "martin_social_feed_post_37_name"
msgstr "Annemarie van den Boomen"

msgid "martin_social_feed_post_37_nick"
msgstr "commeamie"

msgid "martin_social_feed_post_38_name"
msgstr "𝗨𝗿𝗯𝗮𝗻 𝗗𝗲𝘀𝗶𝗴𝗻 𝗦𝗽𝗮𝗰𝗲"

msgid "martin_social_feed_post_38_nick"
msgstr "blnspace"

msgid "martin_social_feed_post_39_nick"
msgstr "femte.til.venstre"

msgid "martin_social_feed_post_3_name"
msgstr "Lisa"

msgid "martin_social_feed_post_3_nick"
msgstr "wohnprojekt"

msgid "martin_social_feed_post_40_nick"
msgstr "nisi"

msgid "martin_social_feed_post_41_name"
msgstr "Michiel"

msgid "martin_social_feed_post_41_nick"
msgstr "michieljbosman"

msgid "martin_social_feed_post_42_name"
msgstr "Linh Tran Trung"

msgid "martin_social_feed_post_42_nick"
msgstr "don_linh"

msgid "martin_social_feed_post_43_name"
msgstr "Michelle Fior"

msgid "martin_social_feed_post_43_nick"
msgstr "fior_elle"

msgid "martin_social_feed_post_44_name"
msgstr "Sophie van Daniels"

msgid "martin_social_feed_post_44_nick"
msgstr "sophievandaniels"

msgid "martin_social_feed_post_45_name"
msgstr "Laura & Nora"

msgid "martin_social_feed_post_45_nick"
msgstr "_designtales_"

msgid "martin_social_feed_post_46_name"
msgstr "Frank Lin"

msgid "martin_social_feed_post_46_nick"
msgstr "frank.lin"

msgid "martin_social_feed_post_47_name"
msgstr "Anna Pirkola"

msgid "martin_social_feed_post_47_nick"
msgstr "annapirkola"

msgid "martin_social_feed_post_48_name"
msgstr "Jessica + Fleur"

msgid "martin_social_feed_post_48_nick"
msgstr "prchtg"

msgid "martin_social_feed_post_49_name"
msgstr "K A T Y"

msgid "martin_social_feed_post_49_nick"
msgstr "lifewithoutstairs"

msgid "martin_social_feed_post_50_name"
msgstr "Jessica + Fleur"

msgid "martin_social_feed_post_50_nick"
msgstr "prchtg"

msgid "martin_social_feed_post_51_name"
msgstr "RACHEL ®️💚🌍"

msgid "martin_social_feed_post_51_nick"
msgstr "petitecocoons"

msgid "martin_social_feed_post_52_name"
msgstr "silvia stella osella"

msgid "martin_social_feed_post_52_nick"
msgstr "silviastella_"

msgid "martin_social_feed_post_53_name"
msgstr "Sophia Roe"

msgid "martin_social_feed_post_53_nick"
msgstr "sophiaroe"

msgid "martin_social_feed_post_54_name"
msgstr "Angela 👱🏻‍♀️"

msgid "martin_social_feed_post_54_nick"
msgstr "angelest.xy"

msgid "martin_social_feed_post_55_name"
msgstr "Elnaz Tanaz Golnaz Hakkak"

msgid "martin_social_feed_post_55_nick"
msgstr "thetripletsss"

msgid "martin_social_feed_post_56_name"
msgstr "ruben schmitz"

msgid "martin_social_feed_post_56_nick"
msgstr "rubenschmitz"

msgid "martin_social_feed_post_57_name"
msgstr "meta Studio Berlin"

msgid "martin_social_feed_post_57_nick"
msgstr "metastudioberlin"

msgid "martin_social_feed_post_58_name"
msgstr "silvia stella osella"

msgid "martin_social_feed_post_58_nick"
msgstr "silviastella_"

msgid "martin_social_feed_post_59_name"
msgstr "Catarina Mira-Rose"

msgid "martin_social_feed_post_59_nick"
msgstr "catarinamira"

msgid "martin_social_feed_post_5_name"
msgstr "Autor Rooms Warszawa"

msgid "martin_social_feed_post_5_nick"
msgstr "autor_rooms"

msgid "martin_social_feed_post_60_name"
msgstr "Timo Kuilder"

msgid "martin_social_feed_post_60_nick"
msgstr "zwartekoffie"

msgid "martin_social_feed_post_61_name"
msgstr "Angela 👱🏻"

msgid "martin_social_feed_post_61_nick"
msgstr "angelest.xy"

msgid "martin_social_feed_post_62_name"
msgstr "Tak."

msgid "martin_social_feed_post_62_nick"
msgstr "tak.studio_"

msgid "martin_social_feed_post_63_name"
msgstr "Maria Kaas"

msgid "martin_social_feed_post_63_nick"
msgstr "makaas"

msgid "martin_social_feed_post_64_name"
msgstr "Charlène"

msgid "martin_social_feed_post_64_nick"
msgstr "ivorybizz"

msgid "martin_social_feed_post_65_name"
msgstr "Mia Mortensen"

msgid "martin_social_feed_post_65_nick"
msgstr "still_sunday"

msgid "martin_social_feed_post_66_name"
msgstr "Danila | Interior & still life"

msgid "martin_social_feed_post_66_nick"
msgstr "indivisualstyle"

msgid "martin_social_feed_post_67_name"
msgstr "SHAHEEN CHAND"

msgid "martin_social_feed_post_67_nick"
msgstr "shaheenchand"

msgid "martin_social_feed_post_68_name"
msgstr "Chun Suk Schulte • Interior"

msgid "martin_social_feed_post_68_nick"
msgstr "snookiful_life"

msgid "martin_social_feed_post_69_name"
msgstr "Robbert Redert"

msgid "martin_social_feed_post_69_nick"
msgstr "thebluefade"

msgid "martin_social_feed_post_71_name"
msgstr "Oliver Hooson"

msgid "martin_social_feed_post_71_nick"
msgstr "olvh"

msgid "martin_social_feed_post_75_name"
msgstr "ASTRID B ANDERSEN"

msgid "martin_social_feed_post_75_nick"
msgstr "astridandersenb"

msgid "martin_social_feed_post_76_name"
msgstr "Simon Schmidt"

msgid "martin_social_feed_post_76_nick"
msgstr "ownwayofinspiration"

msgid "martin_social_feed_post_77_name"
msgstr "Michelle Fleur"

msgid "martin_social_feed_post_77_nick"
msgstr "michellefleur"

msgid "martin_social_feed_post_78_name"
msgstr "Casa Filipe"

msgid "martin_social_feed_post_78_nick"
msgstr "casafilipe"

msgid "martin_social_feed_post_7_name"
msgstr "Jonas Wallin"

msgid "martin_social_feed_post_7_nick"
msgstr "j_wallin"

msgid "martin_social_feed_post_8_name"
msgstr "Sarah Van Peterghem"

msgid "martin_social_feed_post_8_nick"
msgstr "sarah_cocolapine"

msgid "material_veneer"
msgstr "Particle board + veneer"

msgid "mention_me_tag"
msgstr "Refer friends"

msgid "menu.mega.discover_slot_desks"
msgstr "Explore our Desks"

msgid "menu_bar_b2b_header"
msgstr "TYLKO FOR BUSINESS"

msgid "menu_bar_b2b_header_smallcaps"
msgstr "Tylko for Business"

msgid "menu_bar_inspiration_header"
msgstr "INSPIRATION"

msgid "menu_bar_inspiration_header_smallcaps"
msgstr "Inspiration"

msgid "menu_bar_kids_room"
msgstr "Kids’ Room"

msgid "menu_bar_shop_header"
msgstr "SHOP"

msgid "menu_bar_shop_header_smallcaps"
msgstr "Shop"

msgid "menu_bar_spaces_all"
msgstr "All rooms"

msgid "menu_bar_spaces_bedroom"
msgstr "Bedroom"

msgid "menu_bar_spaces_hallway"
msgstr "Hallway"

msgid "menu_bar_spaces_header"
msgstr "ROOMS"

msgid "menu_bar_spaces_living_room"
msgstr "Living room"

msgid "menu_bar_spaces_office"
msgstr "Studio office"

msgid "menu_layer_tile_longer_title_product_lines"
msgstr "Product lines: learn about their unique features"

msgid "menu_layer_tile_longer_title_spaces"
msgstr "Spaces: create harmony in every room"

msgid "menu_layer_tile_longer_title_ugc"
msgstr "Shop the look: get inspired&nbsp;by our community"

msgid "menu_mega_discover_slot_t13"
msgstr "Just in: meet our new wardrobe"

msgid "metatags_contact_description_1"
msgstr "Contact the Tylko team if you have any questions relating to what we do."

msgid "metatags_contact_title_1"
msgstr "Any questions for Tylko?"

msgid "metatags_faq_description_1"
msgstr "At Tylko we work really hard to constantly improve our service. If you have any questions relating to our products, payments or returns, see the FAQ page."

msgid "metatags_faq_title_1"
msgstr "Any questions? Browse through Tylko's FAQs"

msgid "metatags_forgotpassword_1"
msgstr "Forgot your tylko password?"

msgid "metatags_forgotpassword_description_1"
msgstr "See how you can reset your Tylko password."

msgid "metatags_gettheapp_description_1"
msgstr "Our interior design software allows you to see your Tylko Shelf in your home before you buy it."

msgid "metatags_gettheapp_title_1"
msgstr "The Tylko App - Customise furniture with augmented reality"

msgid "metatags_homepage_1"
msgstr "Tylko - Bespoke designer furniture. Discover our custom designs."

msgid "metatags_homepage_description_1"
msgstr "Tailor-made shelves and wardrobes that fit seamlessly into everyday life. Free delivery and no-questions-asked returns for 100 days. "

msgid "metatags_ivy_1"
msgstr "Shop the Perfect Shelf Online with Free Delivery & Returns - Tylko.com"

msgid "metatags_ivy_description_1"
msgstr "Shop the Tylko Shelf custom-made to a centimeter. Each designer piece can be your perfect kitchen, wardrobe or livingroom shelf."

msgid "metatags_library_description_1"
msgstr "Check your saved designs from tylko app on desktop"

msgid "metatags_library_title_1"
msgstr "Save your favourite designs - Tylko"

msgid "metatags_our_mission_title_1"
msgstr "Our mission: \"A Perfect-fit Shelf\" - Tylko"

#: frontend_cms/templates/front/press.html
msgid "metatags_press_description_1"
msgstr "For press enquiries please e-mail <NAME_EMAIL>."

msgid "metatags_press_title_1"
msgstr "Information for press - Tylko"

msgid "metatags_product_lines_title"
msgstr "Tylko - Bespoke designer furniture. Discover our product lines."

msgid "metatags_refer_description_1"
msgstr "It's easy to share the wealth! Give and get your rewards easily."

msgid "metatags_refer_title_1"
msgstr "Tell your friends about Tylko!"

msgid "metatags_register_or_login_title_1"
msgstr "Sign up or log in to your Tylko account"

msgid "metatags_register_title_1"
msgstr "Create your Tylko account"

msgid "metatags_shelf_benches_title_1"
msgstr "Tylko Bench. Shop perfect-fit Storage Benches"

msgid "metatags_shelf_bookshelves_title_1"
msgstr "Tylko Bookshelf. Design your own bookcase"

msgid "metatags_shelf_sideboards_title_1"
msgstr "Tylko Sideboard. Shop sideboards online with free delivery"

msgid "metatags_shelf_story_description_1"
msgstr "Your dearest things all have a story. Explore the story behind making of every Tylko Shelf."

msgid "metatags_shelf_wallstorage_title_1"
msgstr "Tylko Wall Storage. The best customized furniture"

msgid "metatags_shipping_description_1"
msgstr "All the information regarding shipping and returns of the Tylko furniture may be found here."

msgid "metatags_shipping_title_1"
msgstr "How we ship Tylko furniture? Shipping methods and destinations"

msgid "metatags_signup_description_1"
msgstr "Create a new acccount or log in to save and browse through your custom designs."

msgid "metatags_story_title_1"
msgstr "How did we start our brand? Read the Tylko story"

msgid "metatags_tylko_story_description_1"
msgstr "A Mission to revolutionise furniture industry. Perfectly fitted shelf for every home."

msgid "mixed_colors_cashmerebeige_antiquepink"
msgstr "Cashmere Beige + Antique Pink"

msgid "mixed_colors_cashmerebeige_cashmerebeige"
msgstr "Cashmere Beige + Cashmere Beige"

msgid "mixed_colors_cashmerebeige_mistyblue"
msgstr "Cashmere Beige + Misty Blue"

msgid "mixed_colors_cashmerebeige_sagegreen"
msgstr "Cashmere Beige + Sage Green "

msgid "mixed_colors_cashmerebeige_stonegrey"
msgstr "Cashmere Beige + Stone Grey"

msgid "mixed_colors_graphitegrey_antiquepink"
msgstr "Graphite Grey + Antique Pink"

msgid "mixed_colors_graphitegrey_graphitegrey"
msgstr "Graphite Grey + Graphite Grey "

msgid "mixed_colors_graphitegrey_mistyblue"
msgstr "Graphite Grey + Misty Blue "

msgid "mixed_colors_graphitegrey_sagegreen"
msgstr "Graphite Grey + Sage Green"

msgid "mixed_colors_graphitegrey_stonegrey"
msgstr "Graphite Grey + Stone Grey"

msgid "mixed_colors_white_antiquepink"
msgstr "White + Antique Pink"

msgid "mixed_colors_white_mistyblue"
msgstr "White + Misty Blue"

msgid "mixed_colors_white_sagegreen"
msgstr "White + Sage Green"

msgid "mixed_colors_white_stonegrey"
msgstr "White + Stone Grey "

msgid "mixed_colors_white_white"
msgstr "White + White"

msgid "mm_id"
msgstr "This is our tracking ID for referral program which serves to identify any unique browser using our flow."

msgid "mobile_button_apply"
msgstr "Apply"

msgid "mobile_common_configurator_depth_vinyls_limit"
msgstr "Vinyl Storage is available in 40cm depth only."

msgid "mobile_common_style"
msgstr "Style"

msgid "mobile_webgl_configurator_rotate_text"
msgstr "Rotate to edit"

msgid "mobile_webgl_configurator_rows"
msgstr "Rows"

msgid "new_colors_grid_meta_description"
msgstr "The Tylko Original Modern is now available in three modern colours that will surely add character to your space. It’s time to get creative and design unique, perfect-fit shelves for your stylish home. "

msgid "new_colors_grid_meta_title"
msgstr "Introducing a fresh new palette to update your home"

msgid "new_heights_alert_buton_2"
msgstr "Got it"

msgid "new_heights_alert_header_1"
msgstr "Heads up!"

msgid "new_heights_alert_paragraph_3"
msgstr "We've updated our row heights to 18, 28 and 38cm measurements and changed our drawer materials. Please check over your design to see the changes!"

msgid "new_newsletter_enter_email"
msgstr "Enter your Email"

msgid "newsletter_confirmation_button"
msgstr "Join us"

msgid "newsletter_confirmation_header"
msgstr "Subscription confirmed!"

msgid "newsletter_confirmation_text_1"
msgstr "Thanks, you’re on the list. We’ll send you some good stuff soon!"

msgid "newsletter_confirmation_text_2"
msgstr "Until then, why not join us over on Instagram to see our latest styles and soak up some interior inspiration?"

msgid "ola_checkout_country_popup_t03_button_1"
msgstr "Don't change country"

msgid "ola_checkout_country_popup_t03_button_2"
msgstr "Change and delete items"

msgid "ola_checkout_country_popup_t03_description"
msgstr "You've selected to switch to a country where the Tone Wardrobe is currently unavailable. If you continue, all Tone Wardrobe items in your cart will be automatically deleted. Do you still wish to proceed?"

msgid "ola_checkout_country_popup_t03_header"
msgstr "Change country?"

msgid "ola_checkout_input_cop_4"
msgstr "First Name"

msgid "ola_comparison_cat_button"
msgstr "Explore category"

msgid "ola_comparison_colours_header_1"
msgstr "Timeless tones that make a statement."

msgid "ola_comparison_colours_header_2"
msgstr "Fresh hues and cool contrasts."

msgid "ola_comparison_colours_header_3"
msgstr "Natural wood finishes with unique grains."

msgid "ola_comparison_cta_1"
msgstr "Shop all shelves"

msgid "ola_comparison_cta_2"
msgstr "Shop"

msgid "ola_comparison_elevator_header_1"
msgstr "Essential design that's made to last and won't go out of style."

msgid "ola_comparison_elevator_header_2"
msgstr "Bold and ultra-modern shelf for homes that have a lot to say."

msgid "ola_comparison_elevator_header_3"
msgstr "Natural and thoughtfully made - a modern take on a furniture classic."

msgid "ola_comparison_ex_header_1"
msgstr "Explore our shelf ideas"

msgid "ola_comparison_faq_link"
msgstr "Check out our comparison page"

msgid "ola_comparison_features_header_1"
msgstr "Details that matter"

msgid "ola_comparison_features_header_1_text"
msgstr "Form meets function with optional details like doors,<br>drawers and more."

msgid "ola_comparison_features_header_1_text_t1_1"
msgstr "Rounded, extruded aluminium handles that run the full height of the door. Safe and easy to grip."

msgid "ola_comparison_features_header_1_text_t1_2"
msgstr "Self-closing, with extruded aluminium full-length handles, and full extension runners for easy access."

msgid "ola_comparison_features_header_1_text_t1_3"
msgstr "Exposed and hand-oiled to highlight the natural grain of the wood."

msgid "ola_comparison_features_header_1_text_t2_1"
msgstr "Doors with flush, ultra-modern handles and half-cylinder aluminium pulls. Sleek and understated."

msgid "ola_comparison_features_header_1_text_t2_2"
msgstr "Self-closing, with flush, extruded aluminium handles, and extendable runners for deep access."

msgid "ola_comparison_features_header_1_text_t2_3"
msgstr "Seamlessly wrapped for a bold, smooth finish from every angle."

msgid "ola_comparison_features_header_1_text_t3_1"
msgstr "Solid wood handles designed in-house to perfectly match the veneer doors. Warm and tactile."

msgid "ola_comparison_features_header_1_text_t3_2"
msgstr "Self-closing, with solid wood full-length handles and extendable runners for deep access."

msgid "ola_comparison_features_header_1_text_t3_3"
msgstr "Bonded with real wood veneer for a solid and seamless look."

msgid "ola_comparison_features_subheader_1"
msgstr "Edges"

msgid "ola_comparison_features_subheader_2"
msgstr "Doors"

msgid "ola_comparison_features_subheader_3"
msgstr "Drawers"

msgid "ola_comparison_features_subheader_4"
msgstr "Base"

msgid "ola_comparison_features_subheader_5"
msgstr "Cable management"

msgid "ola_comparison_features_t01p_cable"
msgstr "Steel hole with magnetic cover to keep cables tidy. Colour-matched to the shelf for a seamless look."

msgid "ola_comparison_features_t01v_cable"
msgstr "Steel hole with magnetic cover to keep cables tidy. In stylish accent colour - Dusty Pink for Oak or Sand Beige for White Oak."

msgid "ola_comparison_features_t01v_plinth"
msgstr "Elevate your storage with a real wood veneer plinth. Perfectly matched to each shelf."

msgid "ola_comparison_features_t02_cable"
msgstr "Steel hole with magnetic cover to keep cables tidy. Colour-matched to the shelf for a seamless look."

msgid "ola_comparison_features_t02_plinth"
msgstr "Steel legs with durable plastic bottom covers. Offer superior stability and an elevated look."

msgid "ola_comparison_material_header_1"
msgstr "Tech specs"

msgid "ola_comparison_material_header_1_t1_1"
msgstr "Premium 18 mm plywood made of 13 layers of North-European birch hardwood."

msgid "ola_comparison_material_header_1_t1_2"
msgstr "30 kg average load bearing per compartment. Tylko Original Classic in Plywood has larger compartments thanks to the strong, durable plywood construction."

msgid "ola_comparison_material_header_1_t2_1"
msgstr "Innovative non-toxic 18 mm particle board that's designed for durability."

msgid "ola_comparison_material_header_1_t2_2"
msgstr "30 kg average load bearing per compartment. The Original Modern has higher-density compartments that lend it a firmer, more compact look."

msgid "ola_comparison_material_header_1_t3_1"
msgstr "18 mm particle board bonded with a top layer of premium-grade oak or ash real wood veneer"

msgid "ola_comparison_material_header_1_t3_2"
msgstr "30 kg average load bearing per compartment. Tylko Original Classic in Veneer has higher-density compartments for sturdy, stable storage. Load bearing capacity depends on shelf configuration."

msgid "ola_comparison_material_subheader_1"
msgstr "Materials"

msgid "ola_comparison_page_menu"
msgstr "Product Lines"

msgid "ola_comparison_page_title"
msgstr "Find your <br class=\"mobile-visible\">perfect Tylko <br class=\"mobile-visible\">shelf"

msgid "ola_comparison_page_title_button_1"
msgstr "Learn more"

msgid "ola_comparison_page_title_button_2"
msgstr "Order samples"

msgid "ola_comparison_sample_body"
msgstr "What's your flavour? Order a sample kit to take a closer look at the selection of available colours for every shelf. "

msgid "ola_comparison_sample_body_t03"
msgstr "Order a sample kit to get a look and feel of the colours and premium materials used to create the Tone Wardrobe. Every element was thought through to give you sleek, smart storage that you'll love for a very long time."

msgid "ola_comparison_sample_header"
msgstr "Sample kits for Tylko Original Shelves"

msgid "ola_comparison_sample_header_t03"
msgstr "Sample kits for the Tone Wardrobe"

msgid "ola_comparison_sample_menu"
msgstr "Material Samples"

msgid "ola_comparison_sample_set1001_name"
msgstr "White Tone Wardrobe sample kit  "

msgid "ola_comparison_sample_set1002_name"
msgstr "Cashmere Beige Tone Wardrobe Sample Kit"

msgid "ola_comparison_sample_set1003_name"
msgstr "Cashmere Beige + Antique pink Tone Wardrobe sample kit "

msgid "ola_comparison_sample_set1004_name"
msgstr "Graphite Grey Tone Wardrobe sample kit"

msgid "ola_comparison_sample_set1005_name"
msgstr "White + Antique Pink Tone Wardrobe sample kit"

msgid "ola_comparison_sample_set1006_name"
msgstr "Graphite Grey + Antique Pink Tone Wardrobe Sample Kit"

msgid "ola_comparison_sample_set1007_name"
msgstr "The Tone White Set"

msgid "ola_comparison_sample_set1010_name"
msgstr "The Tone Subtle Shades Set"

msgid "ola_comparison_sample_set102_header"
msgstr "Sample kits for the Natural Set"

msgid "ola_comparison_sample_set102_name"
msgstr "The Natural Set"

msgid "ola_comparison_sample_set103_header"
msgstr "Sample kits for the Bold Set"

msgid "ola_comparison_sample_set104_header"
msgstr "Sample kits for The Staple Set"

msgid "ola_comparison_sample_set1102_name"
msgstr "The Monochrome Set"

msgid "ola_comparison_sample_set1103_name"
msgstr "The Edge Earthtone Set"

msgid "ola_comparison_sample_set111_name"
msgstr "The Statement Set"

msgid "ola_comparison_sample_set112_name"
msgstr "The Grey Set"

msgid "ola_comparison_sample_set113_name"
msgstr "The Classic Set II"

msgid "ola_comparison_sample_set114_name"
msgstr "The Woody Set"

msgid "ola_comparison_sample_set1201_the_warm_set"
msgstr "The Warm Set"

msgid "ola_comparison_sample_set1_cta"
msgstr "Add to cart"

msgid "ola_comparison_sample_set1_name"
msgstr "Tylko Original Classic Sample Set"

msgid "ola_comparison_sample_set2_name"
msgstr "Tylko Original Classic Sample Set"

msgid "ola_comparison_sample_set3_name"
msgstr "Tylko Original Modern Sample Set"

msgid "ola_comparison_sample_set4_name"
msgstr "Tylko Original Classic Sample Set"

msgid "ola_comparison_sample_set5_name"
msgstr "Tylko Original Modern Sample Set"

msgid "ola_comparison_sample_set6_name"
msgstr "Bold Colours Sample Set"

msgid "ola_comparison_sample_set7_name"
msgstr "Tylko Original Modern Sample Set"

msgid "ola_comparison_sample_set9_name"
msgstr "The Mix Set"

msgid "ola_comparison_to03_tooltip_body1"
msgstr "18mm particle board used for the wardrobe's core elements."

msgid "ola_comparison_to03_tooltip_body2"
msgstr "12mm MDF board with mat finish used for the wardrobe's doors and frames."

msgid "ola_comparison_to03_tooltip_body3"
msgstr "Powder-coated aluminium used for the wardrobe's handles and hanging rack."

msgid "ola_comparison_to03_tooltip_body4"
msgstr "12mm particle board used for the wardrobe's rear panel. "

msgid "ola_comparison_to03_tooltip_header"
msgstr "What's in the box"

msgid "ola_create_account_checkbox_1"
msgstr "The data controller is Tylko S.A. (hereinafter also referred to as \"Controller\"). The Controller can be <NAME_EMAIL>. You can contact the Controller on all matters relating to the processing of personal data and the use of data processing rights. Your data will be processed in order to fulfil the contract. The legal basis for the processing is art. 6(1)(b) of the GDPR. <a href=\"https://tylko.com/privacy_policy/\">See more.</a>"

msgid "ola_faq_header_4_answer_2.0"
msgstr "We accept MasterCard and Visa, as well as other local payment options, which depend on the country you're in:"

msgid "ola_faq_header_4_answer_2.1"
msgstr "Germany: International Bank Transfer, SEPA Direct Debit, Klarna "

msgid "ola_faq_header_4_answer_2.2"
msgstr "Austria: Bank Transfer, SEPA Direct Debit, Klarna"

msgid "ola_faq_header_4_answer_2.3"
msgstr "France: Credit card (MasterCard, Visa, American Express et Diners Club), Direct Debit, Bank Transfer"

msgid "ola_faq_header_4_answer_2.4"
msgstr "United Kingdom: MasterCard, Visa, International Bank Transfer, SEPA"

msgid "ola_faq_header_4_answer_2.5"
msgstr "All transactions (except Klarna) are processed securely via our payment provider Adyen."

msgid "ola_faq_header_4_question_2"
msgstr "What payment options are available?"

msgid "ola_faq_header_5_answer_1"
msgstr "We make every piece of furniture with top-quality materials that stand up to tough daily use. For extra peace of mind, we give you a solid 2-year guarantee. For more details and the conditions, please read our <a href='https://tylko.com/terms/'>Terms of Service</a>."

msgid "ola_faq_header_5_question_1"
msgstr "Does Tylko furniture come with a guarantee?"

msgid "ola_faq_header_6_answer_2"
msgstr "Tylko furniture is delivered in flat pack boxes by well-known delivery companies, directly to your door. Based on the delivery address and size of your order, we'll choose the most appropriate courier to make sure your one-of-a-kind piece is cared for correctly."

msgid "ola_faq_header_6_answer_4"
msgstr "We offer free shipping to: Austria, Belgium, Bulgaria, Croatia, Czech, Denmark, Estonia, Finland, France, Germany, Greece, Hungary, Ireland, Italy, Latvia, Lithuania, Luxembourg, the Netherlands, Norway, Poland, Portugal, Romania, Slovakia, Slovenia, Spain, Sweden, Switzerland, Norway and the United Kingdom.<br><br>If you’re shipping from: Monaco, San Marino, Gibraltar, Andorra, Liechtenstein, Bosnia and Herzegovina, Cyprus, Montenegro, Albania, Iceland, Kosovo, Macedonia, Malta, Moldova and Serbia, we are happy to give you a quote. Just send us a screenshot of your design, along with your zip/post code and <NAME_EMAIL>, and we'll get back to you with the shipping cost.<br><br>Unfortunately, we don’t offer shipping to countries that are not listed above. However, we're continuously working hard towards offering shipping in as many countries as possible."

msgid "ola_faq_header_6_question_2"
msgstr "How is Tylko furniture delivered?"

msgid "ola_faq_header_6_question_4"
msgstr "Which countries do you offer free shipping to?"

msgid "ola_faq_t01p_answer_1"
msgstr "Both lines offer the same four styles and customisation options, but they also have their own personalities. Available in three classic colours, Tylko Original Classic in Plywood is made from 13 layers of durable plywood wrapped in laminate and features aluminium handles designed in-house. Tylko Original in Veneer is made from particle board with a natural veneer topper and is available in two earthy tones inspired by nature, made complete with solid wood handles for a tactile experience."

msgid "ola_faq_t01p_answer_2"
msgstr "Plywood is made from compressed layers of solid wood. Its strength makes it an excellent choice for long-lasting furniture design. Our premium-grade plywood is sourced from hardened European birchwood and layered together with non-toxic glue."

msgid "ola_faq_t01p_question_1"
msgstr "What’s the difference between Tylko Original Classic in Plywood and Veneer?"

msgid "ola_faq_t01p_question_2"
msgstr "What is plywood?"

msgid "ola_faq_t01v_answer_1"
msgstr "Both lines offer the same four styles and customisation options, but they also have their own personalities. Available in three classic colours, Tylko Original Classic in Plywood is made from 13 layers of durable plywood wrapped in laminate and features aluminium handles designed in-house. Tylko Original Classic in Veneer is made from particle board with a natural veneer topper and is available in two earthy tones inspired by nature, made complete with solid wood handles for a tactile experience."

msgid "ola_faq_t01v_answer_2"
msgstr "Veneer is a thin decorative furniture finish made of solid wood. Our collection includes oak and ash veneers, both having European origins and production. You can read more about the strength and beauty of this material <a href=\"https://tylko.com/journal/5-step-guide-to-veneer/\">here</a>."

msgid "ola_faq_t01v_answer_3"
msgstr "Absolutely! Each shelf is made from emission-free, non-toxic particle board, and features a natural veneer top and a water-based lacquer that’s safe for little and large people. We offer rounded handles and soft-close doors and drawers so no fingers can get caught, as well as wall mounts for extra stability. Because of our furniture’s ergonomic features, our furniture is suitable to be handled by children over the age of 12."

msgid "ola_faq_t01v_answer_4"
msgstr "We make every effort to protect veneer covers from moisture, including using waterproof glues and lacquers. However, we don't recommend placing your Tylko shelf in the bathroom. Long-term exposure to water or moisture may change its original look."

msgid "ola_faq_t01v_question_1"
msgstr "What’s the difference between Tylko Original Classic in Plywood and Veneer?"

msgid "ola_faq_t01v_question_2"
msgstr "What is veneer?"

msgid "ola_faq_t01v_question_3"
msgstr "Is a Tylko Original Classic Shelf in Veneer safe to use in a child’s room?"

msgid "ola_faq_t01v_question_4"
msgstr "Can I place a Tylko Original Classic Shelf  in Veneer in my bathroom?"

msgid "ola_menu_bar_learn_more_header"
msgstr "Discover more"

msgid "ola_menu_bar_line_4"
msgstr "Tone"

msgid "ola_menu_bar_style"
msgstr "All Styles"

msgid "ola_menu_bar_use_1"
msgstr "Sideboard"

msgid "ola_menu_bar_use_1_plural"
msgstr "Sideboards"

msgid "ola_menu_bar_use_2"
msgstr "Bookcase"

msgid "ola_menu_bar_use_2_plural"
msgstr "Bookcases"

msgid "ola_menu_bar_use_3"
msgstr "Vinyl Storage"

msgid "ola_menu_bar_use_3_plural"
msgstr "Vinyl Storage"

msgid "ola_menu_bar_use_4"
msgstr "TV Stand"

msgid "ola_menu_bar_use_4_plural"
msgstr "TV Stands"

msgid "ola_menu_bar_use_5"
msgstr "Shoe Rack"

msgid "ola_menu_bar_use_5_plural"
msgstr "Shoe Racks"

msgid "ola_menu_bar_use_6"
msgstr "Chest of Drawers"

msgid "ola_menu_bar_use_6_plural"
msgstr "Chest of Drawers"

msgid "ola_menu_bar_use_7"
msgstr "Wardrobe"

msgid "ola_menu_bar_use_7_plural"
msgstr "Wardrobes"

msgid "ola_menu_bar_use_header"
msgstr "Category"

msgid "ola_press"
msgstr "Press & Media"

msgid "ola_press_section_1_body_1"
msgstr "Tylko improves homes with premium storage furniture that caters to individual needs. Our online configurator and Tylko AR App allows you to customise your shelf intuitively, hassle-free and made to fit."

msgid "ola_press_section_1_header"
msgstr "Company"

msgid "ola_press_section_1_subheader_1"
msgstr "Our story"

msgid "ola_press_section_2_header"
msgstr "Products"

msgid "ola_press_section_3_header"
msgstr "Technology"

msgid "ola_press_section_4_disclaimer"
msgstr "All our furniture is personalised and produced on demand. Feel free to reach out to us if you have any specific image requests."

msgid "ola_press_section_4_email"
msgstr "<a href = \"mailto: <EMAIL>\"><EMAIL></a>"

msgid "ola_press_section_4_header"
msgstr "For press enquiries please contact <br class=\"press__break\">:"

msgid "ola_press_section_downloads"
msgstr "Downloads"

msgid "ola_privacypolicy_note_newsletter"
msgstr "I agree to receive super cool content from Tylko in the form of occasional emails. The data controller is Tylko S.A. Your data will be processed in order to keep you informed about our products and services. The legal basis for the processing is your consent (art. 6(1)(a) of the GDPR)."

msgid "ola_reviewslist_verified_description_1"
msgstr "A Verified Tylko Customer is someone who has purchased a shelf from our online store."

msgid "ola_reviewslist_verified_header_1"
msgstr "Verified Tylko Customer"

msgid "ola_samples_hp_button"
msgstr "Order samples"

msgid "ola_shipping"
msgstr "Shipping & Returns"

msgid "ola_shipping_header_1"
msgstr "Shipping Method"

msgid "ola_shipping_header_1_text_1"
msgstr "Tylko furniture is delivered by only the most reputable delivery companies, directly to your door. Our goal is to provide you with a first-class service. Depending on the delivery address and size of your order, we'll choose the most efficient courier."

msgid "ola_shipping_header_2"
msgstr "Shipping Time"

msgid "ola_shipping_header_2_text_1"
msgstr "Shipping time varies based on several factors, including our factory's product capacity and stock level. The configurator will automatically adjust the shipping time as you change the design."

msgid "ola_shipping_header_3"
msgstr "Shipping Destinations"

msgid "ola_shipping_header_3_text_1"
msgstr "Shipping is free for all EU countries: Austria, Belgium, Bulgaria, Croatia, Czech Rep., Denmark, Estonia, Finland, France, Germany, Greece, Hungary, Ireland, Italy, Latvia, Lithuania, Luxembourg, Netherlands, Poland, Portugal, Romania, Slovakia, Slovenia, Spain, Sweden, Switzerland and Norway.<br>Shipping is also free to the United Kingdom. "

msgid "ola_status_body_10"
msgstr "Something went wrong with your payment. It could not be processed.<br><br>Please use the link below to try again, or contact us:"

msgid "ola_status_body_11"
msgstr "We couldn't find this order.  Please check the post code and order number you have inserted and try again."

msgid "ola_status_body_2"
msgstr "Your order has been cancelled.<br><br>If you have already paid for it, you should receive a corrected invoice soon. Simply accept it and we will refund your money within 14 days. <br><br>If you have any additional questions, please get in touch:"

msgid "ola_status_body_3"
msgstr "We have received your order.<br><br>If you would like to finalise it, please head to your cart and make the payment.<br><br>If you have any additional questions, please get in touch:"

msgid "ola_status_body_4"
msgstr "We are waiting to receive the payment for your order.<br><br>If you paid via international bank transfer, please note that it can take up to 7 days to process. If you would like to speed up this process, please send us proof of payment via email: <EMAIL>. <br><br>If you haven't paid yet, please use the link below and choose one of the payment methods.<br><br>If you have any additional questions, please get in touch: "

msgid "ola_status_body_5"
msgstr "Your order has been produced and is ready for shipping. <br><br>You should receive an email regarding shipping details within 1 business day. If you don’t, please wait one more business day and check your spam folder. Still nothing? Please contact us."

msgid "ola_status_body_6_{}"
msgstr "Your order is being produced and should be ready by {}. <br><br>Once finished, we will send you an email regarding delivery. <br><br>If you have any additional questions, please get in touch: "

msgid "ola_status_body_7"
msgstr "Your order is on the way with TNT!<br><br>Please check your email for all the shipping details, including the tracking number.<br><br>If you have any questions regarding the delivery or want to change the date, please contact TNT directly."

msgid "ola_status_body_8"
msgstr "Your order is on the way!<br><br>You should have already received your shipping details via email. <br><br>If you have any questions regarding delivery or want to change the date, please contact us:"

msgid "ola_status_body_9"
msgstr "Your order has been delivered. Exciting!<br><br>If you have any questions regarding assembly, please see the tips or get in touch with us.<br><br>Enjoy your new Tylko shelf!"

#, python-brace-format
msgid "ola_status_headline_10_{id}"
msgstr "Payment for order number {id} has failed."

msgid "ola_status_headline_11"
msgstr "Order not found"

#, python-brace-format
msgid "ola_status_headline_2_{id}"
msgstr "Order {id} has been cancelled"

#, python-brace-format
msgid "ola_status_headline_3_{id}"
msgstr "Order number {id} is pending"

#, python-brace-format
msgid "ola_status_headline_4_{id}"
msgstr "Payment for order number {id} is pending"

#, python-brace-format
msgid "ola_status_headline_5_{id}"
msgstr "Order number {id} is ready to be shipped"

#, python-brace-format
msgid "ola_status_headline_6_{id}"
msgstr "Order number {id} is being produced"

#, python-brace-format
msgid "ola_status_headline_7_{id}"
msgstr "Order number {id} has been shipped"

#, python-brace-format
msgid "ola_status_headline_8_{id}"
msgstr "Order number {id} has been shipped"

#, python-brace-format
msgid "ola_status_headline_9_{id}"
msgstr "Order number {id} has been delivered"

msgid "ola_wishlist_answer_1"
msgstr "Yes, delete"

msgid "ola_wishlist_answer_2"
msgstr "No, keep"

msgid "ola_wishlist_empty"
msgstr "Your wishlist is empty"

msgid "ola_wishlist_header"
msgstr "You've got 1 item in your wishlist"

msgid "ola_wishlist_header_1"
msgstr "You've got"

msgid "ola_wishlist_header_2"
msgstr "items in your wishlist"

msgid "ola_wishlist_question"
msgstr "Are you sure you want to delete this<br>item from your wishlist?"

msgid "openend_module_cover"
msgstr "Cover for open end"

msgid "openend_moodule"
msgstr "Open end"

#: mailing/templates/mails/mail_invite_from_app.html mailing/templates/mails/mail_order_status_changed.html
msgid "or"
msgstr "or"

msgid "order_status_account_check_status_link"
msgstr "Check your order status"

msgid "order_status_header_cta"
msgstr "Check status"

msgid "order_status_header_input_email_error"
msgstr "Please enter a valid order number"

msgid "order_status_header_input_email_label"
msgstr "Order number"

msgid "order_status_header_input_email_placeholder"
msgstr "Enter your order number"

msgid "order_status_header_input_postcode_error"
msgstr "Please enter a valid postcode"

msgid "order_status_header_input_postcode_hint"
msgstr "Feel free to use the postcode included in your delivery or billing address — either of those is totally fine with us.  "

msgid "order_status_header_input_postcode_label"
msgstr "Postcode"

msgid "order_status_header_input_postcode_placeholder"
msgstr "Enter your postcode"

msgid "order_status_header_title"
msgstr "Check your order status"

msgid "ottoman_module"
msgstr "Ottoman"

msgid "ottoman_module_cover"
msgstr "Cover for ottoman"

#, python-brace-format
msgid "%(count)d day"
msgid_plural "days"
msgstr[0] "day"
msgstr[1] "days"

#, python-brace-format
msgid "%(count)d hour"
msgid_plural "hours"
msgstr[0] "hour"
msgstr[1] "hours"

#, python-brace-format
msgid "%(count)d minute"
msgid_plural "minutes"
msgstr[0] "minute"
msgstr[1] "minutes"

#, python-brace-format
msgid "%(count)d month"
msgid_plural "months"
msgstr[0] "month"
msgstr[1] "months"

#, python-brace-format
msgid "%(count)d second"
msgid_plural "seconds"
msgstr[0] "second"
msgstr[1] "seconds"

#, python-brace-format
msgid "%(count)d week"
msgid_plural "weeks"
msgstr[0] "week"
msgstr[1] "weeks"

#, python-brace-format
msgid "%(count)d year"
msgid_plural "years"
msgstr[0] "year"
msgstr[1] "years"

msgid "particleboard_t13"
msgstr "Particle board"

msgid "particleboard_with_plywood_t13"
msgstr "Particle board with plywood"

msgid "payment_cvcinfo_americanexpres_header"
msgstr "American Express cards:"

msgid "payment_cvcinfo_header"
msgstr "Visa, MasterCard, Diner's Club, Discover cards and more:"

msgid "payment_header_paycreditcard"
msgstr "Pay With Credit Card"

msgid "payment_text_secure"
msgstr "We keep you secure using 128 bit encryption and never store your credit card details."

msgid "pd_sample_mail_1_button"
msgstr "Create Your Tylko"

msgid "pd_sample_mail_1_header"
msgstr "<strong>Hey There,</strong><br><br>Picking the perfect storage from Tylko can be a big choice, so we thought we’d share some inspiration from real customers that might help you&nbsp;decide. "

msgid "pd_sample_mail_1_preheader"
msgstr "Here’s some picks from real people."

msgid "pd_sample_mail_1_text_1"
msgstr "Here are just a few of our recent faves:"

msgid "pd_sample_mail_1_text_2"
msgstr "<strong>Seen something you like?</strong> <br><br>Head over to the online configurator to&nbsp;get&nbsp;started!"

msgid "pd_sample_mail_1_topic"
msgstr "Still looking over those samples?"

msgid "pd_sample_mail_2_button_1"
msgstr "See More Reviews"

msgid "pd_sample_mail_2_button_2"
msgstr "Get Started"

msgid "pd_sample_mail_2_headline"
msgstr "At Tylko, we know that samples don’t tell the whole story, so we wanted to share what our customers are saying about their storage:"

msgid "pd_sample_mail_2_preheader"
msgstr "Let us break it down for you."

msgid "pd_sample_mail_2_review_1_author"
msgstr "Rasmus - Denmark"

msgid "pd_sample_mail_2_review_1_text"
msgstr "Better than I even imagined. I don’t understand how you can make something so easy to assemble. No doubt my next shelves will be from Tylko!"

msgid "pd_sample_mail_2_review_2_author"
msgstr "Chris - Austria"

msgid "pd_sample_mail_2_review_2_text"
msgstr "I was so impressed with the quality of the materials, but most of all I enjoyed that I could customize it to fit my wall exactly."

msgid "pd_sample_mail_2_review_3_author"
msgstr "Rachel - United Kingdom"

msgid "pd_sample_mail_2_review_3_text"
msgstr "The website functionality is great, and I was able to customise my sideboards down to the millimetre. The units are beautifully made, incredibly good value and a perfect fit with a 1.5 mm gap either side of the wall - precision engineered&nbsp;! "

msgid "pd_sample_mail_2_text_2"
msgstr "Deciding on Tylko doesn’t have to be daunting. We offer free 100-day returns so you can let your storage settle in with no hassle or hurry. <br><br>Ready to see what you can create?"

msgid "pd_sample_mail_2_topic"
msgstr "What do our samples say about us?"

msgid "pd_sample_mail_3_button"
msgstr "Shop Now"

msgid "pd_sample_mail_3_button_2"
msgstr "Shop Now"

msgid "pd_sample_mail_3_list"
msgstr "<p style=\"margin:0!important;padding:0!important;\"> &bull;&nbsp;&nbsp;Use them as drink coasters</p><br><p style=\"margin:0!important;padding:0!important;\"> &bull;&nbsp;&nbsp;Bore out a small hole and create candle holders</p><br><p style=\"margin:0!important;padding:0!important;\"> &bull;&nbsp;&nbsp;Mount to the wall for tiny display shelves or key hooks</p><br><p style=\"margin:0!important;padding:0!important;\"> &bull;&nbsp;&nbsp;Use them as building blocks for children (our finishes and glues are completely non-toxic!)</p><br><p style=\"margin:0!important;padding:0!important;\"> &bull;&nbsp;&nbsp;Or as a spoon rest in the kitchen!</p>"

msgid "pd_sample_mail_3_preheader"
msgstr "Here are some creative ideas from customers like you!"

msgid "pd_sample_mail_3_text_1"
msgstr "So you’ve seen the samples<br> and felt the quality… now what? "

msgid "pd_sample_mail_3_text_2"
msgstr "We’ve gathered up some of the creative ways our customers cut down waste and give their sample sets a&nbsp;new life:"

msgid "pd_sample_mail_3_text_3"
msgstr "Whether you pass them on to prospective new Tylko owners to help them decide, or repurpose them in a&nbsp;creative way, giving your samples a&nbsp;second life is simple with a&nbsp;little imagination. "

#, python-format
msgid "pd_sample_mail_3_text_4_%(voucher_value)s_%(voucher)s"
msgstr "And, if you’re finished with your samples and ready&nbsp;to order, take <strong>%(voucher_value)s off</strong> on us. Just use the code <strong>%(voucher)s</strong> at checkout. (Exclusions apply)"

msgid "pd_sample_mail_3_text_6"
msgstr "Feeling creative? Why not channel that good energy and click below to get started designing your dream&nbsp;Tylko:"

msgid "pd_sample_mail_3_text_disclaimer"
msgstr "Code valid for 7 days."

msgid "pd_sample_mail_3_topic"
msgstr "Not sure what to do with those samples?"

msgid "pd_sample_mail_hi"
msgstr "Hi"

msgid "pdpT01p_cHigh_product_details_features_storage_body"
msgstr "<li>high doors that create a harmonious look</li><li>easy to mount shelf inserts</li><li>long handles that match the rest of the shelf</li><li>sleek interior, with no sight of unnecessary holes</li>"

msgid "pdpT01p_cHigh_product_details_features_storage_title"
msgstr "Additional storage"

msgid "pdpT01p_cHigh_section_body"
msgstr "Enjoy extra storage space that's perfectly fitted to keep all sorts of items neatly tucked away, yet still easy to access. "

msgid "pdpT01p_cHigh_section_title"
msgstr "Extra storage to make more space for what you love. "

msgid "pdpT01p_cHigh_section_toggle_bottom"
msgstr "Lower storage"

msgid "pdpT01p_cHigh_section_toggle_upper"
msgstr "Upper storage"

msgid "pdpT02_matte_black_body"
msgstr "It's the Tylko Original Modern you know and love, now upgraded with a super smooth, extra premium finish. In other words, we took it up a notch. "

msgid "pdpT02_matte_black_headline"
msgstr "You asked, we delivered: meet the Tylko Original Modern in Premium Matte Black"

msgid "pdp.configurator.modal.color.line.hint"
msgstr "pdp.configurator.modal.color.line.hint"

msgid "pdp.configurator.modal.color.sample.hint"
msgstr "pdp.configurator.modal.color.sample.hint"

msgid "pdp.configurator.modal.color.sample.link"
msgstr "pdp.configurator.modal.color.sample.link"

msgid "pdp.configurator.modal.color.section1.description"
msgstr "pdp.configurator.modal.color.section1.description"

msgid "pdp.configurator.modal.color.section1.label"
msgstr "pdp.configurator.modal.color.section1.label"

msgid "pdp.configurator.modal.color.section1.title"
msgstr "pdp.configurator.modal.color.section1.title"

msgid "pdp.configurator.modal.color.section2.description"
msgstr "pdp.configurator.modal.color.section2.description"

msgid "pdp.configurator.modal.color.section2.label"
msgstr "pdp.configurator.modal.color.section2.label"

msgid "pdp.configurator.modal.color.section2.title"
msgstr "pdp.configurator.modal.color.section2.title"

msgid "pdp.configurator.modal.color.section3.description"
msgstr "pdp.configurator.modal.color.section3.description"

msgid "pdp.configurator.modal.color.section3.label"
msgstr "pdp.configurator.modal.color.section3.label"

msgid "pdp.configurator.modal.color.section3.note"
msgstr "pdp.configurator.modal.color.section3.note"

msgid "pdp.configurator.modal.color.section3.title"
msgstr "pdp.configurator.modal.color.section3.title"

msgid "pdp.configurator.modal.color.section4.description"
msgstr "pdp.configurator.modal.color.section4.description"

msgid "pdp.configurator.modal.color.section4.label"
msgstr "pdp.configurator.modal.color.section4.label"

msgid "pdp.configurator.modal.color.section4.title"
msgstr "pdp.configurator.modal.color.section4.title"

msgid "pdp.configurator.modal.color.section5.description"
msgstr "pdp.configurator.modal.color.section5.description"

msgid "pdp.configurator.modal.color.section5.label"
msgstr "pdp.configurator.modal.color.section5.label"

msgid "pdp.configurator.modal.color.section5.title"
msgstr "pdp.configurator.modal.color.section5.title"

msgid "pdp.configurator.modal.color.section6.description"
msgstr "pdp.configurator.modal.color.section6.description"

msgid "pdp.configurator.modal.color.section6.label"
msgstr "pdp.configurator.modal.color.section6.label"

msgid "pdp.configurator.modal.color.section6.title"
msgstr "pdp.configurator.modal.color.section6.title"

msgid "pdp.configurator.modal.color.section7.description1"
msgstr "pdp.configurator.modal.color.section7.description1"

msgid "pdp.configurator.modal.color.section7.description2"
msgstr "pdp.configurator.modal.color.section7.description2"

msgid "pdp.configurator.modal.color.section7.label"
msgstr "pdp.configurator.modal.color.section7.label"

msgid "pdp.configurator.modal.color.section7.link1"
msgstr "pdp.configurator.modal.color.section7.link1"

msgid "pdp.configurator.modal.color.section7.link2"
msgstr "pdp.configurator.modal.color.section7.link2"

msgid "pdp.configurator.modal.color.section7.subtitile1"
msgstr "pdp.configurator.modal.color.section7.subtitile1"

msgid "pdp.configurator.modal.color.section7.subtitile2"
msgstr "pdp.configurator.modal.color.section7.subtitile2"

msgid "pdp.configurator.modal.color.section7.titile"
msgstr "pdp.configurator.modal.color.section7.titile"

msgid "pdp.configurator.modal.color.tab1"
msgstr "pdp.configurator.modal.color.tab1"

msgid "pdp.configurator.modal.color.tab2"
msgstr "pdp.configurator.modal.color.tab2"

msgid "pdp.configurator.modal.color.title"
msgstr "pdp.configurator.modal.color.title"

msgid "pdp.configurator.modal.color.vinyl2sideboard.classic.link"
msgstr "pdp.configurator.modal.color.vinyl2sideboard.classic.link"

msgid "pdp.configurator.modal.colors.go_to_another_configurator_venner"
msgstr "pdp.configurator.modal.colors.go_to_another_configurator_venner"

msgid "pf_availability_in_stock"
msgstr "in stock"

msgid "pf_condition_new"
msgstr "new"

msgid "pinterest_sharing_btn"
msgstr "This Cookie has been setup to provide users with the functionality of sharing their customised design on Pinterest."

msgid "popup_save_createaccount_button"
msgstr "No thanks"

msgid "popup_save_createaccount_button_main"
msgstr "Create account"

msgid "popup_save_createaccount_header1"
msgstr "Create a Tylko Account"

msgid "popup_save_createaccount_preheader"
msgstr "Your design has been saved! Check your inbox"

msgid "popup_save_createaccount_text"
msgstr "Save and view designs anytime, from any device."

msgid "popup_save_login_button"
msgstr "No thanks"

msgid "popup_save_login_button_main"
msgstr "Log in and save design"

msgid "popup_save_login_header1"
msgstr "Log in to your Tylko account."

msgid "popup_save_login_text"
msgstr "We’ll add this design to your wishlist to keep it safe and sound."

msgid "popup_save_send_button"
msgstr "Save this design"

msgid "popup_save_send_header1"
msgstr "Send this design to your email."

msgid "popup_save_send_text"
msgstr "Going somewhere? Make it easy to pick up where you left off."

msgid "pp_list_header_2"
msgstr "Shipping and taxes included"

msgid "pp_list_header_3"
msgstr "100-day free returns"

msgid "pp_list_header_norway_2"
msgstr "Logistics costs and taxes included"

msgid "pp_list_header_switzerland_2"
msgstr "Logistics costs and taxes included"

msgid "product_page_IVY_subheadline_1_1"
msgstr "Well-designed, durable and super easy to assemble. The perfect storage solution."

msgid "product_page_configurator_buton_save"
msgstr "Save for later"

msgid "product_page_configurator_buton_save_abtest1"
msgstr "Save my design"

msgid "product_page_configurator_width_extra_width"
msgstr "Extra width"

msgid "product_page_configurator_width_narrower"
msgstr "Narrower?"

msgid "product_page_configurator_width_wider"
msgstr "Wider?"

#, python-format
msgid "production_delay_email_hello_%(name)s"
msgstr "Hello %(name)s,"

#, python-format
msgid "production_delay_email_p1_%(order_id)s"
msgstr "We have an update for you on order %(order_id)s."

msgid "production_delay_email_p2"
msgstr "Despite our very best efforts, it looks like there will be a slight delay in production. We want to make sure your one-of-a-kind piece of furniture comes out perfect, and our extra checks and quality control procedures sometimes take a little more time."

#, python-format
msgid "production_delay_email_p3_%(date)s"
msgstr "We’re moving as fast as possible, and it looks like your order will now be ready on %(date)s."

msgid "production_delay_email_p4"
msgstr "We’ll send you another email with your delivery info as soon as we have it. Meanwhile, we so appreciate your patience – we can’t wait to get your Tylko home!"

msgid "production_delay_email_p5"
msgstr "All the Best,<br>The Tylko Team"

#, python-format
msgid "production_delay_email_subject_%(order_id)s"
msgstr "New production date for order %(order_id)s."

msgid "productpage_ivy_technical_material_plywood"
msgstr "Plywood is one of the strongest wooden products in the world, which is why it is so widely used in the construction industry. All our wood comes from FSC certified suppliers."

msgid "referral_push_1_mail_button"
msgstr "Tell your friends"

msgid "referral_push_1_mail_headline_1"
msgstr "Refer your friends, get&nbsp;a&nbsp;refund!"

#, python-format
msgid "referral_push_1_mail_paragraph_1_%(amount)s"
msgstr "Here's a smart idea: tell your friends about Tylko and you can earn <span style=\"color:#ff3c00;\">%(amount)s</span> back for every friend who makes an order (and keeps it)."

#, python-format
msgid "referral_push_1_mail_paragraph_2_%(value)s"
msgstr "The more people you refer, the more you get refunded - you can even earn the full price of your furniture back! Plus, we'll give them <span style=\"color:#ff3c00;\">%(value)s</span> off, too, so everyone wins!"

msgid "referral_push_1_mail_preheader"
msgstr "Earn your money back when you share the love."

msgid "referral_push_1_mail_subject_line"
msgstr "Refer your friends, get a refund!"

msgid "region_menu_country_United Kingdom"
msgstr "United Kingdom"

msgid "region_menu_country_other_regions"
msgstr "Other Regions"

msgid "region_menu_header_1"
msgstr "Where are you<br>shopping from?"

msgid "region_menu_header_2"
msgstr "Choose your language"

msgid "region_menu_language_header_1"
msgstr "Language"

msgid "region_menu_text_1"
msgstr "Choose your region to see prices in your currency and get up-to-date shipping times."

msgid "review_page_ico_hub_1_1"
msgstr "Works."

msgid "review_page_ico_hub_1_2"
msgstr "Dines."

msgid "review_page_ico_hub_1_3"
msgstr "Socializes."

msgid "review_page_ico_hub_1_4"
msgstr "Conducts business."

msgid "review_page_ico_hub_2_1"
msgstr "... in solitude."

msgid "review_page_ico_hub_2_2"
msgstr "... with a furry friend."

msgid "review_page_ico_hub_2_3"
msgstr "... with a sweetheart."

msgid "review_page_ico_hub_2_4"
msgstr "... with friends."

msgid "review_page_ico_ivy_1_1"
msgstr "Solved space issue."

msgid "review_page_ico_ivy_1_2"
msgstr "Stores items perfectly."

msgid "review_page_ico_ivy_1_3"
msgstr "Arranges collectables."

msgid "review_page_ico_ivy_1_4"
msgstr "App was new and exciting."

msgid "review_page_ico_ivy_2_1"
msgstr "Stores household items."

msgid "review_page_ico_ivy_2_2"
msgstr "Stores collections."

msgid "review_page_ico_ivy_2_3"
msgstr "Stores books and music."

msgid "review_page_ico_ivy_2_4"
msgstr "Is a pet's’ playground."

msgid "review_page_ico_totem_1_1"
msgstr "Enjoys Asian food."

msgid "review_page_ico_totem_1_2"
msgstr "Enjoys vegetarian food."

msgid "review_page_ico_totem_1_3"
msgstr "Enjoys local dishes."

msgid "review_page_ico_totem_1_4"
msgstr "Enjoys all kinds of food."

msgid "review_page_ico_totem_2_1"
msgstr "Grinds like a pro."

msgid "review_page_ico_totem_2_2"
msgstr "Surprised a friend."

msgid "review_page_ico_totem_2_3"
msgstr "Decorated restaurant."

msgid "review_page_ico_totem_2_4"
msgstr "Perfect for spices."

msgid "reviews_close"
msgstr "close"

msgid "reviews_more"
msgstr "more"

msgid "reviews_read_more"
msgstr "Read more reviews"

msgid "reviews_show_%(star_number)s_stars"
msgstr "%(star_number)s stars"

msgid "reviews_show_all"
msgstr "all"

msgid "right_corner_module"
msgstr "Right corner"

msgid "right_corner_module_cover"
msgstr "Cover for right corner"

msgid "row_configurator_test"
msgstr "test"

msgid "s4l_mail_1_button"
msgstr "Finish Up"

msgid "s4l_mail_1_heading"
msgstr "Your design is waiting..."

msgid "s4l_mail_1_paragraph_1"
msgstr "We love the <strong>design you created</strong> and are holding onto it for you. You can keep editing your piece - or wrap up your order right away."

#, python-format
msgid "s4l_mail_1_paragraph_2_%(shipping)s"
msgstr "If you checkout now, it'll be ready in %(shipping)s weeks, with&nbsp;free delivery and 100-day returns, too.<br><br><strong>Ready? Just click below!</strong>"

msgid "s4l_mail_1_preheader"
msgstr "Next step? Finish up your order!"

msgid "s4l_mail_1_topic"
msgstr "❤️ from Tylko: Your design looks great!"

msgid "s4l_mail_2_button"
msgstr "Finish My Order"

msgid "s4l_mail_2_footer"
msgstr "Still not sure? <br>Let these real-life designs inspire you."

msgid "s4l_mail_2_heading"
msgstr "Ready to wrap up?"

msgid "s4l_mail_2_paragraph"
msgstr "We're standing by to start prepping your design. All you have to do is finish up your order and enjoy <strong>free shipping and 100-day returns.</strong>"

msgid "s4l_mail_2_paragraph_2"
msgstr "Once your furniture arrives, we think you'll love our <strong>hassle-free, click-in assembly</strong>, too. No more fiddly hex keys or tiny bolts - just brilliant storage in a snap."

msgid "s4l_mail_2_preheader"
msgstr "Just say the word and we'll start prepping your order."

msgid "s4l_mail_2_topic"
msgstr "❤️ from Tylko: Don't forget your design."

msgid "s4l_mail_3a_body"
msgstr "People are raving about Tylko, so we thought we'd let their reviews speak for themselves:"

msgid "s4l_mail_3a_body_2"
msgstr "And don't forget - we saved your <strong>Wishlist</strong>. <br>Just"

msgid "s4l_mail_3a_body_3"
msgstr " to continue your order."

msgid "s4l_mail_3a_button"
msgstr "Read More Reviews"

msgid "s4l_mail_3a_heading_1"
msgstr "Have you heard?"

msgid "s4l_mail_3a_hyperlink"
msgstr "click here"

msgid "s4l_mail_3a_preheader"
msgstr "We love happy customers. Here's what they're saying..."

msgid "s4l_mail_3a_review"
msgstr "Perfect. Cannot recommend more highly. Quality great, easy to assemble, looks amazing and swallowed up my many, many books! Friends and relations are now suffering shelf envy!"

msgid "s4l_mail_3a_review_signature"
msgstr "Alison, United Kingdom"

msgid "s4l_mail_3a_topic"
msgstr "❤️ from Tylko: Our customers are the happiest."

msgid "s4l_mail_3b_body_1"
msgstr "We couldn't help but notice your great-looking <strong>Wishlist</strong>."

#, python-format
msgid "s4l_mail_3b_body_2_%(value)s"
msgstr "How does <strong style=\"color:#ff3c00\">%(value)s </strong> off sound to help shift that design into your shopping cart? Order in the next three days with the <strong>voucher code </strong>"

#, python-format
msgid "s4l_mail_3b_body_4_%(minValue)s"
msgstr "It's valid for 3 days on any minimum order of %(minValue)s. Why not put it towards your custom design?<br><br>Thanks Again,<br>Team Tylko"

msgid "s4l_mail_3b_body_4_without_min"
msgstr "It's valid for 3 days. <br>Why not put it towards your custom design?<br><br>Thanks Again,<br>Team Tylko"

msgid "s4l_mail_3b_button"
msgstr "Finish Up"

msgid "s4l_mail_3b_headline"
msgstr "Shop now and save"

msgid "s4l_mail_3b_preheader"
msgstr "We love what you designed. Let us help you bring it home."

#, python-format
msgid "s4l_mail_3b_topic_%(value)s"
msgstr "❤️ from Tylko: Your Wishlist is waiting (and so is %(value)s off!)"

#, python-format
msgid "s4l_mail_4b_body_%(value)s"
msgstr "Ok, you gotta tell us: <strong>what's holding you back</strong> from ordering the piece you designed? <br><br>Let us know and we'll take <strong style=\"color: #ff3c00;\">%(value)s off</strong> your order."

msgid "s4l_mail_4b_body_1"
msgstr "<strong>Great news</strong>, too: we've saved your "

msgid "s4l_mail_4b_body_2"
msgstr "We'll send <strong>your voucher</strong> as soon as you click a response - thanks for letting us know!"

#, python-format
msgid "s4l_mail_4b_body_3_%(value)s"
msgstr "for 3 more days. Get <strong style=\"color: #FF3C00\"> up to %(value)s off</strong>."

#, python-format
msgid "s4l_mail_4b_body_3_%(value)s_%(minValue)s"
msgstr "for 3 more days. Get <strong style=\"color: #FF3C00\"> up to %(value)s off</strong> with a minimum order value of %(minValue)s."

msgid "s4l_mail_4b_body_3a"
msgstr " and extended your special voucher code "

msgid "s4l_mail_4b_headline"
msgstr "Care to share?"

msgid "s4l_mail_4b_option_1"
msgstr "&#9675; I’m just browsing at the moment"

msgid "s4l_mail_4b_option_2"
msgstr "&#9675; I’m comparing other offers on the market"

msgid "s4l_mail_4b_option_3"
msgstr "&#9675; I can’t decide on my final design "

msgid "s4l_mail_4b_option_4"
msgstr "&#9675; I’m waiting for feedback on my design from someone "

msgid "s4l_mail_4b_option_5"
msgstr "&#9675; My place isn’t ready to be furnished at the moment "

msgid "s4l_mail_4b_option_6"
msgstr "&#9675; I’m waiting for a sale or promotion"

msgid "s4l_mail_4b_option_7"
msgstr "&#9675; Other"

msgid "s4l_mail_4b_preheader"
msgstr "We want you to bring your design home. Here's a gift to help it happen."

msgid "s4l_mail_4b_question"
msgstr "Please choose one:"

msgid "s4l_mail_4b_signature"
msgstr "Take Care,<br>Team Tylko"

#, python-format
msgid "s4l_mail_4b_topic_%(value)s"
msgstr "❤️ from Tylko: Click here for your %(value)s gift."

#, python-format
msgid "s4l_mail_5a_body_%(value)s"
msgstr "We really appreciate your feedback. As promised, here's your <strong>unique voucher code for <span style=\"color:#ff3c00\">%(value)s off</span></strong>:"

msgid "s4l_mail_5a_body_2"
msgstr "It's valid for 30 days. <br>Why not put it towards your custom design?"

#, python-format
msgid "s4l_mail_5a_body_2_%(min_value)s"
msgstr "It's valid for 30 days on any minimum order of %(min_value)s. Why not put it towards your custom design?"

msgid "s4l_mail_5a_button"
msgstr "Finish Up"

msgid "s4l_mail_5a_headline"
msgstr "You're the best!"

msgid "s4l_mail_5a_preheader"
msgstr "Thanks for your feedback. Open up for your gift."

msgid "s4l_mail_5a_signature"
msgstr "Thanks Again,<br>Team Tylko"

#, python-format
msgid "s4l_mail_5a_topic_%(value)s"
msgstr "Here's your %(value)s Tylko voucher."

msgid "s4l_mail_wishlist_hyperlink"
msgstr "wishlist"

msgid "s4l_yes_checkbox"
msgstr "Yes, subscribe me"

msgid "sample_button_Type01_02_sets"
msgstr "Tylko Original Sets"

msgid "sample_button_Type03_sets"
msgstr "The Tone Wardobe sets"

msgid "sample_facebook_description"
msgstr "Order a sample kit to feel the premium quality first-hand."

msgid "sample_hero_alt"
msgstr "Tylko Material Samples - Plywood, Natural Wood Veneer, Particle Board with a Laminated Top Finish."

msgid "sample_meta_description"
msgstr "Get a feel of the quality of our materials, like premium plywood and veneer, and get a proper look at our wide range of colours."

msgid "sample_meta_title"
msgstr "Tylko Material Samples - Shelves, Wardrobes, Tylko Original Classic, Original Modern, Tone"

msgid "sample_set_20000_name"
msgstr "White Plywood"

msgid "sample_set_20001_name"
msgstr "Black Plywood"

msgid "sample_set_20003_name"
msgstr "Grey Plywood"

msgid "sample_set_20006_name"
msgstr "Classic Red Plywood"

msgid "sample_set_20007_name"
msgstr "Yellow Plywood"

msgid "sample_set_20008_name"
msgstr "Dusty Pink Plywood"

msgid "sample_set_20009_name"
msgstr "Blue Plywood"

msgid "sample_set_20010_name"
msgstr "Dark Brown Plywood"

msgid "sample_set_20011_name"
msgstr "Mosgrøn"

msgid "sample_set_20100_name"
msgstr "White"

msgid "sample_set_20101_name"
msgstr "Terracotta"

msgid "sample_set_20102_name"
msgstr "Midnight Blue"

msgid "sample_set_20103_name"
msgstr "Sand + Midnight Blue"

msgid "sample_set_20106_name"
msgstr "Premium Matte Black"

msgid "sample_set_20107_name"
msgstr "Sky Blue"

msgid "sample_set_20108_name"
msgstr "Burgundy"

msgid "sample_set_20109_name"
msgstr "Cotton beige"

msgid "sample_set_20110_name"
msgstr "Grey"

msgid "sample_set_20111_name"
msgstr "Grey + Dark grey"

msgid "sample_set_20112_name"
msgstr "Mustard Yellow"

msgid "sample_set_20115_name"
msgstr "Reisinger Pink"

msgid "sample_set_20116_name"
msgstr "Sage Green"

msgid "sample_set_20117_name"
msgstr "Stone Gray"

msgid "sample_set_20200_name"
msgstr "White Oak Veneer"

msgid "sample_set_20201_name"
msgstr "Oak Veneer"

msgid "sample_set_20202_name"
msgstr "Walnut Veneer"

msgid "sample_set_20300_name"
msgstr "White"

msgid "sample_set_20301_name"
msgstr "Cashmere Beige"

msgid "sample_set_20302_name"
msgstr "Graphite Grey"

msgid "sample_set_20303_name"
msgstr "White"

msgid "sample_set_20304_name"
msgstr "Cashmere Beige"

msgid "sample_set_20305_name"
msgstr "Graphite Grey"

msgid "sample_set_20306_name"
msgstr "Powder Pink"

msgid "sample_set_20307_name"
msgstr "Stone Grey"

msgid "sample_set_20308_name"
msgstr "Sage Green"

msgid "sample_set_20309_name"
msgstr "Misty Blue"

msgid "sample_set_20400_name"
msgstr "White"

msgid "sample_set_20403_name"
msgstr "Grey"

msgid "sample_set_20405_name"
msgstr "White Plywood"

msgid "sample_set_20406_name"
msgstr "Grey Plywood"

msgid "sample_set_20408_name"
msgstr "Clay Brown"

msgid "sample_set_20409_name"
msgstr "Olive Green"

msgid "sample_set_20410_name"
msgstr "Sand"

msgid "sample_set_20411_name"
msgstr "Black"

msgid "sample_set_20500_name"
msgstr "Light wood effect"

msgid "sample_set_20501_name"
msgstr "Dark wood effect"

msgid "sample_set_20600_name"
msgstr "Off-White"

msgid "sample_set_20601_name"
msgstr "Cashmere Beige"

msgid "sample_set_20602_name"
msgstr "Pistachio Green"

msgid "sample_set_20603_name"
msgstr "Inky Black"

msgid "sample_set_20604_name"
msgstr "Powder Pink"

msgid "sample_set_21000_name"
msgstr "Dark Brown"

msgid "sample_set_21001_name"
msgstr "Khaki"

msgid "sample_set_21002_name"
msgstr "Grey"

msgid "sample_set_21003_name"
msgstr "Pale Yellow"

msgid "sample_set_21004_name"
msgstr "Powder Pink"

msgid "sample_set_21005_name"
msgstr "Green"

msgid "sample_set_21006_name"
msgstr "Lilac Blue"

msgid "sample_set_21007_name"
msgstr "Off-White"

msgid "sample_set_21008_name"
msgstr "Beige"

msgid "sample_set_21009_name"
msgstr "Brown"

msgid "sample_set_21010_name"
msgstr "Light Grey"

msgid "sample_set_21011_name"
msgstr "Mustard"

msgid "sample_set_21012_name"
msgstr "Blush Pink"

msgid "sample_set_21013_name"
msgstr "Olive Green"

msgid "sample_set_21014_name"
msgstr "Cobalt Blue"

msgid "saveditem_mail_6a_samples_button"
msgstr "Order a sample set"

msgid "saveditem_mail_6a_samples_headline_1"
msgstr "Make your <br>decision easier!"

msgid "saveditem_mail_6a_samples_paragraph_1"
msgstr "Your design"

msgid "saveditem_mail_6a_samples_paragraph_1_2"
msgstr "is still waiting for you."

msgid "saveditem_mail_6a_samples_paragraph_2_1"
msgstr "Not sure which colour or finish to take? Then"

msgid "saveditem_mail_6a_samples_paragraph_2_2"
msgstr "order a sample set"

msgid "saveditem_mail_6a_samples_paragraph_2_3"
msgstr "to have a closer look before you buy!"

msgid "saveditem_mail_6a_samples_preheader"
msgstr "Take a closer look at our great colours and finishes before you decide to buy your shelf."

msgid "scart_eco_tax"
msgstr "eco-fee"

msgid "scart_eco_tax_desc"
msgstr "Includes taxes and"

msgid "seater_module"
msgstr "1-seater"

msgid "seater_module_cover"
msgstr "Cover for 1-seater"

#, python-format
msgid "send_confirmation_tracking_number_complaints_body_%(user)s_%(tracking_number)s_%(carrier)s"
msgstr "Hello %(user)s,<br><br>Your Tylko order replacement has been shipped with %(carrier)s. You can follow its progress every step of the way with the tracking number %(tracking_number)s. If necessary, the courier might contact you directly to arrange delivery, so keep an eye out."

msgid "send_confirmation_tracking_number_complaints_body_2"
msgstr "If you have any questions in the meantime, give us a call us on +44 113 868 0195, +49 800 0010484 or simply reply to this mail.<br><br>Speak Soon,<br>Team Tylko"

msgid "send_confirmation_tracking_number_complaints_headline"
msgstr "Your replacement is on the way!"

msgid "send_confirmation_tracking_number_complaints_preheader"
msgstr "Coming to a front door near you..."

msgid "send_confirmation_tracking_number_complaints_subject"
msgstr "Your Tylko replacement has been shipped!"

msgid "seo_and"
msgstr "and"

msgid "seo_backpanels"
msgstr "backpanels"

msgid "seo_cable_openings"
msgstr "cable management"

msgid "seo_chaise_longue"
msgstr "chaise longue"

msgid "seo_deep"
msgstr "deep"

msgid "seo_deep_feminine"
msgstr "seo_deep_feminine"

msgid "seo_deep_masculine"
msgstr "seo_deep_masculine"

msgid "seo_door"
msgstr "door"

msgid "seo_doors"
msgstr "doors"

msgid "seo_drawers"
msgstr "drawers"

msgid "seo_external_drawers"
msgstr "external drawers"

msgid "seo_footrest"
msgstr "ottoman"

msgid "seo_in"
msgstr "in"

msgid "seo_internal_and_external_drawers"
msgstr "internal and external drawers"

msgid "seo_internal_drawers"
msgstr "internal drawers"

msgid "seo_ivy_ash_feminine"
msgstr "seo_ivy_ash_feminine"

msgid "seo_ivy_ash_masculine"
msgstr "seo_ivy_ash_masculine"

msgid "seo_ivy_ash_neuter"
msgstr "seo_ivy_ash_neuter"

msgid "seo_ivy_black_feminine"
msgstr "seo_ivy_black_feminine"

msgid "seo_ivy_black_masculine"
msgstr "seo_ivy_black_masculine"

msgid "seo_ivy_black_neuter"
msgstr "seo_ivy_black_neuter"

msgid "seo_ivy_blue_feminine"
msgstr "seo_ivy_blue_feminine"

msgid "seo_ivy_blue_masculine"
msgstr "seo_ivy_blue_masculine"

msgid "seo_ivy_blue_neuter"
msgstr "seo_ivy_blue_neuter"

msgid "seo_ivy_dark_brown_feminine"
msgstr "seo_ivy_dark_brown_feminine"

msgid "seo_ivy_dark_brown_masculine"
msgstr "seo_ivy_dark_brown_masculine"

msgid "seo_ivy_dark_brown_neuter"
msgstr "seo_ivy_dark_brown_neuter"

msgid "seo_ivy_grey_feminine"
msgstr "seo_ivy_grey_feminine"

msgid "seo_ivy_grey_masculine"
msgstr "seo_ivy_grey_masculine"

msgid "seo_ivy_grey_neuter"
msgstr "seo_ivy_grey_neuter"

msgid "seo_ivy_mossgreen_feminine"
msgstr "Moss Green"

msgid "seo_ivy_mossgreen_masculine"
msgstr "Moss Green"

msgid "seo_ivy_mossgreen_neuter"
msgstr "Moss Green"

msgid "seo_ivy_pink_feminine"
msgstr "seo_ivy_pink_feminine"

msgid "seo_ivy_pink_masculine"
msgstr "seo_ivy_pink_masculine"

msgid "seo_ivy_pink_neuter"
msgstr "seo_ivy_pink_neuter"

msgid "seo_ivy_red_feminine"
msgstr "seo_ivy_red_feminine"

msgid "seo_ivy_red_masculine"
msgstr "seo_ivy_red_masculine"

msgid "seo_ivy_red_neuter"
msgstr "seo_ivy_red_neuter"

msgid "seo_ivy_white_feminine"
msgstr "seo_ivy_white_feminine"

msgid "seo_ivy_white_masculine"
msgstr "seo_ivy_white_masculine"

msgid "seo_ivy_white_neuter"
msgstr "seo_ivy_white_neuter"

msgid "seo_ivy_yellow_feminine"
msgstr "seo_ivy_yellow_feminine"

msgid "seo_ivy_yellow_masculine"
msgstr "seo_ivy_yellow_masculine"

msgid "seo_ivy_yellow_neuter"
msgstr "seo_ivy_yellow_neuter"

msgid "seo_large"
msgstr "large"

msgid "seo_large_feminine"
msgstr "seo_large_feminine"

msgid "seo_large_masculine"
msgstr "seo_large_masculine"

msgid "seo_long_legs"
msgstr "legs"

msgid "seo_low"
msgstr "low"

msgid "seo_low_feminine"
msgstr "seo_low_feminine"

msgid "seo_low_masculine"
msgstr "seo_low_masculine"

msgid "seo_plinth_legs"
msgstr "plinth"

msgid "seo_plp_color_simple_s01_corduroy_beige"
msgstr "Beige"

msgid "seo_plp_color_simple_s01_corduroy_beige_feminine"
msgstr "Beige"

msgid "seo_plp_color_simple_s01_corduroy_beige_masculine"
msgstr "Beige"

msgid "seo_plp_color_simple_s01_corduroy_beige_neuter"
msgstr "Beige"

msgid "seo_plp_color_simple_s01_corduroy_blush_pink"
msgstr "Blush Pink"

msgid "seo_plp_color_simple_s01_corduroy_blush_pink_feminine"
msgstr "Blush Pink"

msgid "seo_plp_color_simple_s01_corduroy_blush_pink_masculine"
msgstr "Blush Pink"

msgid "seo_plp_color_simple_s01_corduroy_blush_pink_neuter"
msgstr "Blush Pink"

msgid "seo_plp_color_simple_s01_corduroy_brown"
msgstr "Brown"

msgid "seo_plp_color_simple_s01_corduroy_brown_feminine"
msgstr "Brown"

msgid "seo_plp_color_simple_s01_corduroy_brown_masculine"
msgstr "Brown"

msgid "seo_plp_color_simple_s01_corduroy_brown_neuter"
msgstr "Brown"

msgid "seo_plp_color_simple_s01_corduroy_cobalt_blue"
msgstr "Cobalt Blue"

msgid "seo_plp_color_simple_s01_corduroy_cobalt_blue_feminine"
msgstr "Cobalt Blue"

msgid "seo_plp_color_simple_s01_corduroy_cobalt_blue_masculine"
msgstr "Cobalt Blue"

msgid "seo_plp_color_simple_s01_corduroy_cobalt_blue_neuter"
msgstr "Cobalt Blue"

msgid "seo_plp_color_simple_s01_corduroy_light_grey"
msgstr "Light Grey"

msgid "seo_plp_color_simple_s01_corduroy_light_grey_feminine"
msgstr "Light Grey"

msgid "seo_plp_color_simple_s01_corduroy_light_grey_masculine"
msgstr "Light Grey"

msgid "seo_plp_color_simple_s01_corduroy_light_grey_neuter"
msgstr "Light Grey"

msgid "seo_plp_color_simple_s01_corduroy_mustard"
msgstr "Mustard"

msgid "seo_plp_color_simple_s01_corduroy_mustard_feminine"
msgstr "Mustard"

msgid "seo_plp_color_simple_s01_corduroy_mustard_masculine"
msgstr "Mustard"

msgid "seo_plp_color_simple_s01_corduroy_mustard_neuter"
msgstr "Mustard"

msgid "seo_plp_color_simple_s01_corduroy_off_white"
msgstr "Off-White"

msgid "seo_plp_color_simple_s01_corduroy_off_white_feminine"
msgstr "Off-White"

msgid "seo_plp_color_simple_s01_corduroy_off_white_masculine"
msgstr "Off-White"

msgid "seo_plp_color_simple_s01_corduroy_off_white_neuter"
msgstr "Off-White"

msgid "seo_plp_color_simple_s01_corduroy_olive_green"
msgstr "Olive Green"

msgid "seo_plp_color_simple_s01_corduroy_olive_green_feminine"
msgstr "Olive Green"

msgid "seo_plp_color_simple_s01_corduroy_olive_green_masculine"
msgstr "Olive Green"

msgid "seo_plp_color_simple_s01_corduroy_olive_green_neuter"
msgstr "Olive Green"

msgid "seo_plp_color_simple_s01_multicolor"
msgstr "Multicolor"

msgid "seo_plp_color_simple_s01_multicolor_feminine"
msgstr "Multicolor"

msgid "seo_plp_color_simple_s01_multicolor_masculine"
msgstr "Multicolor"

msgid "seo_plp_color_simple_s01_multicolor_neuter"
msgstr "Multicolor"

msgid "seo_plp_color_simple_s01_rewool2_dark_brown"
msgstr "Dark brown"

msgid "seo_plp_color_simple_s01_rewool2_dark_brown_feminine"
msgstr "Dark Brown"

msgid "seo_plp_color_simple_s01_rewool2_dark_brown_masculine"
msgstr "Dark Brown"

msgid "seo_plp_color_simple_s01_rewool2_dark_brown_neuter"
msgstr "Dark Brown"

msgid "seo_plp_color_simple_s01_rewool2_green"
msgstr "Green"

msgid "seo_plp_color_simple_s01_rewool2_green_feminine"
msgstr "Green"

msgid "seo_plp_color_simple_s01_rewool2_green_masculine"
msgstr "Green"

msgid "seo_plp_color_simple_s01_rewool2_green_neuter"
msgstr "Green"

msgid "seo_plp_color_simple_s01_rewool2_grey"
msgstr "Grey"

msgid "seo_plp_color_simple_s01_rewool2_grey_feminine"
msgstr "Grey"

msgid "seo_plp_color_simple_s01_rewool2_grey_masculine"
msgstr "Grey"

msgid "seo_plp_color_simple_s01_rewool2_grey_neuter"
msgstr "Grey"

msgid "seo_plp_color_simple_s01_rewool2_khaki"
msgstr "Khaki"

msgid "seo_plp_color_simple_s01_rewool2_khaki_feminine"
msgstr "Khaki"

msgid "seo_plp_color_simple_s01_rewool2_khaki_masculine"
msgstr "Khaki"

msgid "seo_plp_color_simple_s01_rewool2_khaki_neuter"
msgstr "Khaki"

msgid "seo_plp_color_simple_s01_rewool2_lilac_blue"
msgstr "Lilac Blue"

msgid "seo_plp_color_simple_s01_rewool2_lilac_blue_feminine"
msgstr "Lilac Blue"

msgid "seo_plp_color_simple_s01_rewool2_lilac_blue_masculine"
msgstr "Lilac Blue"

msgid "seo_plp_color_simple_s01_rewool2_lilac_blue_neuter"
msgstr "Lilac Blue"

msgid "seo_plp_color_simple_s01_rewool2_pale_yellow"
msgstr "Pale Yellow"

msgid "seo_plp_color_simple_s01_rewool2_pale_yellow_feminine"
msgstr "Pale Yellow"

msgid "seo_plp_color_simple_s01_rewool2_pale_yellow_masculine"
msgstr "Pale Yellow"

msgid "seo_plp_color_simple_s01_rewool2_pale_yellow_neuter"
msgstr "Pale Yellow"

msgid "seo_plp_color_simple_s01_rewool2_powder_pink"
msgstr "Powder Pink"

msgid "seo_plp_color_simple_s01_rewool2_powder_pink_feminine"
msgstr "Powder Pink"

msgid "seo_plp_color_simple_s01_rewool2_powder_pink_masculine"
msgstr "Powder Pink"

msgid "seo_plp_color_simple_s01_rewool2_powder_pink_neuter"
msgstr "Powder Pink"

msgid "seo_plp_color_simple_t01_ash"
msgstr "White Oak"

msgid "seo_plp_color_simple_t01_black"
msgstr "Black"

msgid "seo_plp_color_simple_t01_black_feminine"
msgstr "seo_plp_color_simple_t01_black_feminine"

msgid "seo_plp_color_simple_t01_black_masculine"
msgstr "seo_plp_color_simple_t01_black_masculine"

msgid "seo_plp_color_simple_t01_black_neuter"
msgstr "seo_plp_color_simple_t01_black_neuter"

msgid "seo_plp_color_simple_t01_blue"
msgstr "Blue"

msgid "seo_plp_color_simple_t01_blue_feminine"
msgstr "seo_plp_color_simple_t01_blue_feminine"

msgid "seo_plp_color_simple_t01_blue_masculine"
msgstr "seo_plp_color_simple_t01_blue_masculine"

msgid "seo_plp_color_simple_t01_blue_neuter"
msgstr "seo_plp_color_simple_t01_blue_neuter"

msgid "seo_plp_color_simple_t01_brown"
msgstr "Brown"

msgid "seo_plp_color_simple_t01_brown_feminine"
msgstr "Brown"

msgid "seo_plp_color_simple_t01_brown_masculine"
msgstr "Brown"

msgid "seo_plp_color_simple_t01_brown_neuter"
msgstr "Brown"

msgid "seo_plp_color_simple_t01_classic_red"
msgstr "Red"

msgid "seo_plp_color_simple_t01_classic_red_feminine"
msgstr "seo_plp_color_simple_t01_classic_red_feminine"

msgid "seo_plp_color_simple_t01_classic_red_masculine"
msgstr "seo_plp_color_simple_t01_classic_red_masculine"

msgid "seo_plp_color_simple_t01_classic_red_neuter"
msgstr "seo_plp_color_simple_t01_classic_red_neuter"

msgid "seo_plp_color_simple_t01_dusty_pink"
msgstr "Pink"

msgid "seo_plp_color_simple_t01_dusty_pink_feminine"
msgstr "seo_plp_color_simple_t01_dusty_pink_feminine"

msgid "seo_plp_color_simple_t01_dusty_pink_masculine"
msgstr "seo_plp_color_simple_t01_dusty_pink_masculine"

msgid "seo_plp_color_simple_t01_dusty_pink_neuter"
msgstr "seo_plp_color_simple_t01_dusty_pink_neuter"

msgid "seo_plp_color_simple_t01_grey"
msgstr "Grey"

msgid "seo_plp_color_simple_t01_grey_feminine"
msgstr "seo_plp_color_simple_t01_grey_feminine"

msgid "seo_plp_color_simple_t01_grey_masculine"
msgstr "seo_plp_color_simple_t01_grey_masculine"

msgid "seo_plp_color_simple_t01_grey_neuter"
msgstr "seo_plp_color_simple_t01_grey_neuter"

msgid "seo_plp_color_simple_t01_mossgreen"
msgstr "Moss Green"

msgid "seo_plp_color_simple_t01_mossgreen_feminine"
msgstr "green"

msgid "seo_plp_color_simple_t01_mossgreen_masculine"
msgstr "seo_plp_color_simple_t01_mossgreen_masculine"

msgid "seo_plp_color_simple_t01_mossgreen_neuter"
msgstr "green"

msgid "seo_plp_color_simple_t01_oak"
msgstr "Oak"

msgid "seo_plp_color_simple_t01_white"
msgstr "White"

msgid "seo_plp_color_simple_t01_white_feminine"
msgstr "seo_plp_color_simple_t01_white_feminine"

msgid "seo_plp_color_simple_t01_white_masculine"
msgstr "seo_plp_color_simple_t01_white_masculine"

msgid "seo_plp_color_simple_t01_white_neuter"
msgstr "seo_plp_color_simple_t01_white_neuter"

msgid "seo_plp_color_simple_t01_yellow"
msgstr "Yellow"

msgid "seo_plp_color_simple_t01_yellow_feminine"
msgstr "seo_plp_color_simple_t01_yellow_feminine"

msgid "seo_plp_color_simple_t01_yellow_masculine"
msgstr "seo_plp_color_simple_t01_yellow_masculine"

msgid "seo_plp_color_simple_t01_yellow_neuter"
msgstr "seo_plp_color_simple_t01_yellow_neuter"

msgid "seo_plp_color_simple_t02_black"
msgstr "Black"

msgid "seo_plp_color_simple_t02_black_feminine"
msgstr "Black"

msgid "seo_plp_color_simple_t02_black_masculine"
msgstr "Black"

msgid "seo_plp_color_simple_t02_black_neuter"
msgstr "Black"

msgid "seo_plp_color_simple_t02_burgundy_red"
msgstr "Burgund"

msgid "seo_plp_color_simple_t02_burgundy_red_feminine"
msgstr "seo_plp_color_simple_t02_burgundy_red_feminine"

msgid "seo_plp_color_simple_t02_burgundy_red_masculine"
msgstr "seo_plp_color_simple_t02_burgundy_red_masculine"

msgid "seo_plp_color_simple_t02_burgundy_red_neuter"
msgstr "seo_plp_color_simple_t02_burgundy_red_neuter"

msgid "seo_plp_color_simple_t02_cotton_beige"
msgstr "Beige"

msgid "seo_plp_color_simple_t02_cotton_beige_feminine"
msgstr "seo_plp_color_simple_t02_cotton_beige_feminine"

msgid "seo_plp_color_simple_t02_cotton_beige_masculine"
msgstr "seo_plp_color_simple_t02_cotton_beige_masculine"

msgid "seo_plp_color_simple_t02_cotton_beige_neuter"
msgstr "seo_plp_color_simple_t02_cotton_beige_neuter"

msgid "seo_plp_color_simple_t02_green"
msgstr "Green"

msgid "seo_plp_color_simple_t02_green_feminine"
msgstr "seo_plp_color_simple_t02_green_feminine"

msgid "seo_plp_color_simple_t02_green_masculine"
msgstr "seo_plp_color_simple_t02_green_masculine"

msgid "seo_plp_color_simple_t02_green_neuter"
msgstr "seo_plp_color_simple_t02_green_neuter"

msgid "seo_plp_color_simple_t02_grey"
msgstr "Grey"

msgid "seo_plp_color_simple_t02_grey_dark_grey"
msgstr "Grey"

msgid "seo_plp_color_simple_t02_grey_dark_grey_feminine"
msgstr "seo_plp_color_simple_t02_grey_dark_grey_feminine"

msgid "seo_plp_color_simple_t02_grey_dark_grey_masculine"
msgstr "seo_plp_color_simple_t02_grey_dark_grey_masculine"

msgid "seo_plp_color_simple_t02_grey_dark_grey_neuter"
msgstr "seo_plp_color_simple_t02_grey_dark_grey_neuter"

msgid "seo_plp_color_simple_t02_grey_feminine"
msgstr "seo_plp_color_simple_t02_grey_feminine"

msgid "seo_plp_color_simple_t02_grey_masculine"
msgstr "seo_plp_color_simple_t02_grey_masculine"

msgid "seo_plp_color_simple_t02_grey_neuter"
msgstr "seo_plp_color_simple_t02_grey_neuter"

msgid "seo_plp_color_simple_t02_matte_black"
msgstr "Premium Black"

msgid "seo_plp_color_simple_t02_matte_black_feminine"
msgstr "seo_plp_color_simple_t02_matte_black_feminine"

msgid "seo_plp_color_simple_t02_matte_black_masculine"
msgstr "seo_plp_color_simple_t02_matte_black_masculine"

msgid "seo_plp_color_simple_t02_matte_black_neuter"
msgstr "seo_plp_color_simple_t02_matte_black_neuter"

msgid "seo_plp_color_simple_t02_midnight_blue"
msgstr "Blue"

msgid "seo_plp_color_simple_t02_midnight_blue_feminine"
msgstr "seo_plp_color_simple_t02_midnight_blue_feminine"

msgid "seo_plp_color_simple_t02_midnight_blue_masculine"
msgstr "seo_plp_color_simple_t02_midnight_blue_masculine"

msgid "seo_plp_color_simple_t02_midnight_blue_neuter"
msgstr "seo_plp_color_simple_t02_midnight_blue_neuter"

msgid "seo_plp_color_simple_t02_reisinger_pink"
msgstr "Reisinger Pink"

msgid "seo_plp_color_simple_t02_reisinger_pink_feminine"
msgstr "seo_plp_color_simple_t02_reisinger_pink_feminine"

msgid "seo_plp_color_simple_t02_reisinger_pink_masculine"
msgstr "seo_plp_color_simple_t02_reisinger_pink_masculine"

msgid "seo_plp_color_simple_t02_reisinger_pink_neuter"
msgstr "seo_plp_color_simple_t02_reisinger_pink_neuter"

msgid "seo_plp_color_simple_t02_sand_midnight_blue"
msgstr "Sand and Blue"

msgid "seo_plp_color_simple_t02_sand_midnight_blue_feminine"
msgstr "seo_plp_color_simple_t02_sand_midnight_blue_feminine"

msgid "seo_plp_color_simple_t02_sand_midnight_blue_masculine"
msgstr "seo_plp_color_simple_t02_sand_midnight_blue_masculine"

msgid "seo_plp_color_simple_t02_sand_midnight_blue_neuter"
msgstr "seo_plp_color_simple_t02_sand_midnight_blue_neuter"

msgid "seo_plp_color_simple_t02_sand_mustard_yellow"
msgstr "Sand and Yellow"

msgid "seo_plp_color_simple_t02_sand_mustard_yellow_feminine"
msgstr "seo_plp_color_simple_t02_sand_mustard_yellow_feminine"

msgid "seo_plp_color_simple_t02_sand_mustard_yellow_masculine"
msgstr "seo_plp_color_simple_t02_sand_mustard_yellow_masculine"

msgid "seo_plp_color_simple_t02_sand_mustard_yellow_neuter"
msgstr "seo_plp_color_simple_t02_sand_mustard_yellow_neuter"

msgid "seo_plp_color_simple_t02_sky_blue"
msgstr "Blue"

msgid "seo_plp_color_simple_t02_sky_blue_feminine"
msgstr "seo_plp_color_simple_t02_sky_blue_feminine"

msgid "seo_plp_color_simple_t02_sky_blue_masculine"
msgstr "seo_plp_color_simple_t02_sky_blue_masculine"

msgid "seo_plp_color_simple_t02_sky_blue_neuter"
msgstr "seo_plp_color_simple_t02_sky_blue_neuter"

msgid "seo_plp_color_simple_t02_stone_grey"
msgstr "Stone Grey"

msgid "seo_plp_color_simple_t02_stone_grey_feminine"
msgstr "seo_plp_color_simple_t02_stone_grey_feminine"

msgid "seo_plp_color_simple_t02_stone_grey_masculine"
msgstr "seo_plp_color_simple_t02_stone_grey_masculine"

msgid "seo_plp_color_simple_t02_stone_grey_neuter"
msgstr "seo_plp_color_simple_t02_stone_grey_neuter"

msgid "seo_plp_color_simple_t02_stone_grey_walnut"
msgstr "Stone Grey and Walnut"

msgid "seo_plp_color_simple_t02_stone_grey_walnut_feminine"
msgstr "seo_plp_color_simple_t02_stone_grey_walnut_feminine"

msgid "seo_plp_color_simple_t02_stone_grey_walnut_masculine"
msgstr "seo_plp_color_simple_t02_stone_grey_walnut_masculine"

msgid "seo_plp_color_simple_t02_stone_grey_walnut_neuter"
msgstr "seo_plp_color_simple_t02_stone_grey_walnut_neuter"

msgid "seo_plp_color_simple_t02_terracota"
msgstr "Terracota"

msgid "seo_plp_color_simple_t02_terracota_feminine"
msgstr "seo_plp_color_simple_t02_terracota_feminine"

msgid "seo_plp_color_simple_t02_terracota_masculine"
msgstr "seo_plp_color_simple_t02_terracota_masculine"

msgid "seo_plp_color_simple_t02_terracota_neuter"
msgstr "seo_plp_color_simple_t02_terracota_neuter"

msgid "seo_plp_color_simple_t02_white"
msgstr "White"

msgid "seo_plp_color_simple_t02_white_feminine"
msgstr "seo_plp_color_simple_t02_white_feminine"

msgid "seo_plp_color_simple_t02_white_masculine"
msgstr "seo_plp_color_simple_t02_white_masculine"

msgid "seo_plp_color_simple_t02_white_neuter"
msgstr "seo_plp_color_simple_t02_white_neuter"

msgid "seo_plp_color_simple_t03_cashmere_antique_pink"
msgstr "Beige and Pink"

msgid "seo_plp_color_simple_t03_cashmere_antique_pink_feminine"
msgstr "seo_plp_color_simple_t03_cashmere_antique_pink_feminine"

msgid "seo_plp_color_simple_t03_cashmere_antique_pink_masculine"
msgstr "seo_plp_color_simple_t03_cashmere_antique_pink_masculine"

msgid "seo_plp_color_simple_t03_cashmere_antique_pink_neuter"
msgstr "seo_plp_color_simple_t03_cashmere_antique_pink_neuter"

msgid "seo_plp_color_simple_t03_cashmere_beige"
msgstr "Beige"

msgid "seo_plp_color_simple_t03_cashmere_beige_feminine"
msgstr "seo_plp_color_simple_t03_cashmere_beige_feminine"

msgid "seo_plp_color_simple_t03_cashmere_beige_masculine"
msgstr "seo_plp_color_simple_t03_cashmere_beige_masculine"

msgid "seo_plp_color_simple_t03_cashmere_beige_neuter"
msgstr "seo_plp_color_simple_t03_cashmere_beige_neuter"

msgid "seo_plp_color_simple_t03_cashmere_misty_blue"
msgstr "Cashmere and Blue"

msgid "seo_plp_color_simple_t03_cashmere_misty_blue_feminine"
msgstr "seo_plp_color_simple_t03_cashmere_misty_blue_feminine"

msgid "seo_plp_color_simple_t03_cashmere_misty_blue_masculine"
msgstr "seo_plp_color_simple_t03_cashmere_misty_blue_masculine"

msgid "seo_plp_color_simple_t03_cashmere_misty_blue_neuter"
msgstr "seo_plp_color_simple_t03_cashmere_misty_blue_neuter"

msgid "seo_plp_color_simple_t03_cashmere_sage_green"
msgstr "Cashmere and Green"

msgid "seo_plp_color_simple_t03_cashmere_sage_green_feminine"
msgstr "seo_plp_color_simple_t03_cashmere_sage_green_feminine"

msgid "seo_plp_color_simple_t03_cashmere_sage_green_masculine"
msgstr "seo_plp_color_simple_t03_cashmere_sage_green_masculine"

msgid "seo_plp_color_simple_t03_cashmere_sage_green_neuter"
msgstr "seo_plp_color_simple_t03_cashmere_sage_green_neuter"

msgid "seo_plp_color_simple_t03_cashmere_stone_gray"
msgstr "Cashmere and Gray"

msgid "seo_plp_color_simple_t03_cashmere_stone_gray_feminine"
msgstr "seo_plp_color_simple_t03_cashmere_stone_gray_feminine"

msgid "seo_plp_color_simple_t03_cashmere_stone_gray_masculine"
msgstr "seo_plp_color_simple_t03_cashmere_stone_gray_masculine"

msgid "seo_plp_color_simple_t03_cashmere_stone_gray_neuter"
msgstr "seo_plp_color_simple_t03_cashmere_stone_gray_neuter"

msgid "seo_plp_color_simple_t03_graphite_grey"
msgstr "Graphite"

msgid "seo_plp_color_simple_t03_graphite_grey_antique_pink"
msgstr "Graphite and Pink"

msgid "seo_plp_color_simple_t03_graphite_grey_antique_pink_feminine"
msgstr "seo_plp_color_simple_t03_graphite_grey_antique_pink_feminine"

msgid "seo_plp_color_simple_t03_graphite_grey_antique_pink_masculine"
msgstr "seo_plp_color_simple_t03_graphite_grey_antique_pink_masculine"

msgid "seo_plp_color_simple_t03_graphite_grey_antique_pink_neuter"
msgstr "seo_plp_color_simple_t03_graphite_grey_antique_pink_neuter"

msgid "seo_plp_color_simple_t03_graphite_grey_feminine"
msgstr "seo_plp_color_simple_t03_graphite_grey_feminine"

msgid "seo_plp_color_simple_t03_graphite_grey_masculine"
msgstr "seo_plp_color_simple_t03_graphite_grey_masculine"

msgid "seo_plp_color_simple_t03_graphite_grey_neuter"
msgstr "seo_plp_color_simple_t03_graphite_grey_neuter"

msgid "seo_plp_color_simple_t03_graphite_misty_blue"
msgstr "Gray and Blue"

msgid "seo_plp_color_simple_t03_graphite_misty_blue_feminine"
msgstr "seo_plp_color_simple_t03_graphite_misty_blue_feminine"

msgid "seo_plp_color_simple_t03_graphite_misty_blue_masculine"
msgstr "seo_plp_color_simple_t03_graphite_misty_blue_masculine"

msgid "seo_plp_color_simple_t03_graphite_misty_blue_neuter"
msgstr "seo_plp_color_simple_t03_graphite_misty_blue_neuter"

msgid "seo_plp_color_simple_t03_graphite_sage_green"
msgstr "Gray and Green"

msgid "seo_plp_color_simple_t03_graphite_sage_green_feminine"
msgstr "seo_plp_color_simple_t03_graphite_sage_green_feminine"

msgid "seo_plp_color_simple_t03_graphite_sage_green_masculine"
msgstr "seo_plp_color_simple_t03_graphite_sage_green_masculine"

msgid "seo_plp_color_simple_t03_graphite_sage_green_neuter"
msgstr "seo_plp_color_simple_t03_graphite_sage_green_neuter"

msgid "seo_plp_color_simple_t03_graphite_stone_gray"
msgstr "Graphite and Gray"

msgid "seo_plp_color_simple_t03_graphite_stone_gray_feminine"
msgstr "seo_plp_color_simple_t03_graphite_stone_gray_feminine"

msgid "seo_plp_color_simple_t03_graphite_stone_gray_masculine"
msgstr "seo_plp_color_simple_t03_graphite_stone_gray_masculine"

msgid "seo_plp_color_simple_t03_graphite_stone_gray_neuter"
msgstr "seo_plp_color_simple_t03_graphite_stone_gray_neuter"

msgid "seo_plp_color_simple_t03_white"
msgstr "White"

msgid "seo_plp_color_simple_t03_white_antique_pink"
msgstr "White and Pink"

msgid "seo_plp_color_simple_t03_white_antique_pink_feminine"
msgstr "seo_plp_color_simple_t03_white_antique_pink_feminine"

msgid "seo_plp_color_simple_t03_white_antique_pink_masculine"
msgstr "seo_plp_color_simple_t03_white_antique_pink_masculine"

msgid "seo_plp_color_simple_t03_white_antique_pink_neuter"
msgstr "seo_plp_color_simple_t03_white_antique_pink_neuter"

msgid "seo_plp_color_simple_t03_white_feminine"
msgstr "seo_plp_color_simple_t03_white_feminine"

msgid "seo_plp_color_simple_t03_white_masculine"
msgstr "seo_plp_color_simple_t03_white_masculine"

msgid "seo_plp_color_simple_t03_white_misty_blue"
msgstr "White and Blue"

msgid "seo_plp_color_simple_t03_white_misty_blue_feminine"
msgstr "seo_plp_color_simple_t03_white_misty_blue_feminine"

msgid "seo_plp_color_simple_t03_white_misty_blue_masculine"
msgstr "seo_plp_color_simple_t03_white_misty_blue_masculine"

msgid "seo_plp_color_simple_t03_white_misty_blue_neuter"
msgstr "seo_plp_color_simple_t03_white_misty_blue_neuter"

msgid "seo_plp_color_simple_t03_white_neuter"
msgstr "seo_plp_color_simple_t03_white_neuter"

msgid "seo_plp_color_simple_t03_white_sage_green"
msgstr "White and Green"

msgid "seo_plp_color_simple_t03_white_sage_green_feminine"
msgstr "seo_plp_color_simple_t03_white_sage_green_feminine"

msgid "seo_plp_color_simple_t03_white_sage_green_masculine"
msgstr "seo_plp_color_simple_t03_white_sage_green_masculine"

msgid "seo_plp_color_simple_t03_white_sage_green_neuter"
msgstr "seo_plp_color_simple_t03_white_sage_green_neuter"

msgid "seo_plp_color_simple_t03_white_stone_gray"
msgstr "White and Gray"

msgid "seo_plp_color_simple_t03_white_stone_gray_feminine"
msgstr "seo_plp_color_simple_t03_white_stone_gray_feminine"

msgid "seo_plp_color_simple_t03_white_stone_gray_masculine"
msgstr "seo_plp_color_simple_t03_white_stone_gray_masculine"

msgid "seo_plp_color_simple_t03_white_stone_gray_neuter"
msgstr "seo_plp_color_simple_t03_white_stone_gray_neuter"

msgid "seo_plp_color_simple_t13_beige"
msgstr "Sand"

msgid "seo_plp_color_simple_t13_beige_feminine"
msgstr "Sand"

msgid "seo_plp_color_simple_t13_beige_masculine"
msgstr "Sand"

msgid "seo_plp_color_simple_t13_beige_neuter"
msgstr "Sand"

msgid "seo_plp_color_simple_t13_black"
msgstr "Black"

msgid "seo_plp_color_simple_t13_black_feminine"
msgstr "seo_plp_color_simple_t13_black_feminine"

msgid "seo_plp_color_simple_t13_black_masculine"
msgstr "seo_plp_color_simple_t13_black_masculine"

msgid "seo_plp_color_simple_t13_black_neuter"
msgstr "seo_plp_color_simple_t13_black_neuter"

msgid "seo_plp_color_simple_t13_clay_brown"
msgstr "Brown"

msgid "seo_plp_color_simple_t13_clay_brown_feminine"
msgstr "seo_plp_color_simple_t13_clay_brown_feminine"

msgid "seo_plp_color_simple_t13_clay_brown_masculine"
msgstr "seo_plp_color_simple_t13_clay_brown_masculine"

msgid "seo_plp_color_simple_t13_clay_brown_neuter"
msgstr "seo_plp_color_simple_t13_clay_brown_neuter"

msgid "seo_plp_color_simple_t13_grey"
msgstr "Grey"

msgid "seo_plp_color_simple_t13_grey_dark_grey"
msgstr "Grey"

msgid "seo_plp_color_simple_t13_grey_dark_grey_feminine"
msgstr "seo_plp_color_simple_t13_grey_dark_grey_feminine"

msgid "seo_plp_color_simple_t13_grey_dark_grey_masculine"
msgstr "seo_plp_color_simple_t13_grey_dark_grey_masculine"

msgid "seo_plp_color_simple_t13_grey_dark_grey_neuter"
msgstr "seo_plp_color_simple_t13_grey_dark_grey_neuter"

msgid "seo_plp_color_simple_t13_grey_feminine"
msgstr "seo_plp_color_simple_t13_grey_feminine"

msgid "seo_plp_color_simple_t13_grey_masculine"
msgstr "seo_plp_color_simple_t13_grey_masculine"

msgid "seo_plp_color_simple_t13_grey_neuter"
msgstr "seo_plp_color_simple_t13_grey_neuter"

msgid "seo_plp_color_simple_t13_olive_green"
msgstr "Green"

msgid "seo_plp_color_simple_t13_olive_green_feminine"
msgstr "seo_plp_color_simple_t13_olive_green_feminine"

msgid "seo_plp_color_simple_t13_olive_green_masculine"
msgstr "seo_plp_color_simple_t13_olive_green_masculine"

msgid "seo_plp_color_simple_t13_olive_green_neuter"
msgstr "seo_plp_color_simple_t13_olive_green_neuter"

msgid "seo_plp_color_simple_t13_sand_midnight_blue"
msgstr "Sand and Blue"

msgid "seo_plp_color_simple_t13_sand_midnight_blue_feminine"
msgstr "seo_plp_color_simple_t13_sand_midnight_blue_feminine"

msgid "seo_plp_color_simple_t13_sand_midnight_blue_masculine"
msgstr "seo_plp_color_simple_t13_sand_midnight_blue_masculine"

msgid "seo_plp_color_simple_t13_sand_midnight_blue_neuter"
msgstr "seo_plp_color_simple_t13_sand_midnight_blue_neuter"

msgid "seo_plp_color_simple_t13_sand_mustard_yellow"
msgstr "Sand and Yellow"

msgid "seo_plp_color_simple_t13_sand_mustard_yellow_feminine"
msgstr "seo_plp_color_simple_t13_sand_mustard_yellow_feminine"

msgid "seo_plp_color_simple_t13_sand_mustard_yellow_masculine"
msgstr "seo_plp_color_simple_t13_sand_mustard_yellow_masculine"

msgid "seo_plp_color_simple_t13_sand_mustard_yellow_neuter"
msgstr "seo_plp_color_simple_t13_sand_mustard_yellow_neuter"

msgid "seo_plp_color_simple_t13_white"
msgstr "White"

msgid "seo_plp_color_simple_t13_white_feminine"
msgstr "seo_plp_color_simple_t13_white_feminine"

msgid "seo_plp_color_simple_t13_white_masculine"
msgstr "seo_plp_color_simple_t13_white_masculine"

msgid "seo_plp_color_simple_t13_white_neuter"
msgstr "seo_plp_color_simple_t13_white_neuter"

msgid "seo_plp_color_simple_t13v_dark"
msgstr "Dark wood effect"

msgid "seo_plp_color_simple_t13v_dark_feminine"
msgstr "Dark wood effect"

msgid "seo_plp_color_simple_t13v_dark_masculine"
msgstr "Dark wood effect"

msgid "seo_plp_color_simple_t13v_dark_neuter"
msgstr "Dark wood effect"

msgid "seo_plp_color_simple_t13v_light"
msgstr "Light wood effect"

msgid "seo_plp_color_simple_t13v_light_feminine"
msgstr "Light wood effect"

msgid "seo_plp_color_simple_t13v_light_masculine"
msgstr "Light wood effect"

msgid "seo_plp_color_simple_t13v_light_neuter"
msgstr "Light wood effect"

msgid "seo_plp_color_simple_t23_inky_black"
msgstr "Graphite"

msgid "seo_plp_color_simple_t23_inky_black_feminine"
msgstr "graphite"

msgid "seo_plp_color_simple_t23_inky_black_masculine"
msgstr "graphite"

msgid "seo_plp_color_simple_t23_inky_black_neuter"
msgstr "graphite"

msgid "seo_plp_color_simple_t23_off_white"
msgstr "White"

msgid "seo_plp_color_simple_t23_off_white_feminine"
msgstr "white"

msgid "seo_plp_color_simple_t23_off_white_masculine"
msgstr "white"

msgid "seo_plp_color_simple_t23_off_white_neuter"
msgstr "white"

msgid "seo_plp_color_simple_t23_oyster_beige"
msgstr "Beige"

msgid "seo_plp_color_simple_t23_oyster_beige_feminine"
msgstr "beige"

msgid "seo_plp_color_simple_t23_oyster_beige_masculine"
msgstr "beige"

msgid "seo_plp_color_simple_t23_oyster_beige_neuter"
msgstr "beige"

msgid "seo_plp_color_simple_t23_pistachio_green"
msgstr "Green"

msgid "seo_plp_color_simple_t23_pistachio_green_feminine"
msgstr "green"

msgid "seo_plp_color_simple_t23_pistachio_green_masculine"
msgstr "green"

msgid "seo_plp_color_simple_t23_pistachio_green_neuter"
msgstr "green"

msgid "seo_plp_color_simple_t23_powder_pink"
msgstr "Pink"

msgid "seo_plp_color_simple_t23_powder_pink_feminine"
msgstr "pink"

msgid "seo_plp_color_simple_t23_powder_pink_masculine"
msgstr "pink"

msgid "seo_plp_color_simple_t23_powder_pink_neuter"
msgstr "pink"

msgid "seo_plywood"
msgstr "plywood"

msgid "seo_rail"
msgstr "rail"

msgid "seo_shallow"
msgstr "shallow"

msgid "seo_shallow_feminine"
msgstr "seo_shallow_feminine"

msgid "seo_shallow_masculine"
msgstr "seo_shallow_masculine"

msgid "seo_slim"
msgstr "slim"

msgid "seo_slim_feminine"
msgstr "seo_slim_feminine"

msgid "seo_slim_masculine"
msgstr "seo_slim_masculine"

msgid "seo_small"
msgstr "small"

msgid "seo_small_feminine"
msgstr "seo_small_feminine"

msgid "seo_small_masculine"
msgstr "seo_small_masculine"

msgid "seo_tall"
msgstr "tall"

msgid "seo_tall_feminine"
msgstr "seo_tall_feminine"

msgid "seo_tall_masculine"
msgstr "seo_tall_masculine"

msgid "seo_top_and_bottom_storage"
msgstr "extra upper and lower storage"

msgid "seo_top_storage"
msgstr "extra top storage"

msgid "seo_veneer"
msgstr "veneer"

msgid "seo_wide"
msgstr "wide"

msgid "seo_wide_feminine"
msgstr "seo_wide_feminine"

msgid "seo_wide_masculine"
msgstr "seo_wide_masculine"

msgid "seo_with"
msgstr "with"

msgid "session"
msgstr "session"

msgid "sessionid"
msgstr "This is a generic cookie name that may have different purposes on different websites. Generally it will be used as a type of anonymous session identifier."

msgid "shipping_spot_section_button_1"
msgstr "Reserve Your Shipping Now"

msgid "shipping_spot_section_header_1"
msgstr "Tylko's Coming To Your Town"

msgid "shipping_spot_section_text_1"
msgstr "Tylko is currently only available in Europe.<br>If you leave your email with us, we'll hold a dedicated shipping spot for you when we launch in your country!"

msgid "shippingpage_header2_returnpolicy"
msgstr "Return Policy"

msgid "shippingpage_header2_shippingtime"
msgstr "Shipping Time"

msgid "shippingpage_text_returnpolicy"
msgstr "We’re sure you’ll love your Tylko product, but if for any reason you’re not happy we give you 100 days from delivery to return it - with free shipping within the EU! To make a return, just email <NAME_EMAIL>, or give us a call at +44 ************ or +49 32 *********. We’ll send you all the boxes you'll need and arrange the pickup. Then, once your package reaches us we’ll refund you fully within 14 days. You'll get a correction invoice, and after you accept it the refund will be made via bank transfer. For more detailed information on Tylko’s return policy, you can see our Terms of Service.<br><br>If you believe your Tylko purchase is incomplete or defective, you can easily get it repaired or replaced by emailing <NAME_EMAIL>. We will contact you within 14 days and let you know if we can fix or replace your product. We’ll send you all the boxes you'll need and arrange the pickup, as well as cover any shipping costs in connection with your issue. For more detailed information, please see our Terms of Service."

msgid "show_password_checkbox"
msgstr "Show password"

msgid "sideboard_in_dustypink"
msgstr "Dusty-pink sideboard with plinth"

msgid "sideboard_in_terracota"
msgstr "Sideboard in terracota colour with steel lift-up legs"

msgid "sotty_fabric_corduroy"
msgstr "Corduroy"

msgid "sotty_fabric_wool"
msgstr "Wool"

msgid "sp_landing"
msgstr "This cookie is used to implement Spotify audio content on the website. It can also be used to record user interaction and preferences in combination with audio content. This information can be used for statistics and marketing purposes."

msgid "sp_t"
msgstr "This cookie is used to implement Spotify audio content on the website. It can also be used to record user interaction and preferences in combination with audio content. This can be used for statistics and marketing purposes."

msgid "split_version"
msgstr "This cookie is used to define user frontend application type."

msgid "splusall_hp_CTA"
msgstr "Learn more"

msgid "splusall_hp_body"
msgstr "Explore Tylko's fully-customisable Sideboards."

msgid "splusall_hp_headline"
msgstr "Form and function, combined."

msgid "splusall_hp_subheadline"
msgstr "Tylko Sideboards"

msgid "st01p_faq_answer_1"
msgstr "Try your shelf for 100 days. If for any reason you’re not happy with it, we’ll pick it back up for free and give you a full refund. No questions asked."

msgid "st01p_faq_answer_2"
msgstr "We offer free shipping to: Austria, Belgium, Bulgaria, Croatia, Czech, Denmark, Estonia, Finland, France, Germany, Greece, Hungary, Ireland, Italy, Latvia, Lithuania, Luxembourg, the Netherlands, Norway, Poland, Portugal, Romania, Slovakia, Slovenia, Spain, Sweden, Switzerland, Norway and the United Kingdom. If your country is not on the list, please check our FAQ for more shipping details."

msgid "st01p_faq_answer_3_1"
msgstr "We accept MasterCard and Visa, as well as payments through PayPal. Other payment options depend on the country you're in:  "

msgid "st01p_faq_answer_3_2"
msgstr "Germany: International Bank Transfer, SEPA Direct Debit, Klarna "

msgid "st01p_faq_answer_3_3"
msgstr "Austria: Bank Transfer, SEPA Direct Debit, Klarna"

msgid "st01p_faq_answer_3_5"
msgstr "United Kingdom: MasterCard, Visa, International Bank Transfer, SEPA, PayPal"

msgid "st01p_faq_answer_3_6"
msgstr "All transactions (except Klarna) are processed securely via our payment provider Adyen."

msgid "st01p_faq_answer_4"
msgstr "We make every piece of furniture with top-quality materials that stand up to tough daily use. For extra peace of mind, we give you a solid 2-year guarantee. For more details and the conditions, please read our <a class=\"link--color text-no-underline\"  href=\"https://tylko.com/terms/\">Terms of Service</a>."

msgid "st01p_faq_answer_5"
msgstr "Sideboards are available in the colours and finishes found in the Tylko Original Plywood and Veneer lines."

msgid "st01p_faq_answer_6"
msgstr "We offer sample kits that let you experience our great colours and finishes – perfect if you’re having a hard time deciding! You can order three different sample kits at the bottom of <a href=\"https://tylko.com/product-lines/\" class=\"link--color text-no-underline\">this page</a>. We can also provide the RAL/NCS codes for all of our colours. "

msgid "st01p_faq_answer_7"
msgstr "Both lines offer the same four styles and customisation options, but they also have their own personalities. Available in five timeless colours, Tylko Original Classic in Plywood is made from 13 layers of durable plywood wrapped in laminate and features aluminium handles designed in-house. Tylko Original Classic in Veneer is made from particle board with a natural veneer topper and is available in two earthy tones inspired by nature, made complete with solid wood handles for a tactile experience."

msgid "st01p_faq_answer_8"
msgstr "Absolutely. We only use sustainably sourced plywood and non-toxic glues and oils. The handles are also rounded to avoid any nasty bumps. The shelf itself is very sturdy and strong, so it won’t sway, sag or topple over. But for added peace of mind, we provide wall mounts so you can attach it firmly to your wall. Because of our furniture’s ergonomic features, our furniture is suitable to be handled by children over the age of 12."

msgid "st01p_faq_header"
msgstr "Frequently asked questions"

msgid "st01p_faq_question_1"
msgstr "How does the 100-day free return work? "

msgid "st01p_faq_question_2"
msgstr "Which countries do you offer free shipping to?"

msgid "st01p_faq_question_3"
msgstr "What payment options are available?"

msgid "st01p_faq_question_4"
msgstr "Does Tylko furniture come with a guarantee?"

msgid "st01p_faq_question_5"
msgstr "Is the new Sideboard available in other colours and finishes?"

msgid "st01p_faq_question_6"
msgstr "How will I know if a shelf colour will suit my home? "

msgid "st01p_faq_question_7"
msgstr "What’s the difference between Tylko Original Classic in Plywood and Veneer?"

msgid "st01p_faq_question_8"
msgstr "Is a Tylko shelf safe to use in a child’s room?"

msgid "st01p_pdp_features_body_1"
msgstr "A stylish plywood plinth for lift with looks. Colour-matched to the shelf."

msgid "st01p_pdp_product_specs_header_1"
msgstr "Product details"

msgid "st01p_pdp_product_specs_section_1"
msgstr "Your sideboard"

msgid "st01p_pdp_product_specs_section_1_1"
msgstr "Width"

msgid "st01p_pdp_product_specs_section_1_2"
msgstr "Height"

msgid "st01p_pdp_product_specs_section_1_3"
msgstr "Depth"

msgid "st01p_pdp_product_specs_section_1_3_feet"
msgstr "(Including the standard feet)"

msgid "st01p_pdp_product_specs_section_1_3_legs"
msgstr "(Including plinth)"

msgid "st01p_pdp_product_specs_section_1_4"
msgstr "Compartment max load"

msgid "st01p_pdp_product_specs_section_1_5"
msgstr "Shelf max load"

msgid "st01p_pdp_product_specs_section_1_6"
msgstr "Top board seams"

msgid "st01p_pdp_product_specs_section_1_6_tooltip"
msgstr "Shelves wider than 240 cm come with a visible connection line between the segments."

msgid "st01p_pdp_product_specs_section_1_6a"
msgstr "1 seam"

msgid "st01p_pdp_product_specs_section_1_6b"
msgstr "2 seams"

msgid "st01p_pdp_product_specs_section_1_7"
msgstr "Features"

msgid "st01p_pdp_product_specs_section_1_7a"
msgstr "Door"

msgid "st01p_pdp_product_specs_section_1_7b"
msgstr "Drawers"

msgid "st01p_pdp_product_specs_section_1_7c"
msgstr "Standard feet"

msgid "st01p_pdp_product_specs_section_1_7d"
msgstr "Plinth"

msgid "st01p_pdp_product_specs_section_1_7e"
msgstr "Cable opening"

msgid "st01p_pdp_product_specs_section_2"
msgstr "Full features"

msgid "st01p_pdp_product_specs_section_2_1"
msgstr "Doors"

msgid "st01p_pdp_product_specs_section_2_1_body"
msgstr "<li>Weighted to self-close</li><li>Sleek extruded aluminium handles  </li><li>Pre-mounted click-in assembly elements</li>"

msgid "st01p_pdp_product_specs_section_2_2"
msgstr "Drawers"

msgid "st01p_pdp_product_specs_section_2_2_body"
msgstr "<li>Fully extendable runners for deep access </li><li>Weighted to self-close </li><li>Sleek extruded aluminium handles  </li><li>Pre-mounted click-in assembly elements</li>"

msgid "st01p_pdp_product_specs_section_2_3"
msgstr "Standard feet"

msgid "st01p_pdp_product_specs_section_2_3_body"
msgstr "<li>Adjustable for uneven floors</li>"

msgid "st01p_pdp_product_specs_section_2_4"
msgstr "Plinth"

msgid "st01p_pdp_product_specs_section_2_4_body"
msgstr "<li>Sturdy, sleek plywood plinth</li><li>Colour-matched to the shelf</li><li>Simple assembly</li>"

msgid "st01p_pdp_product_specs_section_2_5"
msgstr "Cable management"

msgid "st01p_pdp_product_specs_section_2_5_body"
msgstr "<li>Steel hole with magnetic cover for better organisation</li><li>Colour-matched to the shelf for a seamless look </li><li>Works with all plugs and cable</li>"

msgid "st01p_pdp_product_specs_section_2_6"
msgstr "What's in the package"

msgid "st01p_pdp_product_specs_section_2_6_body"
msgstr "<li>A personalised assembly guide</li><li>Padded tool for shelf disassembly</li><li>Wall fasteners and universal screws for safety</li>"

msgid "st01p_pdp_product_specs_section_3"
msgstr "Material and quality"

msgid "st01p_pdp_product_specs_section_3_1_body"
msgstr "Plywood is one of the strongest materials out there. Our premium plywood is made from 13 layers of birchwood, carefully selected for its strength and beauty. We leave the edges exposed and hand-oiled to showcase the natural grain of the wood."

msgid "st01p_pdp_product_specs_section_3_2"
msgstr "Caring for your shelf"

msgid "st01p_pdp_product_specs_section_3_2_body"
msgstr "Once assembled, use gentle furniture cleaners and a dry cloth to keep your shelf pristine."

msgid "st01p_pdp_product_specs_section_4"
msgstr "Wall securing"

msgid "st01p_pdp_product_specs_section_4_body"
msgstr "Our click-in system and rigid back panels ensure that every Tylko shelf is incredibly stable. However for your safety, securing the shelf to the wall is mandatory. We include all of the necessary wall mounting hardware in every order so you can safely attach your Tylko to the wall. "

msgid "st01p_pdp_product_specs_section_5"
msgstr "Assembly"

msgid "st01p_pdp_product_specs_section_5_1"
msgstr "Click it together. Quick and easy."

msgid "st01p_pdp_product_specs_section_5_1_body"
msgstr "Save valuable time and energy with our colour-coded, click-together assembly system. Just unpack the numbered boxes in order, follow the easy instructions and you’ll have your shelf up in minutes. "

msgid "st01p_pdp_product_specs_section_5_2"
msgstr "Doors"

msgid "st01p_pdp_product_specs_section_5_2_body"
msgstr "<li>Pre-mounted hinge hardware</li><li>Click together to connect</li>"

msgid "st01p_pdp_product_specs_section_5_3"
msgstr "Drawers"

msgid "st01p_pdp_product_specs_section_5_3_body"
msgstr "<li>Pre-mounted slider hardware </li><li>Parts easily click together</li><li>Slide in the shelf to complete </li><li>Max load bearing: 30 kg</li>"

msgid "st01p_pdp_usp_title_1"
msgstr "Smart packaging"

msgid "st01p_pdp_usp_title_2"
msgstr "Easy click-in assembly"

msgid "st01p_pdp_usp_title_3"
msgstr "100 days to settle in"

msgid "st01v_faq_answer_1"
msgstr "Try your shelf for 100 days. If for any reason you’re not happy with it, we’ll pick it back up for free and give you a full refund. No questions asked."

msgid "st01v_faq_answer_2"
msgstr "We offer free shipping to: Austria, Belgium, Bulgaria, Croatia, Czech, Denmark, Estonia, Finland, France, Germany, Greece, Hungary, Ireland, Italy, Latvia, Lithuania, Luxembourg, the Netherlands, Norway, Poland, Portugal, Romania, Slovakia, Slovenia, Spain, Sweden, Switzerland, Norway and the United Kingdom. If your country is not on the list, please check our FAQ for more shipping details."

msgid "st01v_faq_answer_3_1"
msgstr "We accept MasterCard and Visa, as well as payments through PayPal. Other payment options depend on the country you're in:  "

msgid "st01v_faq_answer_3_2"
msgstr "Germany: International Bank Transfer, SEPA Direct Debit, Klarna "

msgid "st01v_faq_answer_3_3"
msgstr "Austria: Bank Transfer, SEPA Direct Debit, Klarna"

msgid "st01v_faq_answer_3_5"
msgstr "United Kingdom: MasterCard, Visa, International Bank Transfer, SEPA, PayPal"

msgid "st01v_faq_answer_3_6"
msgstr "All transactions (except Klarna) are processed securely via our payment provider Adyen."

msgid "st01v_faq_answer_4"
msgstr "We make every piece of furniture with top-quality materials that stand up to tough daily use. For extra peace of mind, we give you a solid 2-year guarantee. For more details and the conditions, please read our <a class=\"link--color text-no-underline\" href=\"https://tylko.com/terms/\">Terms of Service</a>."

msgid "st01v_faq_answer_5"
msgstr "Sideboards are available in the colours and finishes found in the Tylko Original in Plywood and Veneer lines."

msgid "st01v_faq_answer_6"
msgstr "We offer sample kits that let you experience our great colours and finishes – perfect if you’re having a hard time deciding! You can order three different sample kits at the bottom of <a href=\"https://tylko.com/product-lines/\" class=\"link--color text-no-underline\">this page</a>. We can also provide the RAL/NCS codes for all of our colours. "

msgid "st01v_faq_answer_7"
msgstr "Both lines offer the same four styles and customisation options, but they also have their own personalities. Available in five timeless colours, Tylko Original Classic in Plywood is made from 13 layers of durable plywood wrapped in laminate and features aluminium handles designed in-house. Tylko Original Classic in Veneer is made from particle board with a natural veneer topper and is available in two earthy tones inspired by nature, made complete with solid wood handles for a tactile experience."

msgid "st01v_faq_answer_8"
msgstr "Absolutely. We use emission-free, non-toxic particle board, as well as rounded handles. Our soft-close doors and drawers ensure no fingers get caught. The included wall mounts offer extra stability and safety. Because of our furniture’s ergonomic features, our furniture is suitable to be handled by children over the age of 12."

msgid "st01v_faq_header"
msgstr "Frequently asked questions"

msgid "st01v_faq_question_1"
msgstr "How does the 100-day free return work? "

msgid "st01v_faq_question_2"
msgstr "Which countries do you offer free shipping to?"

msgid "st01v_faq_question_3"
msgstr "What payment options are available?"

msgid "st01v_faq_question_4"
msgstr "Does Tylko furniture come with a guarantee?"

msgid "st01v_faq_question_5"
msgstr "Is the new Sideboard available in other colours and finishes?"

msgid "st01v_faq_question_6"
msgstr "How will I know if a shelf colour will suit my home? "

msgid "st01v_faq_question_7"
msgstr "What’s the difference between Tylko Original Classic in Plywood and Veneer?"

msgid "st01v_faq_question_8"
msgstr "Is a Tylko shelf safe to use in a child’s room?"

msgid "st01v_pdp_product_specs_header_1"
msgstr "Product details"

msgid "st01v_pdp_product_specs_section_1"
msgstr "Your sideboard"

msgid "st01v_pdp_product_specs_section_1_1"
msgstr "Width"

msgid "st01v_pdp_product_specs_section_1_2"
msgstr "Height"

msgid "st01v_pdp_product_specs_section_1_3"
msgstr "Depth"

msgid "st01v_pdp_product_specs_section_1_4"
msgstr "Compartment max load"

msgid "st01v_pdp_product_specs_section_1_5"
msgstr "Shelf max load"

msgid "st01v_pdp_product_specs_section_1_6"
msgstr "Top board seams"

msgid "st01v_pdp_product_specs_section_1_6_tooltip"
msgstr "Shelves wider than 240 cm come with a visible connection line between the segments."

msgid "st01v_pdp_product_specs_section_1_7"
msgstr "Features"

msgid "st01v_pdp_product_specs_section_1_7a"
msgstr "Door"

msgid "st01v_pdp_product_specs_section_1_7b"
msgstr "Drawers"

msgid "st01v_pdp_product_specs_section_1_7d"
msgstr "Plinth"

msgid "st01v_pdp_product_specs_section_1_7e"
msgstr "Cable opening"

msgid "st01v_pdp_product_specs_section_2"
msgstr "Full features"

msgid "st01v_pdp_product_specs_section_2_1"
msgstr "Doors"

msgid "st01v_pdp_product_specs_section_2_1_body"
msgstr "<li>Weighted to self-close </li><li>Solid wood handles for a tactile experience </li><li>Pre-mounted click-in assembly elements</li>"

msgid "st01v_pdp_product_specs_section_2_2"
msgstr "Drawers"

msgid "st01v_pdp_product_specs_section_2_2_body"
msgstr "<li>Fully-extendable runners for deep access</li><li>Weighted to self-close </li><li>Solid wood handles for a tactile experience </li><li>Pre-mounted click-in assembly elements</li>"

msgid "st01v_pdp_product_specs_section_2_3"
msgstr "Standard feet"

msgid "st01v_pdp_product_specs_section_2_3_body"
msgstr "<li>Adjustable for uneven floors</li>"

msgid "st01v_pdp_product_specs_section_2_4"
msgstr "Plinth"

msgid "st01v_pdp_product_specs_section_2_4_body"
msgstr "<li>High-quality real wood veneer</li><li>Matched to the shelf</li><li>Simple assembly</li>"

msgid "st01v_pdp_product_specs_section_2_5"
msgstr "Cable management"

msgid "st01v_pdp_product_specs_section_2_5_body_a"
msgstr "<li>Steel cable hole with colour accent magnetic cover</li><li>Powder coated in subtle Dusty Pink</li><li>Compatible with all plugs and cable sizes</li>"

msgid "st01v_pdp_product_specs_section_2_6"
msgstr "What's in the package"

msgid "st01v_pdp_product_specs_section_2_6_body"
msgstr "<li>A personalised assembly guide</li><li>Padded tool for shelf disassembly</li><li>Wall fasteners and universal screws for safety</li>"

msgid "st01v_pdp_product_specs_section_3"
msgstr "Material and quality"

msgid "st01v_pdp_product_specs_section_3_1_body"
msgstr "Every Tylko Original Classic in Veneer shelf is made from particle board and covered in a layer of premium-grade natural veneer. Made from oak or ash, it's finished with planet-friendly water-based lacquer and non-toxic glues. Each layer has a one-of-a-kind wood grain that invites touch – a tactile, beautiful detail. "

msgid "st01v_pdp_product_specs_section_3_2"
msgstr "Caring for your shelf"

msgid "st01v_pdp_product_specs_section_3_2_body"
msgstr "Once assembled, use gentle furniture cleaners and a dry cloth to keep your shelf pristine."

msgid "st01v_pdp_product_specs_section_4"
msgstr "Wall securing"

msgid "st01v_pdp_product_specs_section_4_body"
msgstr "Our click-in system and rigid back panels ensure that every Tylko shelf is incredibly stable. However for your safety, securing the shelf to the wall is mandatory. We include all of the necessary wall mounting hardware in every order so you can safely attach your Tylko to the wall. "

msgid "st01v_pdp_product_specs_section_5"
msgstr "Assembly"

msgid "st01v_pdp_product_specs_section_5_1"
msgstr "Click it together. Quick and easy."

msgid "st01v_pdp_product_specs_section_5_1_body"
msgstr "Save valuable time and energy with our colour-coded, click-together assembly system. Just unpack the numbered boxes in order, follow the easy instructions and you’ll have your shelf up in minutes. "

msgid "st01v_pdp_product_specs_section_5_2"
msgstr "Doors"

msgid "st01v_pdp_product_specs_section_5_2_body"
msgstr "<li>Pre-mounted hinge hardware</li><li>Click together to connect</li>"

msgid "st01v_pdp_product_specs_section_5_3"
msgstr "Drawers"

msgid "st01v_pdp_product_specs_section_5_3_body"
msgstr "<li>Pre-mounted slider hardware</li><li>Parts easily click together</li><li>Slide in the shelf to complete </li><li>Max load bearing: 30 kg</li>"

msgid "st01v_pdp_usp_title_1"
msgstr "Smart packaging"

msgid "st01v_pdp_usp_title_2"
msgstr "Easy click-in assembly"

msgid "st01v_pdp_usp_title_3"
msgstr "100 days to settle in"

msgid "st02_pdp_explore_body_1"
msgstr "Other colours and materials"

msgid "st02_pdp_explore_body_2"
msgstr "Different depths"

msgid "st02_pdp_explore_body_3"
msgstr "New shoe racks"

msgid "st02_pdp_explore_body_4"
msgstr "Our product lines"

msgid "st02_pdp_explore_body_5"
msgstr "All wood finishes"

msgid "st02_pdp_explore_header"
msgstr "Meet all Tylko storage solutions"

msgid "st02_pdp_product_specs_section_1"
msgstr "Your sideboard"

msgid "st02_pdp_product_specs_section_1_3_feet"
msgstr "(Including the standard feet)"

msgid "st02_pdp_product_specs_section_1_3_legs"
msgstr "(Including the legs)"

msgid "st02_pdp_product_specs_section_1_6"
msgstr "Top board seams"

msgid "st02_pdp_product_specs_section_1_6_tooltip"
msgstr "Shelves wider than 240 cm come with a&nbsp;visible joint at the top."

msgid "st02_pdp_product_specs_section_1_7"
msgstr "Features"

msgid "st02_pdp_product_specs_section_1_7a"
msgstr "Doors"

msgid "st02_pdp_product_specs_section_1_7b"
msgstr "Drawers"

msgid "st02_pdp_product_specs_section_1_7c"
msgstr "Standard feet"

msgid "st02_pdp_product_specs_section_1_7d"
msgstr "Legs"

msgid "st02_pdp_product_specs_section_1_7e"
msgstr "Cable opening"

msgid "st02_pdp_product_specs_section_2"
msgstr "Full features"

msgid "st02_pdp_product_specs_section_2_1"
msgstr "Doors"

msgid "st02_pdp_product_specs_section_2_2_body"
msgstr "<li>Extendable runners for deep access</li><li>Weighted to self-close </li><li>Extruded aluminium handles </li><li>Pre-mounted elements and click-in assembly </li>"

msgid "st02_pdp_product_specs_section_2_3"
msgstr "Standard feet"

msgid "st02_pdp_product_specs_section_2_3_body"
msgstr "<li>Adjustable for uneven floors</li>"

msgid "st02_pdp_product_specs_section_2_4"
msgstr "Legs"

msgid "st02_pdp_product_specs_section_2_4_body"
msgstr "<li>High-quality steel construction</li><li>11cm height, adjustable to 12cm</li><li>Perfect elevation for robot vacuum cleaners</li><li>Simple click-in assembly</li>"

msgid "st02_pdp_product_specs_section_2_5"
msgstr "Cable management"

msgid "st02_pdp_product_specs_section_2_5_body"
msgstr "<li>Steel hole with magnetic cover for better organisation</li><li>Colour-matched to the shelf for a seamless look</li><li>Works with all plugs and cables</li>"

msgid "st02_pdp_product_specs_section_4"
msgstr "Assembly"

msgid "st02_pdp_product_specs_section_4_2"
msgstr "Doors"

msgid "st02_pdp_product_specs_section_4_2_body"
msgstr "<li>Pre-mounted hinge hardware</li><li>Click together to connect</li>"

msgid "st02_pdp_product_specs_section_4_3"
msgstr "Drawers"

msgid "st02_pdp_product_specs_section_4_3_body"
msgstr "<li>Pre-mounted slider hardware</li><li>Parts easily click together</li><li>Slide in the shelf to complete</li><li>Max load bearing: 30 kg</li>"

msgid "st02_pdp_product_specs_section_4_4"
msgstr "Wall securing"

msgid "st02_pdp_product_specs_section_4_4_body"
msgstr "Our click-in system and rigid back panels ensure that every Tylko shelf is incredibly stable. However for your safety, securing the shelf to the wall is mandatory. We include all of the necessary wall mounting hardware in every order so you can safely attach your Tylko to the wall. "

msgid "st02_pdp_product_specs_section_4_body"
msgstr "Our click-in system and rigid back panels ensure that every Tylko shelf is incredibly stable. However for your safety, securing the shelf to the wall is mandatory. We include all of the necessary wall mounting hardware in every order so you can safely attach your Tylko to the wall. "

msgid "summary_promocode_button_remove"
msgstr "Remove Promo Code"

msgid "summary_promocode_info"
msgstr "You can only use a Promo Code once."

msgid "summary_sale_button_backtodiscount"
msgstr "Change to 20% Discount"

msgid "survey_thank_page_buton_1"
msgstr "Continue shopping"

msgid "survey_thank_page_buton_2"
msgstr "Return to homepage"

msgid "survey_thank_page_header_1"
msgstr "Thanks for taking the survey!"

msgid "survey_thank_page_parahraph_1_1"
msgstr "Your feedback helps us improve every day. We’re sending your voucher code right now, so please check your inbox!"

msgid "switching_regions_popup_button_1"
msgstr "Switching Stores"

msgid "switching_regions_popup_header_1"
msgstr "Switching Stores"

msgid "switching_regions_popup_text_1_austria"
msgstr "We can only ship to Austria from our Austrian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_belgium"
msgstr "We can only ship to Belgium from our Belgian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_bulgaria"
msgstr "We can only ship to Bulgaria from our Bulgarian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_croatia"
msgstr "We can only ship to Croatia from our Croatian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_czech"
msgstr "We can only ship to Czech Rep. from our Czech store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_denmark"
msgstr "We can only ship to Denmark from our Danish store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_estonia"
msgstr "We can only ship to Estonia from our Estonian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_finland"
msgstr "We can only ship to Finland from our Finnish store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_france"
msgstr "We can only ship to France from our French store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_germany"
msgstr "We can only ship to Germany from our German store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_greece"
msgstr "We can only ship to Greece from our Greek store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_hungary"
msgstr "We can only ship to Hungary from our Hungarian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_ireland"
msgstr "We can only ship to Ireland from our Irish store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_italy"
msgstr "We can only ship to Italy from our Italian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_latvia"
msgstr "We can only ship to Latvia from our Latvian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_lithuania"
msgstr "We can only ship to Lithuania from our Lithuanian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_luxembourg"
msgstr "We can only ship to Luxembourg from our Luxembourgian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_netherlands"
msgstr "We can only ship to Netherlands from our Dutch store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_norway"
msgstr "We can only ship to Norway from our Norwegian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_poland"
msgstr "We can only ship to Poland from our Polish store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_portugal"
msgstr "We can only ship to Portugal from our Portuguese store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_romania"
msgstr "We can only ship to Romania from our Romanian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_slovakia"
msgstr "We can only ship to Slovakia from our Slovakian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_slovenia"
msgstr "We can only ship to Slovenia from our Slovenian store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_spain"
msgstr "We can only ship to Spain from our Spanish store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_sweden"
msgstr "We can only ship to Sweden from our Swedish store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_switzerland"
msgstr "We can only ship to Switzerland from our Swiss store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "switching_regions_popup_text_1_united_kingdom"
msgstr "We can only ship to United Kingdom from our English store. If you switch stores, your order will stay the same, but prices may vary. You’ll be able to review it before purchase."

msgid "t01_chigh_pdp_section_4_description_1"
msgstr "View your shelf in your space before you buy it, and edit size, height and style  on the spot. With the Tylko app, perfect-fit furniture is guaranteed. "

msgid "t01_chigh_pdp_section_4_header_1"
msgstr "Get a perfect fit <span class=\"md:block\">with the Tylko app</span>"

msgid "t01_pdp_product_specs_header_1"
msgstr "Product details"

msgid "t01_pdp_product_specs_header_2"
msgstr "Assembly"

msgid "t01_pdp_product_specs_header_4"
msgstr "Material and quality"

msgid "t01_pdp_product_specs_header_5"
msgstr "Service"

msgid "t01_pdp_product_specs_section_1_header_1"
msgstr "Shelf parameters"

msgid "t01_pdp_product_specs_section_1_subheader_4"
msgstr "Compartment max load"

msgid "t01_pdp_product_specs_section_1_subheader_5"
msgstr "Shelf max load"

msgid "t01_pdp_product_specs_section_2_description_1"
msgstr "<li>Pre-mounted and click into place</li><li>Weighted to self-close</li><li>Feature extruded aluminum handles</li>"

msgid "t01_pdp_product_specs_section_2_description_2"
msgstr "<li>Easy to assemble - click into place</li><li>Full extension runners for easy access</li><li>Weighted to self-close</li><li>Feature extruded aluminum handles</li>"

msgid "t01_pdp_product_specs_section_2_description_3"
msgstr "<li>Adjustable to fit uneven floorboards</li><li>Include felt pads to protect against scratching</li>"

msgid "t01_pdp_product_specs_section_2_description_4"
msgstr "<li>a personalised instruction manual</li><li>a padded tool to disassemble your shelf</li><li>wall fasteners and universal screws for safety purposes</li><li>floor-protecting pads to protect floors</li>"

msgid "t01_pdp_product_specs_section_2_header_1"
msgstr "Features"

msgid "t01_pdp_product_specs_section_2_subheader_1"
msgstr "Doors"

msgid "t01_pdp_product_specs_section_2_subheader_2"
msgstr "Drawers"

msgid "t01_pdp_product_specs_section_2_subheader_3"
msgstr "Shelf feet"

msgid "t01_pdp_product_specs_section_2_subheader_4"
msgstr "What's included"

msgid "t01_pdp_product_specs_section_3_description_1"
msgstr "Save valuable time and energy with our colour-coded, click-together assembly system. Just unpack the numbered boxes in order, follow the easy instructions and you’ll have your shelf up in minutes."

msgid "t01_pdp_product_specs_section_3_description_1_2"
msgstr "If your shelf is higher than 60 cm or wider than 150 cm, we recommend fastening it to a wall. We also recommend fastening all shelves with a 24 cm depth. You'll find suitable hardware and wall plugs to do this inside your package. This step will make your shelf extra stable."

msgid "t01_pdp_product_specs_section_3_description_2_1"
msgstr "<li>Pre-mounted hinge hardware</li><li>Click together to connect</li>"

msgid "t01_pdp_product_specs_section_3_description_3_1"
msgstr "<li>Pre-mounted slider hardware</li><li>Parts easily click together</li><li>Slide in the shelf to complete</li><li>Max load bearing: 30 kg</li>"

msgid "t01_pdp_product_specs_section_3_subheader_1"
msgstr "Click it together. Quick and easy."

msgid "t01_pdp_product_specs_section_3_subheader_2"
msgstr "Doors"

msgid "t01_pdp_product_specs_section_3_subheader_3"
msgstr "Drawers"

msgid "t01_pdp_product_specs_section_4_description_1_1"
msgstr "Plywood is one of the strongest materials around. Our premium plywood is made of 13 layers of birch hardwood selected for its strength and beauty. We leave the edges exposed and hand-oiled to showcase the natural grain of the wood fiber."

msgid "t01_pdp_product_specs_section_4_description_1_2"
msgstr "13 layers of compressed birchwood"

msgid "t01_pdp_product_specs_section_4_description_1_3"
msgstr "Hand-polished with toxin-free plant oil."

msgid "t01_pdp_product_specs_section_4_description_2_1"
msgstr "Once assembled, use gentle furniture cleaners and a dry towel to keep your shelf in tip-top shape."

msgid "t01_pdp_product_specs_section_4_subheader_1"
msgstr "Material"

msgid "t01_pdp_product_specs_section_4_subheader_2"
msgstr "Caring for your shelf"

msgid "t01_pdp_product_specs_section_5_description_1"
msgstr "If for any reason you’re not happy with your shelf, we’ll pick it back up for free within 100 days for a full refund. No questions asked."

msgid "t01_pdp_product_specs_section_5_description_2"
msgstr "We professionally deliver your Tylko shelf for free – right to your doorstep. It will arrive in labelled flat pack boxes for quick, easy assembly."

msgid "t01_pdp_product_specs_section_5_description_3"
msgstr "We ship to the following countries: Austria, Belgium, Bulgaria, Croatia, Czech Republic, Denmark, Estonia, Finland, France, Germany, Greece, Hungary, Ireland, Italy, Latvia, Lithuania, Luxembourg, The Netherlands, Poland, Portugal, Romania, Slovakia, Slovenia, Spain, Sweden, Switzerland, Norway and the United Kingdom."

msgid "t01_pdp_product_specs_section_5_description_3_1"
msgstr "If for any reason you’re not happy with your shelf, we’ll pick it back up for free within 100 days and give you a full refund. No questions asked."

msgid "t01_pdp_product_specs_section_5_description_4_1"
msgstr "We accept most major credit cards (Mastercard, Visa) and payments through PayPal.<br><br>Additional payment options differ depending on the country. All transactions are processed securely via our payment provider Adyen.<br><br>For more information regarding payment please read our"

msgid "t01_pdp_product_specs_section_5_description_4_2"
msgstr "FAQ."

msgid "t01_pdp_product_specs_section_5_description_5"
msgstr "All of our furniture is made from high-quality materials that can withstand tough everyday use. We provide a two-year guarantee against failure and manufacturing flaws. For more details and conditions please read our "

msgid "t01_pdp_product_specs_section_5_description_5_button"
msgstr "Terms of Service."

msgid "t01_pdp_product_specs_section_5_description_6"
msgstr "We pride ourselves on the Tylko Original Classic being a safe, child-friendly shelf. Our wood is FSC- and PEFC-certified and comes only from ecological, sustainably managed forests. We use non-toxic glues and oils for added peace of mind."

msgid "t01_pdp_product_specs_section_5_subheader_1"
msgstr "Free returns"

msgid "t01_pdp_product_specs_section_5_subheader_2"
msgstr "Free delivery"

msgid "t01_pdp_product_specs_section_5_subheader_3"
msgstr "Shipping"

msgid "t01_pdp_product_specs_section_5_subheader_4"
msgstr "Payments"

msgid "t01_pdp_product_specs_section_5_subheader_5"
msgstr "The Tylko guarantee"

msgid "t01_pdp_product_specs_section_5_subheader_6"
msgstr "Safety practices and certificates"

msgid "t01_pdp_section_2_cta_1"
msgstr "Play video"

msgid "t01_pdp_section_3_description_1"
msgstr "Our click-in system makes assembly fast and simple. We are redefining what flat pack furniture can be."

msgid "t01_pdp_section_3_description_2"
msgstr "Recyclable labelled packages. Plain-language manuals. A Tylko shelf is a breeze to build."

msgid "t01_pdp_section_3_description_3"
msgstr "Pre-mounted and colour-coded connectors click into place with ease – and make it simple to dismantle when it’s time to move home."

msgid "t01_pdp_section_3_header_1"
msgstr "Easy assembly"

msgid "t01_pdp_section_4_description_1"
msgstr "See your shelf live in your space before you decide to buy it. Edit size, height and style on the fly. Furniture shopping with the powerful Tylko app always means a perfect fit."

msgid "t01_pdp_section_4_header_1"
msgstr "Get a perfect fit with the Tylko app"

msgid "t01_pdp_section_5_description_1"
msgstr "Friendly<br>personal service"

msgid "t01_pdp_section_5_description_2"
msgstr "<span class=\"bold\">100 days</span><br>to fall in love"

msgid "t01_pdp_section_5_description_3"
msgstr "<span class=\"bold\">100 000</span><br>happy customers"

msgid "t01_pdp_section_5_header_1"
msgstr "We’re here to make<br>you comfortable"

msgid "t01_pdp_section_7_header_1"
msgstr "Frequently asked questions"

msgid "t01_pdp_section_7_header_2_1"
msgstr "What’s the difference between Tylko Original shelves?"

msgid "t01_pdp_section_7_header_2_2"
msgstr "How will I know if a shelf colour will suit my home?"

msgid "t01_pdp_section_7_header_2_3"
msgstr "Is a Tylko shelf safe to use in a child’s room?"

msgid "t01_pdp_section_7_header_2_4"
msgstr "How does the 100-day free return work?"

msgid "t01_pdp_section_7_header_2_5"
msgstr "Why do you offer only three row sizes?"

msgid "t01_pdp_section_7_header_2_6"
msgstr "Why is the slimmer 24 cm depth only available on selected product categories?"

msgid "t01_pdp_section_7_paragraph_1"
msgstr "The Tylko Original Classic and Tylko Original Modern lines both have the same modern aesthetic when it comes to pattern, grid, gradient and slant styles, as well as the same door and drawer design. The difference with the Original Classic in Plywood is that it's constructed from durable premium plywood with exposed edges showcasing the 13 layers of natural birchwood. The Tylko Original Classic in Plywood also features rounded aluminum handles that run along the edge of the door and drawers for an easy grab and safety."

msgid "t01_pdp_section_7_paragraph_2"
msgstr "We offer sample kits that let you experience our great colours and finishes – perfect if you’re having a hard time deciding! You can order three different sample kits at the bottom of <a href=\"https://tylko.com/product-lines/\">this page</a>. We can also provide the RAL/NCS codes for all of our colours."

msgid "t01_pdp_section_7_paragraph_3"
msgstr "Absolutely. We only use sustainably sourced plywood and non-toxic glues and oils. The handles are also rounded to avoid any nasty bumps. The shelf itself is very sturdy and strong, so it won’t sway, sag or topple over. But for added peace of mind, we provide wall mounts so you can attach it firmly to your wall. Because of our furniture’s ergonomic features, our furniture is suitable to be handled by children over the age of 12."

msgid "t01_pdp_section_7_paragraph_4"
msgstr "Try your shelf for 100 days. If for any reason you’re not happy with it, we’ll pick it back up for free and give you a full refund. No questions asked."

msgid "t01_pdp_section_7_paragraph_5"
msgstr "Our three row heights are influenced by the common everyday objects of our customers – things like photo frames, vinyl records, oversized coffee table books and shoeboxes. The three heights (18 cm, 28 cm, 38 cm) have been carefully designed so that no matter how much you adjust or adapt them, they won’t sway or sag under weight."

msgid "t01_pdp_section_7_paragraph_6"
msgstr "One of our guiding principles in design is superior stability – we don’t want any swaying or sagging. We excluded the 24 cm depth from certain products that would be negatively affected stability-wise, such as the TV stand. Also, due to construction limitations, we’re unable to offer drawers with any product using the 24 cm depth."

msgid "t01p_pdp_difference_body_1"
msgstr "We deliver manageable, numbered flat pack boxes right to your doorstep. A personalised manual takes you through the assembly process."

msgid "t01p_pdp_difference_body_2"
msgstr "Each part has colour-coded connectors to make assembly fast. Your shelf is also easy to disassemble so you can take it with you when you move."

msgid "t01p_pdp_difference_body_3"
msgstr "If for any reason you’re not happy with your shelf, we’ll pick it back up for free within 100 days and give you a full refund. No questions asked."

msgid "t01p_pdp_difference_title_1"
msgstr "Delivered in flat-pack boxes"

msgid "t01p_pdp_difference_title_2"
msgstr "Easy click-in assembly"

msgid "t01p_pdp_difference_title_3"
msgstr "100 days to settle in"

msgid "t01p_pdp_product_specs_section_2_description_1"
msgstr "<li>Weighted to self-close</li><li>Sleek extruded aluminium handles </li><li>Pre-mounted click-in assembly elements</li>"

msgid "t01p_pdp_product_specs_section_2_description_2"
msgstr "- Fully extendable runners for deep access<br>- Weighted to self-close<br>- Sleek extruded aluminium handles<br>- Pre-mounted click-in assembly elements<br>"

msgid "t01p_pdp_product_specs_section_2_description_3"
msgstr "<li>Adjustable to fit uneven floors</li>"

msgid "t01p_pdp_product_specs_section_4_description_1_1"
msgstr "Plywood is one of the strongest materials out there. Our premium plywood is made from 13 layers of birchwood, carefully selected for its strength and beauty. We leave the edges exposed and hand-oiled to showcase the natural grain of the wood."

msgid "t01p_pdp_reviews_body_2"
msgstr "based on"

msgid "t01p_pdp_reviews_body_3"
msgstr "reviews"

msgid "t01p_pdp_reviews_header"
msgstr "Customer reviews"

msgid "t01p_pdp_scroll_body_1"
msgstr "Durable and timeless, the Tylko Original Classic in Plywood is a true modern classic <br>that makes organisation simple."

msgid "t01p_pdp_scroll_body_2"
msgstr "A designer's favourite: 13 layers of beautiful <br>hardened birchwood, finished with hand-oiled edges <br>that showcase the natural wood grain."

msgid "t01p_pdp_scroll_body_3"
msgstr "Tight corner or big collection? Use the <br>Tylko configurator to design a shelf that fits<br> your taste and space perfectly."

msgid "t01p_pdp_scroll_body_mobile_2"
msgstr "A designer's favourite: 13 layers of beautiful hardened birchwood, finished with hand-oiled edges that showcase the natural wood grain."

msgid "t01p_pdp_scroll_body_mobile_3"
msgstr "Tight corner or big collection? Use the Tylko configurator to design a shelf that fits your taste and space perfectly."

msgid "t01p_pdp_scroll_header_1"
msgstr "Classic design<br>with an edge"

msgid "t01p_pdp_scroll_header_2"
msgstr "Premium birch plywood"

msgid "t01p_pdp_scroll_header_3"
msgstr "PerfectFit Technology"

msgid "t01p_pdp_usp_body_1"
msgstr "Birchwood core and laminate top, carefully selected to last a lifetime"

msgid "t01p_pdp_usp_body_2"
msgstr "Finished with hand-polished edges and rounded aluminium handles"

msgid "t01p_pdp_usp_body_3"
msgstr "Classic hues that make a statement but will stand the test of time"

msgid "t01p_pdp_usp_title_1"
msgstr "Long-lasting beauty"

msgid "t01p_pdp_usp_title_2"
msgstr "Attention to detail"

msgid "t01p_pdp_usp_title_3"
msgstr "Timeless colours"

msgid "t01v_pdp_difference_body_1"
msgstr "We deliver manageable, numbered flat pack boxes right to your doorstep. A personalised manual takes you through the assembly process."

msgid "t01v_pdp_difference_body_2"
msgstr "Each part has colour-coded connectors to make assembly fast. Your shelf is also easy to disassemble so you can take it with you when you move."

msgid "t01v_pdp_difference_body_3"
msgstr "If for any reason you’re not happy with your shelf, we’ll pick it back up for free within 100 days and give you a full refund. No questions asked."

msgid "t01v_pdp_difference_title_1"
msgstr "Delivered in flat-pack boxes"

msgid "t01v_pdp_difference_title_2"
msgstr "Easy click-in assembly"

msgid "t01v_pdp_difference_title_3"
msgstr "100 days to settle in"

msgid "t01v_pdp_explore_button"
msgstr "Explore"

msgid "t01v_pdp_explore_line"
msgstr "Tylko Original Classic in Plywood"

msgid "t01v_pdp_product_specs_section_2_description_1"
msgstr "<li>Weighted to self-close</li><li>Solid wood handles for a tactile experience</li><li>Pre-mounted click-in assembly elements</li>"

msgid "t01v_pdp_product_specs_section_2_description_2"
msgstr "<li>Fully-extendable runners for deep access</li><li>Weighted to self-close</li><li>Solid wood handles for a tactile experience</li><li>Pre-mounted click-in assembly elements</li>"

msgid "t01v_pdp_product_specs_section_2_description_3"
msgstr "<li>Adjustable to fit uneven floors</li>"

msgid "t01v_pdp_product_specs_section_4_description_1_1"
msgstr "Every Tylko Original Classic in Veneer shelf is made from particle board and covered in a layer of premium-grade natural veneer. Made from oak or ash, it's finished with planet-friendly water-based lacquer and non-toxic glues. Each layer has a one-of-a-kind wood grain that invites touch – a tactile, beautiful detail. "

msgid "t01v_pdp_reviews_body_2"
msgstr "based on"

msgid "t01v_pdp_reviews_body_3"
msgstr "reviews"

msgid "t01v_pdp_reviews_body_4"
msgstr "&nbsp;from&nbsp;"

msgid "t01v_pdp_reviews_header"
msgstr "Customer reviews"

msgid "t01v_pdp_scroll_body_1"
msgstr "Timeless, natural and thoughtfully made: the Tylko Original Classic in Veneer is organisation stripped to the essentials."

msgid "t01v_pdp_scroll_body_2"
msgstr "No fancy colours or flair – just a simple wood<br>grain that’s unique to every shelf with<br>smooth matching handles."

msgid "t01v_pdp_scroll_body_3"
msgstr "Tight corner or big collection? Use the<br>Tylko configurator to design a shelf that fits<br> your taste and space perfectly."

msgid "t01v_pdp_scroll_body_mobile_2"
msgstr "No fancy colours or flair – just a simple wood grain that’s unique to every shelf with smooth matching handles."

msgid "t01v_pdp_scroll_body_mobile_3"
msgstr "Tight corner or big collection? Use the Tylko configurator to design a shelf that fits your taste and space perfectly."

msgid "t01v_pdp_scroll_header_1"
msgstr "A classic – reinvented"

msgid "t01v_pdp_scroll_header_2"
msgstr "Natural wood finish"

msgid "t01v_pdp_scroll_header_3"
msgstr "PerfectFit<br>Technology"

msgid "t01v_pdp_scroll_mobile_body_1"
msgstr "Timeless, natural and thoughtfully made: the Tylko Original Classic in Veneer is organisation stripped to the essentials."

msgid "t01v_pdp_usp_body_1"
msgstr "Premium wood finish with a unique grain, consciously sourced from Europe."

msgid "t01v_pdp_usp_body_2"
msgstr "Solid wood handles designed in-house for a warm and tactile feel."

msgid "t01v_pdp_usp_body_3"
msgstr "Finished with planet-friendly water-based lacquer and toxin-free glues."

msgid "t01v_pdp_usp_title_1"
msgstr "Inspired by nature"

msgid "t01v_pdp_usp_title_2"
msgstr "Quality you can feel"

msgid "t01v_pdp_usp_title_3"
msgstr "Certified safe"

msgid "t02_matte_black_3d_landing_page"
msgstr "This cookie is used for AB testing purposes."

msgid "t03war_pdp_assembly_header_1"
msgstr "Free assembly service"

msgid "t03war_pdp_assembly_header_2"
msgstr "in selected countries"

msgid "t03war_pdp_assembly_tooltip"
msgstr "The Tone Wardrobe comes with a no hassle, free of charge professional assembly service. Our team will get you properly set up in no time, and once the job is done, they will take care of any leftover packages. This service is currently available in selected countries: Germany, The Netherlands, Luxembourg, Austria, Belgium, France, Denmark, Poland, Switzerland, England and Scotland."

msgid "t03war_pdp_delivery_header_2"
msgstr "to your country"

msgid "t03war_pdp_delivery_tooltip"
msgstr "Enjoy a free, smooth delivery right to your doorstep. Your Tone Wardrobe will arrive safely secured in labelled flat pack boxes. We currently ship the Tone Wardrobe to Germany, the Netherlands, Luxembourg, Austria, Belgium, France, Switzerland, Poland, Denmark, England and Scotland."

msgid "t03war_pdp_faq_a1"
msgstr "Try your Tylko for 100 days. If for any reason you’re not happy with it, we’ll pick it back up for free and give you a full refund. No questions asked."

msgid "t03war_pdp_faq_a10"
msgstr "No, it won’t.  If you have already placed an order, your wardrobe will be produced and delivered on schedule. We will keep you updated on the exact delivery date, so stay tuned. "

msgid "t03war_pdp_faq_a11"
msgstr "If you have already added your wardrobe to the cart, you can still complete your order. However, your design will have to stay as it is. If you want to adjust your design, you will have to sign up to the guest list and wait for your chance to order. "

msgid "t03war_pdp_faq_a12"
msgstr "We’ll notify you as soon as your turn comes up. Once you’re notified, you will get a few days to complete your order — if you don’t, we’ll have to pass your spot to the next person in line. Thanks for waiting, we appreciate your support. "

msgid "t03war_pdp_faq_a13"
msgstr "It’s not possible to give an exact timing, as it depends on factors such as the number of people on the waiting list, and the speed of production. We can guarantee you that we are doing our best to get through to your order as fast as possible. "

msgid "t03war_pdp_faq_a14"
msgstr "If you don’t order your wardrobe in the provided time frame, we will have to give your spot to the next person in the queue. However, you can always sign up to the guest list again, by following the same process as before. "

msgid "t03war_pdp_faq_a2"
msgstr "The Tone Wardrobe is currently available to order from Germany, the Netherlands, Luxembourg, Austria, Belgium, France, Switzerland, Poland, Denmark, England and Scotland.."

msgid "t03war_pdp_faq_a3_1"
msgstr "We accept MasterCard and Visa, as well as payments through PayPal. Other payment options depend on the country you're in:  "

msgid "t03war_pdp_faq_a3_2"
msgstr "Germany: International Bank Transfer, SEPA Direct Debit, Klarna "

msgid "t03war_pdp_faq_a3_3"
msgstr "Austria: Bank Transfer, SEPA Direct Debit, Klarna"

msgid "t03war_pdp_faq_a3_5"
msgstr "United Kingdom: MasterCard, Visa, International Bank Transfer, SEPA, PayPal"

msgid "t03war_pdp_faq_a3_6"
msgstr "All transactions (except Klarna) are processed securely via our payment provider Adyen."

msgid "t03war_pdp_faq_a4"
msgstr "We make every piece of furniture with top-quality materials that stand up to tough daily use. For extra peace of mind, we give you a solid 2-year guarantee. For more details and the conditions, please read our <a href=\"https://tylko.com/terms/\">Terms of Service</a>."

msgid "t03war_pdp_faq_a5"
msgstr "We offer sample kits that let you experience our great colours and finishes – perfect if you’re having a hard time deciding! You can order three different sample kits at the bottom of <a href=\"https://tylko.com/product-lines/\">this page</a>. We can also provide the RAL/NCS codes for all of our colours. "

msgid "t03war_pdp_faq_a6"
msgstr "Absolutely. We use emission-free, non-toxic materials, as well as rounded handles. Our soft-close doors and drawers ensure no fingers get caught. The included wall mounts offer extra stability and safety. Because of our furniture’s ergonomic features, our furniture is suitable to be handled by children over the age of 12."

msgid "t03war_pdp_faq_a7"
msgstr "Our design team spent months designing the ideal layout, by testing thousands of options. Based on reseach, they designed 16 layouts to offer the perfect storage solution, that can be very easily customised to fit your needs."

msgid "t03war_pdp_faq_a8"
msgstr "First, measure the space where you want to put your wardrobe. Please be aware of skirting boards and uneven walls that might influence the measurements. When deciding on height, ensure that your measurement is 10cm less than your ceiling height to allow room for assembly. Also make sure you have enough space to open the wardrobe's doors. Once you have all the measurements ready, the fun begins! It helps to make a list of all the things you'll be storing first and then choosing the segments that best fit your needs, making sure you will have enough hanging space, and the right amount of drawers, for example. "

msgid "t03war_pdp_faq_q1"
msgstr "How does the 100-day free return work? "

msgid "t03war_pdp_faq_q10"
msgstr "I’ve already placed an order for the Tone Wardrobe. Will it be affected?"

msgid "t03war_pdp_faq_q11"
msgstr "If I have added my design to the cart but did not complete the order, can I still buy it now?"

msgid "t03war_pdp_faq_q12"
msgstr "What happens when I sign up to the guest list?"

msgid "t03war_pdp_faq_q13"
msgstr "How long will it take to receive the invitation?"

msgid "t03war_pdp_faq_q14"
msgstr "What happens if I don’t place the order in time?"

msgid "t03war_pdp_faq_q2"
msgstr "Which countries can the Tone Wardrobe currently be shipped to?"

msgid "t03war_pdp_faq_q3"
msgstr "What payment options are available?"

msgid "t03war_pdp_faq_q4"
msgstr "Does Tylko furniture come with a guarantee?"

msgid "t03war_pdp_faq_q5"
msgstr "How will I know if the product colour will suit my home? "

msgid "t03war_pdp_faq_q6"
msgstr "Is the Tone Wardrobe safe to place in a child’s room?"

msgid "t03war_pdp_faq_q7"
msgstr "Why I can only pick from pre-defined layouts and not customise them myself?"

msgid "t03war_pdp_faq_q8"
msgstr "What are some tips on designing the perfect wardrobe for me?"

msgid "t03war_pdp_returns_tooltip"
msgstr "If for any reason you’re not happy with your wardrobe, we’ll pick it back up for free within 100 days for a full refund. No questions asked."

msgid "t03war_pdp_segments_description_1"
msgstr "A full-length hanging segment that's ideal for oversized clothing."

msgid "t03war_pdp_segments_description_10"
msgstr "A row of shelves for stacking, plus two drawers to neatly store the smaller items. "

msgid "t03war_pdp_segments_description_11"
msgstr "Two stacking sections and four drawers - perfect for separating by season or specific use."

msgid "t03war_pdp_segments_description_12"
msgstr "Six drawers to keep things tidily tucked, plus handy shelves for extra space to stash. "

msgid "t03war_pdp_segments_description_13"
msgstr "A dedicated space for hanging mid-length items, plus two external drawers for concealed storage."

msgid "t03war_pdp_segments_description_14"
msgstr "Enough space to hang shorter items, plus one internal drawer to conceal small objects, and two external drawers for extra storage."

msgid "t03war_pdp_segments_description_15"
msgstr "Lots of space to stack chunky items like sweaters, one internal drawer for smaller items, plus two external drawers for quick access storage."

msgid "t03war_pdp_segments_description_16"
msgstr "Keep things tidy and concealed, with three internal drawers and 2 external drawers, plus enough space to stack bigger items. "

msgid "t03war_pdp_segments_description_2"
msgstr "Two rows of hanging racks - perfect for keeping shorter-length clothing in order."

msgid "t03war_pdp_segments_description_3"
msgstr "Space to hang mid-length clothes, with room top and bottom for bigger items. "

msgid "t03war_pdp_segments_description_4"
msgstr "A dedicated space for hanging mid-length items, plus two drawers for concealed storage."

msgid "t03war_pdp_segments_description_5"
msgstr "Hanging space for shorter clothes, with open shelves for folding, stacking and storing."

msgid "t03war_pdp_segments_description_6"
msgstr "The do-it-all segment with a hanging rack, two low drawers, and space to stack."

msgid "t03war_pdp_segments_description_7"
msgstr "A short hanging section plus four drawers to keep things tidy and tucked away."

msgid "t03war_pdp_segments_description_8"
msgstr "A row of regular shelves - perfect for boxes, bulkier items and organising clothes in stacks."

msgid "t03war_pdp_segments_description_9"
msgstr "A row of tighter shelves for more specific sorting and smaller organisation."

msgid "t03war_pdp_segments_headline_1"
msgstr "Extra Long Hang"

msgid "t03war_pdp_segments_headline_10"
msgstr "Stack + 2 Drawer"

msgid "t03war_pdp_segments_headline_11"
msgstr "Stack + 4 Drawer"

msgid "t03war_pdp_segments_headline_12"
msgstr "Stack + 6 Drawer"

msgid "t03war_pdp_segments_headline_13"
msgstr "Long Hang + External drawers"

msgid "t03war_pdp_segments_headline_14"
msgstr "Hang + External Drawers"

msgid "t03war_pdp_segments_headline_15"
msgstr "Stack + External Drawers"

msgid "t03war_pdp_segments_headline_16"
msgstr "Internal + External Drawers"

msgid "t03war_pdp_segments_headline_2"
msgstr "Double Hang"

msgid "t03war_pdp_segments_headline_3"
msgstr "Long Hang"

msgid "t03war_pdp_segments_headline_4"
msgstr "Long Hang + 2 Drawer"

msgid "t03war_pdp_segments_headline_5"
msgstr "Hang"

msgid "t03war_pdp_segments_headline_6"
msgstr "Hang + 2 Drawer"

msgid "t03war_pdp_segments_headline_7"
msgstr "Hang + 4 Drawer"

msgid "t03war_pdp_segments_headline_8"
msgstr "Stack"

msgid "t03war_pdp_segments_headline_9"
msgstr "Dense Stack"

msgid "t03war_pdp_specs_body_1_1"
msgstr "Width"

msgid "t03war_pdp_specs_body_1_2"
msgstr "Height"

msgid "t03war_pdp_specs_body_1_3"
msgstr "(Including standard feet)"

msgid "t03war_pdp_specs_body_1_3_1"
msgstr "Please ensure wardrobe is 10cm less than your ceiling height to allow room for assembly."

msgid "t03war_pdp_specs_body_1_4"
msgstr "Depth"

msgid "t03war_pdp_specs_body_1_5"
msgstr "Colour"

msgid "t03war_pdp_specs_body_2_1"
msgstr "Doors"

msgid "t03war_pdp_specs_body_2_10"
msgstr "In all additional openings, such as the overhead compartment and external drawers, we have used the push-to-open system to match the wardrobe's harmonious look and feel. "

msgid "t03war_pdp_specs_body_2_11"
msgstr "External drawers"

msgid "t03war_pdp_specs_body_2_12"
msgstr "- Full-extension, soft-close runners  - Push to open for easy access - The colour of the liner matches the rest of the wardrobe's interior"

msgid "t03war_pdp_specs_body_2_13"
msgstr "Internal drawers"

msgid "t03war_pdp_specs_body_2_14"
msgstr "- Full-extension, soft-close runners - Lowered front panels for easy access - The colour of the liner matches the rest of the wardrobe's interior"

msgid "t03war_pdp_specs_body_2_2"
msgstr "- Built from slim yet sturdy 12mm particle board - Weighted to self-close - Colour-matched aluminum handles with ergonomic design"

msgid "t03war_pdp_specs_body_2_3"
msgstr "Overhead Compartment"

msgid "t03war_pdp_specs_body_2_4"
msgstr "- Same material construction as main wardrobe - Push-to-open doors - Space managing insert shelves"

msgid "t03war_pdp_specs_body_2_5"
msgstr "Hanging Rack"

msgid "t03war_pdp_specs_body_2_6"
msgstr "- Ergonomic height across every segment for comfortable use - Aluminum core for added strength - Powder-coated and colour-matched "

msgid "t03war_pdp_specs_body_2_7"
msgstr "Wardrobe Base"

msgid "t03war_pdp_specs_body_2_8"
msgstr "- Conceals assembly elements for a sleek and seamless finish - Built from durable aluminium - Powder-coated to colour match"

msgid "t03war_pdp_specs_body_2_9"
msgstr "Push-to-open"

msgid "t03war_pdp_specs_body_3_1"
msgstr "Construction Material"

msgid "t03war_pdp_specs_body_3_2"
msgstr "The Tylko Wardrobe is made of durable high-density particle board with a laminated top finish. The Wardrobe core elements are constructed from 18mm particle board, while the doors and frames are made from a slightly thinner 12mm board for a streamlined look with no compromise on stability or strength. The handles and hanging rack are fabricated in durable aluminum that's powder-coated and colour-matched to perfectly suit your storage."

msgid "t03war_pdp_specs_body_3_3"
msgstr "Matt and scratch-free finish"

msgid "t03war_pdp_specs_body_3_4"
msgstr "The wardrobe boasts a premium look and feel, with a smooth matt finish, resistant to fingerprints and scratches. "

msgid "t03war_pdp_specs_body_3_5"
msgstr "Care Instructions"

msgid "t03war_pdp_specs_body_3_6"
msgstr "Use gentle furniture cleaners and a dry towel to keep your Wardrobe looking great."

msgid "t03war_pdp_specs_body_4_1"
msgstr "Assembly"

msgid "t03war_pdp_specs_body_4_2"
msgstr "The Tone Wardrobe comes with a free assembly service. Our team of professionals will quickly assemble your wardrobe on the spot, and will secure it to the wall. Don't worry about having enough space: the wardrobe can be assembled vertically. And once the job is complete, all delivery boxes will be taken care of. Easy, right?"

msgid "t03war_pdp_specs_body_4_3"
msgstr "Delivery"

msgid "t03war_pdp_specs_body_4_4"
msgstr "Delivery is also free. We pack all elements safely to ensure your wardrobe arrives in perfect condition."

msgid "t03war_pdp_specs_headline_1"
msgstr "Your Tylko Wardrobe"

msgid "t03war_pdp_specs_headline_2"
msgstr "Features"

msgid "t03war_pdp_specs_headline_3"
msgstr "Materials & Quality"

msgid "t03war_pdp_specs_headline_4"
msgstr "Assembly and delivery"

msgid "three_seater_pdp_name"
msgstr "three_seater"

#, python-format
msgid "total_vat_included_%(amount)s"
msgstr "Total <br>( VAT Included: %(amount)s )"

msgid "trustedshop_text_icon"
msgstr "Buyer Protection, Data Protection, <br class=\"tablet-visible\">Secure Payment"

msgid "two_seater_pdp_name"
msgstr "two_seater"

msgid "ty-cookies"
msgstr "Cookie to keep given User Consents."

msgid "tylko_bookcase"
msgstr "Tylko Bookcase"

msgid "tylko_chest_of_drawers"
msgstr "Tylko Chest of Drawers"

msgid "tylko_hp_title_1"
msgstr "Tylko: create the ideal shelves and wardrobes for your home"

msgid "tylko_sideboard"
msgstr "Tylko Sideboard"

msgid "tylko_story_buton_1"
msgstr "Get to know Tylko"

msgid "tylko_story_buton_2"
msgstr "Shop the shelf"

msgid "tylko_story_header_1_1"
msgstr "You know what your<br/>home needs."

msgid "tylko_story_header_1_2"
msgstr "We don’t pretend to."

msgid "tylko_story_header_2_1"
msgstr "Say goodbye to standardized furniture"

msgid "tylko_story_header_2_2"
msgstr "We respect your time"

msgid "tylko_story_header_2_3"
msgstr "Sustainable means long-lasting"

msgid "tylko_story_header_2_4"
msgstr "Why? Because we care"

msgid "tylko_story_header_2_6"
msgstr "Where furniture meets the future"

msgid "tylko_story_paragraph_1_1"
msgstr "Every home is different. At Tylko, we believe that yours should be filled with perfect-fit, functional furniture that happens to look great."

msgid "tylko_story_paragraph_2_1"
msgstr "Work-Life Balance. These aren’t empty words for us at Tylko. We make your life easier by delivering the perfect shelf, right to your door."

msgid "tylko_story_paragraph_3_1"
msgstr "Our furniture, produced from slow-growth wood from European birch forests, is so durable it will outlive the trees planted to replace it."

msgid "tylko_story_paragraph_4_1"
msgstr "From meeting your needs, your ease and comfort through the process of furnishing your home and about future generations."

msgid "tylko_story_paragraph_6_1"
msgstr "Our augmented reality app lets you see your furniture in your space before you buy."

msgid "tylko_story_preheader_1"
msgstr "Prologue: Our Mission"

msgid "tylko_tv_stand"
msgstr "Tylko TV Stand"

msgid "tylko_wardrobe"
msgstr "Tylko Wardrobe"

msgid "type03_wardrobe_sidecart_name"
msgstr "Tone Wardrobe"

msgid "ugc_body"
msgstr "You can shop your favourite look right here, right now by clicking on the image. Feel like inspiring our community too? Tag your #mytylko moments for a chance to get featured on our channels.  "

msgid "ugc_headline"
msgstr "Shop the look"

msgid "ugc_title"
msgstr "Get inspired by our community"

msgid "unitedkingdom"
msgstr "United Kingdom"

msgid "unsubscription_subpage_confirmation_header_1"
msgstr "You have been successfully unsubscribed."

msgid "unsubscription_subpage_error_header_1"
msgstr "Oops, for some reason your automatic request didn't go through. Send us a quick <NAME_EMAIL> and we'll manually remove you from the mailing list."

msgid "url.lp.bedroom"
msgstr "/rooms/bedroom/"

msgid "url.lp.hallway"
msgstr "/rooms/hallway/"

msgid "url.lp.kidsroom"
msgstr "/rooms/kids-room/"

msgid "url.lp.livingroom"
msgstr "/rooms/living-room/"

msgid "url.lp.rooms"
msgstr "/rooms/"

msgid "url.lp.studiooffice"
msgstr "/rooms/studio-office/"

msgid "userGaId"
msgstr "This cookie carries the Google Analytics User ID."

msgid "usps_samples_return_policy"
msgstr "This 100-day return policy excludes Tylko sample sets."

msgid "veneer_material_ash"
msgstr "White Oak"

msgid "veneer_material_oak"
msgstr "Oak"

msgid "veneer_shelf_ash"
msgstr "White Oak"

msgid "veneer_shelf_dark_oak"
msgstr "Walnut"

msgid "veneer_shelf_name"
msgstr "Tylko Original Classic in Veneer"

msgid "veneer_shelf_oak"
msgstr "Oak Veneer"

msgid "veneer_watty_dark"
msgstr "Dark wood effect"

msgid "veneer_watty_light"
msgstr "Light wood effect"

msgid "vueCheckout_sku_heading"
msgstr "Ships in 3 working days"

msgid "vueCheckout_sku_heading_html"
msgstr "Ships in 3 working days"

msgid "vueCheckout_sku_tooltip"
msgstr "Shorter delivery times are only applicable if your order includes quick production products only (Tylko Original Modern designs in White, Sand + Midnight Blue, plus Terracotta). If your basket includes other items, the shipping time for your entire order is standard."

msgid "vueCheckout_sku_tooltip_html"
msgstr "Shorter delivery times are only applicable if your order includes quick production products only (Tylko Original Modern designs in White, Sand + Midnight Blue, plus Terracotta). If your basket includes other items, the shipping time for your entire order is standard."

msgid "vueContact_addreview_page_button_photo"
msgstr "Choose photo"

msgid "vueContact_addreview_page_header2_photo"
msgstr "vueContact_addreview_page_header2_photo"

msgid "vueFaq_ola_faq"
msgstr "vueFaq_ola_faq"

msgid "vueFaq_ola_faq_brexit_answer_1"
msgstr "vueFaq_ola_faq_brexit_answer_1"

msgid "vueFaq_ola_faq_brexit_answer_2"
msgstr "vueFaq_ola_faq_brexit_answer_2"

msgid "vueFaq_ola_faq_brexit_answer_3"
msgstr "vueFaq_ola_faq_brexit_answer_3"

msgid "vueFaq_ola_faq_brexit_answer_4"
msgstr "vueFaq_ola_faq_brexit_answer_4"

msgid "vueFaq_ola_faq_brexit_header"
msgstr "vueFaq_ola_faq_brexit_header"

msgid "vueFaq_ola_faq_brexit_question_1"
msgstr "vueFaq_ola_faq_brexit_question_1"

msgid "vueFaq_ola_faq_brexit_question_2"
msgstr "vueFaq_ola_faq_brexit_question_2"

msgid "vueFaq_ola_faq_brexit_question_3"
msgstr "vueFaq_ola_faq_brexit_question_3"

msgid "vueFaq_ola_faq_brexit_question_4"
msgstr "vueFaq_ola_faq_brexit_question_4"

msgid "vueFaq_ola_faq_covid_answer_1"
msgstr "vueFaq_ola_faq_covid_answer_1"

msgid "vueFaq_ola_faq_covid_answer_2"
msgstr "vueFaq_ola_faq_covid_answer_2"

msgid "vueFaq_ola_faq_covid_answer_3"
msgstr "vueFaq_ola_faq_covid_answer_3"

msgid "vueFaq_ola_faq_covid_answer_4"
msgstr "vueFaq_ola_faq_covid_answer_4"

msgid "vueFaq_ola_faq_covid_header"
msgstr "vueFaq_ola_faq_covid_header"

msgid "vueFaq_ola_faq_covid_question_1"
msgstr "vueFaq_ola_faq_covid_question_1"

msgid "vueFaq_ola_faq_covid_question_2"
msgstr "vueFaq_ola_faq_covid_question_2"

msgid "vueFaq_ola_faq_covid_question_3"
msgstr "vueFaq_ola_faq_covid_question_3"

msgid "vueFaq_ola_faq_covid_question_4"
msgstr "vueFaq_ola_faq_covid_question_4"

msgid "vueFaq_ola_faq_header_1"
msgstr "vueFaq_ola_faq_header_1"

msgid "vueFaq_ola_faq_header_1_answer_1"
msgstr "vueFaq_ola_faq_header_1_answer_1"

msgid "vueFaq_ola_faq_header_1_answer_1_2"
msgstr "vueFaq_ola_faq_header_1_answer_1_2"

msgid "vueFaq_ola_faq_header_1_answer_2"
msgstr "vueFaq_ola_faq_header_1_answer_2"

msgid "vueFaq_ola_faq_header_1_answer_3"
msgstr "vueFaq_ola_faq_header_1_answer_3"

msgid "vueFaq_ola_faq_header_1_answer_4"
msgstr "vueFaq_ola_faq_header_1_answer_4"

msgid "vueFaq_ola_faq_header_1_question_1"
msgstr "vueFaq_ola_faq_header_1_question_1"

msgid "vueFaq_ola_faq_header_1_question_2"
msgstr "vueFaq_ola_faq_header_1_question_2"

msgid "vueFaq_ola_faq_header_1_question_3"
msgstr "vueFaq_ola_faq_header_1_question_3"

msgid "vueFaq_ola_faq_header_1_question_4"
msgstr "vueFaq_ola_faq_header_1_question_4"

msgid "vueFaq_ola_faq_header_2"
msgstr "vueFaq_ola_faq_header_2"

msgid "vueFaq_ola_faq_header_2_answer_1"
msgstr "vueFaq_ola_faq_header_2_answer_1"

msgid "vueFaq_ola_faq_header_2_answer_2"
msgstr "vueFaq_ola_faq_header_2_answer_2"

msgid "vueFaq_ola_faq_header_2_answer_3"
msgstr "vueFaq_ola_faq_header_2_answer_3"

msgid "vueFaq_ola_faq_header_2_answer_4"
msgstr "vueFaq_ola_faq_header_2_answer_4"

msgid "vueFaq_ola_faq_header_2_answer_5"
msgstr "vueFaq_ola_faq_header_2_answer_5"

msgid "vueFaq_ola_faq_header_2_question_1"
msgstr "vueFaq_ola_faq_header_2_question_1"

msgid "vueFaq_ola_faq_header_2_question_2"
msgstr "vueFaq_ola_faq_header_2_question_2"

msgid "vueFaq_ola_faq_header_2_question_3"
msgstr "vueFaq_ola_faq_header_2_question_3"

msgid "vueFaq_ola_faq_header_2_question_4"
msgstr "vueFaq_ola_faq_header_2_question_4"

msgid "vueFaq_ola_faq_header_2_question_5"
msgstr "vueFaq_ola_faq_header_2_question_5"

msgid "vueFaq_ola_faq_header_2_url"
msgstr "vueFaq_ola_faq_header_2_url"

msgid "vueFaq_ola_faq_header_3"
msgstr "vueFaq_ola_faq_header_3"

msgid "vueFaq_ola_faq_header_3_answer_1"
msgstr "vueFaq_ola_faq_header_3_answer_1"

msgid "vueFaq_ola_faq_header_3_answer_2"
msgstr "vueFaq_ola_faq_header_3_answer_2"

msgid "vueFaq_ola_faq_header_3_answer_3"
msgstr "vueFaq_ola_faq_header_3_answer_3"

msgid "vueFaq_ola_faq_header_3_question_1"
msgstr "vueFaq_ola_faq_header_3_question_1"

msgid "vueFaq_ola_faq_header_3_question_2"
msgstr "vueFaq_ola_faq_header_3_question_2"

msgid "vueFaq_ola_faq_header_3_question_3"
msgstr "vueFaq_ola_faq_header_3_question_3"

msgid "vueFaq_ola_faq_header_3_url"
msgstr "vueFaq_ola_faq_header_3_url"

msgid "vueFaq_ola_faq_header_4"
msgstr "vueFaq_ola_faq_header_4"

msgid "vueFaq_ola_faq_header_4_answer_1"
msgstr "vueFaq_ola_faq_header_4_answer_1"

msgid "vueFaq_ola_faq_header_4_answer_2.0"
msgstr "vueFaq_ola_faq_header_4_answer_2.0"

msgid "vueFaq_ola_faq_header_4_answer_2.1"
msgstr "vueFaq_ola_faq_header_4_answer_2.1"

msgid "vueFaq_ola_faq_header_4_answer_2.2"
msgstr "vueFaq_ola_faq_header_4_answer_2.2"

msgid "vueFaq_ola_faq_header_4_answer_2.3"
msgstr "vueFaq_ola_faq_header_4_answer_2.3"

msgid "vueFaq_ola_faq_header_4_answer_2.4"
msgstr "vueFaq_ola_faq_header_4_answer_2.4"

msgid "vueFaq_ola_faq_header_4_answer_2.5"
msgstr "vueFaq_ola_faq_header_4_answer_2.5"

msgid "vueFaq_ola_faq_header_4_answer_3"
msgstr "vueFaq_ola_faq_header_4_answer_3"

msgid "vueFaq_ola_faq_header_4_answer_4"
msgstr "vueFaq_ola_faq_header_4_answer_4"

msgid "vueFaq_ola_faq_header_4_question_1"
msgstr "vueFaq_ola_faq_header_4_question_1"

msgid "vueFaq_ola_faq_header_4_question_2"
msgstr "vueFaq_ola_faq_header_4_question_2"

msgid "vueFaq_ola_faq_header_4_question_3"
msgstr "vueFaq_ola_faq_header_4_question_3"

msgid "vueFaq_ola_faq_header_4_question_4"
msgstr "vueFaq_ola_faq_header_4_question_4"

msgid "vueFaq_ola_faq_header_4_url"
msgstr "vueFaq_ola_faq_header_4_url"

msgid "vueFaq_ola_faq_header_5"
msgstr "vueFaq_ola_faq_header_5"

msgid "vueFaq_ola_faq_header_5_answer_1"
msgstr "vueFaq_ola_faq_header_5_answer_1"

msgid "vueFaq_ola_faq_header_5_answer_3"
msgstr "vueFaq_ola_faq_header_5_answer_3"

msgid "vueFaq_ola_faq_header_5_question_1"
msgstr "vueFaq_ola_faq_header_5_question_1"

msgid "vueFaq_ola_faq_header_5_question_2"
msgstr "vueFaq_ola_faq_header_5_question_2"

msgid "vueFaq_ola_faq_header_5_question_3"
msgstr "vueFaq_ola_faq_header_5_question_3"

msgid "vueFaq_ola_faq_header_5_url"
msgstr "vueFaq_ola_faq_header_5_url"

msgid "vueFaq_ola_faq_header_6"
msgstr "vueFaq_ola_faq_header_6"

msgid "vueFaq_ola_faq_header_6_answer_1"
msgstr "vueFaq_ola_faq_header_6_answer_1"

msgid "vueFaq_ola_faq_header_6_answer_2"
msgstr "vueFaq_ola_faq_header_6_answer_2"

msgid "vueFaq_ola_faq_header_6_answer_3"
msgstr "vueFaq_ola_faq_header_6_answer_3"

msgid "vueFaq_ola_faq_header_6_answer_4"
msgstr "vueFaq_ola_faq_header_6_answer_4"

msgid "vueFaq_ola_faq_header_6_answer_5"
msgstr "vueFaq_ola_faq_header_6_answer_5"

msgid "vueFaq_ola_faq_header_6_answer_6"
msgstr "vueFaq_ola_faq_header_6_answer_6"

msgid "vueFaq_ola_faq_header_6_question_1"
msgstr "vueFaq_ola_faq_header_6_question_1"

msgid "vueFaq_ola_faq_header_6_question_2"
msgstr "vueFaq_ola_faq_header_6_question_2"

msgid "vueFaq_ola_faq_header_6_question_3"
msgstr "vueFaq_ola_faq_header_6_question_3"

msgid "vueFaq_ola_faq_header_6_question_4"
msgstr "vueFaq_ola_faq_header_6_question_4"

msgid "vueFaq_ola_faq_header_6_question_5"
msgstr "vueFaq_ola_faq_header_6_question_5"

msgid "vueFaq_ola_faq_header_6_question_6"
msgstr "vueFaq_ola_faq_header_6_question_6"

msgid "vueFaq_ola_faq_header_7"
msgstr "vueFaq_ola_faq_header_7"

msgid "vueFaq_ola_faq_header_7_answer_1"
msgstr "vueFaq_ola_faq_header_7_answer_1"

msgid "vueFaq_ola_faq_header_7_answer_2"
msgstr "vueFaq_ola_faq_header_7_answer_2"

msgid "vueFaq_ola_faq_header_7_answer_3"
msgstr "vueFaq_ola_faq_header_7_answer_3"

msgid "vueFaq_ola_faq_header_7_answer_4"
msgstr "vueFaq_ola_faq_header_7_answer_4"

msgid "vueFaq_ola_faq_header_7_answer_5"
msgstr "vueFaq_ola_faq_header_7_answer_5"

msgid "vueFaq_ola_faq_header_7_answer_6"
msgstr "vueFaq_ola_faq_header_7_answer_6"

msgid "vueFaq_ola_faq_header_7_question_1"
msgstr "vueFaq_ola_faq_header_7_question_1"

msgid "vueFaq_ola_faq_header_7_question_2"
msgstr "vueFaq_ola_faq_header_7_question_2"

msgid "vueFaq_ola_faq_header_7_question_3"
msgstr "vueFaq_ola_faq_header_7_question_3"

msgid "vueFaq_ola_faq_header_7_question_4"
msgstr "vueFaq_ola_faq_header_7_question_4"

msgid "vueFaq_ola_faq_header_7_question_5"
msgstr "vueFaq_ola_faq_header_7_question_5"

msgid "vueFaq_ola_faq_header_7_question_6"
msgstr "vueFaq_ola_faq_header_7_question_6"

msgid "vueFaq_ola_faq_header_7_url"
msgstr "vueFaq_ola_faq_header_7_url"

msgid "vueFaq_ola_faq_header_bf"
msgstr "vueFaq_ola_faq_header_bf"

msgid "vueFaq_ola_faq_header_bf_answer_1"
msgstr "vueFaq_ola_faq_header_bf_answer_1"

msgid "vueFaq_ola_faq_header_bf_answer_2"
msgstr "vueFaq_ola_faq_header_bf_answer_2"

msgid "vueFaq_ola_faq_header_bf_answer_3"
msgstr "vueFaq_ola_faq_header_bf_answer_3"

msgid "vueFaq_ola_faq_header_bf_question_1"
msgstr "vueFaq_ola_faq_header_bf_question_1"

msgid "vueFaq_ola_faq_header_bf_question_2"
msgstr "vueFaq_ola_faq_header_bf_question_2"

msgid "vueFaq_ola_faq_header_bf_question_3"
msgstr "vueFaq_ola_faq_header_bf_question_3"

msgid "vueFaq_ola_faq_header_cm"
msgstr "vueFaq_ola_faq_header_cm"

msgid "vueFaq_ola_faq_header_cm_answer_1"
msgstr "vueFaq_ola_faq_header_cm_answer_1"

msgid "vueFaq_ola_faq_header_cm_answer_2"
msgstr "vueFaq_ola_faq_header_cm_answer_2"

msgid "vueFaq_ola_faq_header_cm_answer_3"
msgstr "vueFaq_ola_faq_header_cm_answer_3"

msgid "vueFaq_ola_faq_header_cm_question_1"
msgstr "vueFaq_ola_faq_header_cm_question_1"

msgid "vueFaq_ola_faq_header_cm_question_2"
msgstr "vueFaq_ola_faq_header_cm_question_2"

msgid "vueFaq_ola_faq_header_cm_question_3"
msgstr "vueFaq_ola_faq_header_cm_question_3"

msgid "vueGrid_new_label"
msgstr "New"

msgid "vueGrid_soon_label"
msgstr "Soon"

msgid "waitlist_mail_1_copy_body"
msgstr "Thanks for joining the Tone Wardrobe <strong>guest list</strong>. <br><br>Due to high demand, we’re not able to accept more orders at the moment. Each one of our products is custom-made with care at our local factories, which means we don’t have ready-made&nbsp;furniture in stock. <br><br>When we’re ready to produce your Tone Wardrobe, we’ll&nbsp;let you know immediately by sending you an&nbsp;invite to place your order. Once you receive the&nbsp;invite, you’ll have a set amount of time to place your&nbsp;order. <br><br>Here’s your  <strong>unique design</strong>:"

msgid "waitlist_mail_1_copy_footer"
msgstr "And feel free to explore what makes the Tone Wardrobe worth the wait "

msgid "waitlist_mail_1_copy_footer_2"
msgstr ". <br><br>Thanks for understanding.<br>We’ll be in touch soon!<br><br>Take care, <br>The Tylko Team"

msgid "waitlist_mail_1_copy_invitation"
msgstr "Hi there"

msgid "waitlist_mail_1_link"
msgstr "here"

msgid "waitlist_mail_1_preheader"
msgstr "Welcome to the Wardrobe guest list."

msgid "waitlist_mail_1_subject"
msgstr "You’re on the list 😎"

msgid "waitlist_mail_2a_button"
msgstr "Edit My Design"

#, python-format
msgid "waitlist_mail_2a_copy_body_%(date)s"
msgstr "As they say, good things come to those who wait. The Tone Wardrobe you designed is <strong>now available to order</strong>, hurrah!<br><br>You have until %(date)s to place your order, or else we’ll have to offer someone else your production spot. <br><br><strong>Here’s the Wardrobe you designed:</strong>"

msgid "waitlist_mail_2a_copy_edit"
msgstr "Feel free to tweak it, <br>or head straight to checkout."

#, python-format
msgid "waitlist_mail_2a_copy_footer_%(discount)s"
msgstr "Don’t forget - the <strong>promocode</strong> you received when joining the guest list is still valid. Use it at checkout to <strong>save %(discount)s</strong> on your Tone Wardrobe."

msgid "waitlist_mail_2a_preheader"
msgstr "It’s time to get your Tylko."

msgid "waitlist_mail_2a_signature"
msgstr "Have a good time, <br>The Tylko Team"

msgid "waitlist_mail_2a_subject"
msgstr "Good news! You can order your wardrobe."

msgid "waitlist_mail_2b_copy_body"
msgstr "As they say, good things come to those who wait. The Tone Wardrobe you designed is <strong>now available to order</strong>, hurrah!<br><br><strong>Here’s your design:</strong>"

#, python-format
msgid "waitlist_mail_2b_copy_body_%(discount)s"
msgstr "Don’t forget - the <strong>promocode</strong> that you received when joining the guest list is still valid. Use it at checkout to <strong>save %(discount)s</strong> on your Tone Wardrobe."

msgid "waitlist_mail_2b_copy_edit"
msgstr "Feel free to tweak your design, or head straight to checkout whenever you’re ready."

msgid "waitlist_mail_2b_preheader"
msgstr "The wait is over: go get your Tylko."

msgid "waitlist_mail_2b_signature"
msgstr "Have a good time, <br>The Tylko Team"

msgid "waitlist_mail_2b_subject"
msgstr "Order your Tone Wardrobe today."

msgid "wardrobe_promo_cta"
msgstr "Shop Now"

msgid "wardrobe_promo_header"
msgstr "Save 10% on every Wardrobe"

msgid "wardrobe_promo_header_text"
msgstr "Take 10% off any Wardrobe and enjoy free shipping and expert assembly service, too! Code: SCHRANK10, valid through 28.02"

msgid "wardrobe_sidecart_name"
msgstr "Tylko Wardrobe"

msgid "watty_beige"
msgstr "Cashmere Beige"

msgid "watty_beige_feminine"
msgstr "watty_beige_feminine"

msgid "watty_beige_graphite_feminine"
msgstr "watty_beige_graphite_feminine"

msgid "watty_beige_graphite_masculine"
msgstr "watty_beige_graphite_masculine"

msgid "watty_beige_graphite_neuter"
msgstr "watty_beige_graphite_neuter"

msgid "watty_beige_masculine"
msgstr "watty_beige_masculine"

msgid "watty_beige_neuter"
msgstr "watty_beige_neuter"

msgid "watty_beige_pink"
msgstr "Cashmere Beige + Antique Pink"

msgid "watty_beige_pink_feminine"
msgstr "watty_beige_pink_feminine"

msgid "watty_beige_pink_masculine"
msgstr "watty_beige_pink_masculine"

msgid "watty_beige_pink_neuter"
msgstr "watty_beige_pink_neuter"

msgid "watty_beige_white_feminine"
msgstr "watty_beige_white_feminine"

msgid "watty_beige_white_masculine"
msgstr "watty_beige_white_masculine"

msgid "watty_beige_white_neuter"
msgstr "watty_beige_white_neuter"

msgid "watty_cashmere_misty_blue"
msgstr "Cashmere and Blue"

msgid "watty_cashmere_misty_blue_feminine"
msgstr "watty_cashmere_misty_blue_feminine"

msgid "watty_cashmere_misty_blue_masculine"
msgstr "watty_cashmere_misty_blue_masculine"

msgid "watty_cashmere_misty_blue_neuter"
msgstr "watty_cashmere_misty_blue_neuter"

msgid "watty_cashmere_sage_green"
msgstr "Cashmere and Green"

msgid "watty_cashmere_sage_green_feminine"
msgstr "watty_cashmere_sage_green_feminine"

msgid "watty_cashmere_sage_green_masculine"
msgstr "watty_cashmere_sage_green_masculine"

msgid "watty_cashmere_sage_green_neuter"
msgstr "watty_cashmere_sage_green_neuter"

msgid "watty_cashmere_stone_gray"
msgstr "Cashmere and Gray"

msgid "watty_cashmere_stone_gray_feminine"
msgstr "watty_cashmere_stone_gray_feminine"

msgid "watty_cashmere_stone_gray_masculine"
msgstr "watty_cashmere_stone_gray_masculine"

msgid "watty_cashmere_stone_gray_neuter"
msgstr "watty_cashmere_stone_gray_neuter"

msgid "watty_graphite"
msgstr "Graphite Grey"

msgid "watty_graphite_beige_feminine"
msgstr "watty_graphite_beige_feminine"

msgid "watty_graphite_beige_masculine"
msgstr "watty_graphite_beige_masculine"

msgid "watty_graphite_beige_neuter"
msgstr "watty_graphite_beige_neuter"

msgid "watty_graphite_feminine"
msgstr "watty_graphite_feminine"

msgid "watty_graphite_masculine"
msgstr "watty_graphite_masculine"

msgid "watty_graphite_misty_blue"
msgstr "Gray and Blue"

msgid "watty_graphite_misty_blue_feminine"
msgstr "watty_graphite_misty_blue_feminine"

msgid "watty_graphite_misty_blue_masculine"
msgstr "watty_graphite_misty_blue_masculine"

msgid "watty_graphite_misty_blue_neuter"
msgstr "watty_graphite_misty_blue_neuter"

msgid "watty_graphite_neuter"
msgstr "watty_graphite_neuter"

msgid "watty_graphite_pink"
msgstr "Graphite Grey + Antique Pink"

msgid "watty_graphite_pink_feminine"
msgstr "watty_graphite_pink_feminine"

msgid "watty_graphite_pink_masculine"
msgstr "watty_graphite_pink_masculine"

msgid "watty_graphite_pink_neuter"
msgstr "watty_graphite_pink_neuter"

msgid "watty_graphite_sage_green"
msgstr "Gray and Green"

msgid "watty_graphite_sage_green_feminine"
msgstr "watty_graphite_sage_green_feminine"

msgid "watty_graphite_sage_green_masculine"
msgstr "watty_graphite_sage_green_masculine"

msgid "watty_graphite_sage_green_neuter"
msgstr "watty_graphite_sage_green_neuter"

msgid "watty_graphite_stone_gray"
msgstr "Graphite and Gray"

msgid "watty_graphite_stone_gray_feminine"
msgstr "watty_graphite_stone_gray_feminine"

msgid "watty_graphite_stone_gray_masculine"
msgstr "watty_graphite_stone_gray_masculine"

msgid "watty_graphite_stone_gray_neuter"
msgstr "watty_graphite_stone_gray_neuter"

msgid "watty_graphite_white_feminine"
msgstr "watty_graphite_white_feminine"

msgid "watty_graphite_white_masculine"
msgstr "watty_graphite_white_masculine"

msgid "watty_graphite_white_neuter"
msgstr "watty_graphite_white_neuter"

msgid "watty_white"
msgstr "White"

msgid "watty_white_beige_feminine"
msgstr "watty_white_beige_feminine"

msgid "watty_white_beige_masculine"
msgstr "watty_white_beige_masculine"

msgid "watty_white_beige_neuter"
msgstr "watty_white_beige_neuter"

msgid "watty_white_feminine"
msgstr "watty_white_feminine"

msgid "watty_white_graphite_feminine"
msgstr "watty_white_graphite_feminine"

msgid "watty_white_graphite_masculine"
msgstr "watty_white_graphite_masculine"

msgid "watty_white_graphite_neuter"
msgstr "watty_white_graphite_neuter"

msgid "watty_white_masculine"
msgstr "watty_white_masculine"

msgid "watty_white_misty_blue"
msgstr "White and Blue"

msgid "watty_white_misty_blue_feminine"
msgstr "watty_white_misty_blue_feminine"

msgid "watty_white_misty_blue_masculine"
msgstr "watty_white_misty_blue_masculine"

msgid "watty_white_misty_blue_neuter"
msgstr "watty_white_misty_blue_neuter"

msgid "watty_white_neuter"
msgstr "watty_white_neuter"

msgid "watty_white_pink"
msgstr "White + Antique Pink"

msgid "watty_white_pink_feminine"
msgstr "watty_white_pink_feminine"

msgid "watty_white_pink_masculine"
msgstr "watty_white_pink_masculine"

msgid "watty_white_pink_neuter"
msgstr "watty_white_pink_neuter"

msgid "watty_white_sage_green"
msgstr "White and Green"

msgid "watty_white_sage_green_feminine"
msgstr "watty_white_sage_green_feminine"

msgid "watty_white_sage_green_masculine"
msgstr "watty_white_sage_green_masculine"

msgid "watty_white_sage_green_neuter"
msgstr "watty_white_sage_green_neuter"

msgid "watty_white_stone_gray"
msgstr "White and Gray"

msgid "watty_white_stone_gray_feminine"
msgstr "watty_white_stone_gray_feminine"

msgid "watty_white_stone_gray_masculine"
msgstr "watty_white_stone_gray_masculine"

msgid "watty_white_stone_gray_neuter"
msgstr "watty_white_stone_gray_neuter"

msgid "web_checkout_title_payment_method"
msgstr "Payment Method"

msgid "webglbug.popup1"
msgstr "We've detected that your device's operating system has a known bug that affects our configurator. What can you do?"

msgid "webglbug.popup2"
msgstr "Quick fix: restart your browser. This will allow you to continue, but the problem may happen again in the future. Discover more →"

msgid "webglbug.popup3"
msgstr "Recommended: upgrade your iOS to the latest version for the best experience. See how →"

msgid "webglbug.popup4"
msgstr "Thank you for your understanding and patience. \nAny problems? We're here to help."

#: frontend_cms/templates/front/contest.html frontend_cms/templates/front/pdp.html
msgid "width"
msgstr "Width"

msgid "wishlistpage_empty"
msgstr "There are no items in your Wishlist yet. You can add designs to your Wishlist and access them from any device when logged in."

msgid "wood_finish"
msgstr "Wood finish"

msgid "zuza_hp_carousel_body_1"
msgstr "Sideboards"

msgid "zuza_hp_carousel_body_2"
msgstr "Bookcases"

msgid "zuza_hp_carousel_body_3"
msgstr "Wardrobes"

msgid "zuza_hp_carousel_body_4"
msgstr "TV Stands"

msgid "zuza_hp_carousel_body_5"
msgstr "Chest of Drawers"

msgid "zuza_hp_carousel_buton_1"
msgstr "Explore full collection"

msgid "zuza_hp_carousel_header_1"
msgstr "Find the one"

msgid "zuza_hp_carousel_header_2"
msgstr "Step into our world"

msgid "zuza_hp_carousel_soon_badge"
msgstr "Soon"

msgid "zuza_hp_categories_buton_1"
msgstr "See all categories"

msgid "zuza_hp_categories_header_1"
msgstr "Explore the collections"

msgid "zuza_hp_hero_buton_1"
msgstr "Design yours"

msgid "zuza_hp_hero_header_1b"
msgstr "Modern storage for better living"

msgid "zuza_hp_relax_buton_1"
msgstr "Shop the collection"

msgid "zuza_hp_usp_description_1"
msgstr "Adjust your furniture to fit your every need, including style, size and colour."

msgid "zuza_hp_usp_description_2"
msgstr "We're not just good looks. All our products are made with premium, consciously-sourced material, so rest assured they will last you a very, very long time. "

msgid "zuza_hp_usp_description_3"
msgstr "Each of our products was thoughtfully designed by experts to ensure a quick assembly. "

msgid "zuza_hp_usp_header_2_1"
msgstr "Personalised design"

msgid "zuza_hp_usp_header_2_2"
msgstr "Built to last"

msgid "zuza_hp_usp_header_2_3"
msgstr "Simple assembly"
