from datetime import datetime

from django.contrib import (
    admin,
    messages,
)
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html

import pandas as pd

from custom.admin_action import admin_action_with_form
from custom.admin_permissions import group_member_required
from custom.gemini import GeminiClient
from custom.utils.report_file import ReportFile
from dixa.forms import AnalyzeConversationsForm
from dixa.models import (
    AnalysisOutputField,
    ChannelMapping,
    ConversationAnalysisResult,
    ConversationForAI,
    DailyUsage,
    MessageForAI,
    SystemInstruction,
    WhatsappFile,
)
from dixa.services.conversation_analyzer_ai import analyze_conversation


class ChannelMappingAdmin(admin.ModelAdmin):
    list_display = (
        'slack_channel_name',
        'slack_channel_id',
        'dixa_queue_name',
        'dixa_queue_id',
    )
    search_fields = (
        'slack_channel_name',
        'slack_channel_id',
        'dixa_queue_name',
        'dixa_queue_id',
    )


class WhatsappFileAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'file',
        'url',
        'created_at',
    )
    search_fields = ('id', '^file')

    @admin.display(description='URL')
    def url(self, obj: WhatsappFile):
        target_url = reverse('whatsapp_files-detail', kwargs={'pk': str(obj.pk)})
        return format_html(
            '<a href="{}" target="_blank">{}</a>',
            target_url,
            target_url,
        )


class MessageForAIInline(admin.TabularInline):
    model = MessageForAI
    extra = 0
    readonly_fields = (
        'created_at',
        'cleaned_text',
        'raw_data',
        'uuid',
        'language',
        'text_without_stop_words',
        'is_customer_message',
        'phone',
        'email',
        'name',
    )
    ordering = ('created_at',)


class MessageForAIAdmin(admin.ModelAdmin):
    list_display = (
        'uuid',
        'conversation_id',
        'text_without_stop_words',
        'cleaned_text',
        'is_customer_message',
        'phone',
        'email',
        'name',
        'created_at',
    )
    search_fields = (
        'uuid',
        'conversation_id',
        'text_without_stop_words',
        'is_customer_message',
        'phone',
        'email',
        'name',
        'created_at',
    )
    ordering = ('conversation', 'created_at')


class ConversationForAIAdmin(admin.ModelAdmin):
    inlines = [MessageForAIInline]
    list_display = (
        'dixa_id',
        'channel',
        'email',
        'phone',
    )
    search_fields = ('email__icontains', 'dixa_id__icontains')
    list_filter = ('channel',)
    actions = ['analyze_conversations_action']

    def analyze_conversations(
        self, request, queryset, form, daily_usage: DailyUsage, **kwargs
    ):
        gemini_client = GeminiClient(model_name=form.cleaned_data['model_name'])
        daily_usage.update_usage(queryset.count())
        for conversation in queryset:
            try:
                analyze_conversation(
                    conversation,
                    form.cleaned_data['system_instruction'],
                    gemini_client,
                    without_stop_words=form.cleaned_data['without_stop_words'],
                )
            except Exception as e:  # noqa: PERF203
                self.message_user(
                    request,
                    f'Error analyzing conversation {conversation.dixa_id}: {e!s}',
                    level=messages.ERROR,
                )
        return HttpResponseRedirect(request.get_full_path())

    @admin.display(description='Analyze selected conversations with AI')
    @group_member_required('dixa_ai_analysis')
    def analyze_conversations_action(self, request, queryset):
        daily_usage, _ = DailyUsage.objects.get_or_create(usage_date=datetime.today())
        conversations_to_analyze = queryset.count()
        if conversations_to_analyze > daily_usage.left_usage:
            self.message_user(
                request,
                'Daily limit will be exceeded. You can analyze only '
                f'{daily_usage.left_usage} conversations today. Remember that this is '
                f'only for test purposes. If you want to analyze more conversations, '
                f'please contact BET or PROPL team.',
                level=messages.ERROR,
            )
            return
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            queryset=queryset,
            form_class=AnalyzeConversationsForm,
            success_function=ConversationForAIAdmin.analyze_conversations,
            success_function_kwargs={'daily_usage': daily_usage},
            show_queryset=True,
        )


class AnalysisOutputFieldInline(admin.TabularInline):
    model = AnalysisOutputField
    extra = 1
    fields = ('field_name', 'description', 'possible_values')


class SystemInstructionAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_by', 'created_at')
    search_fields = ('name', 'created_by', 'text')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)
    inlines = [AnalysisOutputFieldInline]


class ConversationAnalysisResultAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'conversation_id',
        'created_at',
        'system_instruction',
        'model_name',
    )
    list_filter = ('model_name', 'system_instruction')
    search_fields = ('conversation__dixa_id', 'model_name', 'input', 'output')
    readonly_fields = (
        'conversation',
        'system_instruction',
        'model_name',
        'input',
        'output',
        'data',
    )
    actions = ['export_to_csv']
    list_select_related = ('system_instruction', 'conversation')

    @admin.display(description='Export selected results to CSV')
    def export_to_csv(self, request, queryset):
        df = pd.DataFrame(
            {
                'dixa_id': analysis.conversation.dixa_id,
                'system_instruction': analysis.system_instruction.name,
                'created_at': analysis.created_at,
                **analysis.data,
            }
            for analysis in queryset
        )
        current_time = timezone.now().strftime('%Y%m%d_%H%M%S')
        file_name = f'analysis_results_{current_time}'
        report = ReportFile.load_from_pandas_dataframe(df, file_name)
        return report.get_as_http_response()


class DailyUsageAdmin(admin.ModelAdmin):
    list_display = ('usage_date', 'limit', 'usage', 'left_usage')
    readonly_fields = ('usage',)

    def has_add_permission(self, request):
        # Records should only be created by the system
        return False


admin.site.register(ChannelMapping, ChannelMappingAdmin)
admin.site.register(ConversationForAI, ConversationForAIAdmin)
admin.site.register(MessageForAI, MessageForAIAdmin)
admin.site.register(SystemInstruction, SystemInstructionAdmin)
admin.site.register(ConversationAnalysisResult, ConversationAnalysisResultAdmin)
admin.site.register(DailyUsage, DailyUsageAdmin)
admin.site.register(WhatsappFile, WhatsappFileAdmin)
