from django.contrib.auth import get_user_model
from rest_framework import serializers

from complaints.internal_api.serializers import ForLogisticOrderComplaintSerializer
from custom.internal_api.enums import AssemblyTypeChoices
from custom.utils.fields import LogisticSMSEventClassField
from free_returns.serializers import ForLogisticFreeReturnSerializer
from invoice.choices import InvoiceStatus
from invoice.serializers import ForLogisticInvoiceSerializer
from orders.enums import OrderStatus
from orders.models import (
    Order,
    OrderItem,
)
from producers.choices import ProductStatus
from producers.models import Product, ProductStatusHistory
from regions.models import (
    Currency,
    Region,
)
from user_profile.models import UserProfile

User = get_user_model()


class ProductStatusHistorySerializer(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    status = serializers.SerializerMethodField()

    class Meta:
        model = ProductStatusHistory
        fields = ['created_at', 'status']

    def get_status(self, obj):
        return ProductStatus(obj.status).name


class ProductSerializer(serializers.ModelSerializer):
    status_history = ProductStatusHistorySerializer(
        source='product_status_history', many=True, read_only=True
    )

    class Meta:
        model = Product
        fields = ['id', 'order_id', 'status_history']


class CurrencySerializer(serializers.ModelSerializer):
    class Meta:
        model = Currency
        fields = ['id', 'name', 'code', 'symbol']


class ForLogisticOrderInProgressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = [
            'id',
            'country',
            'city',
            'order_type',
            'street_address_1',
            'street_address_2',
            'full_name',
            'owner',
            'postal_code',
            'email',
        ]


class ForLogisticOrderDeliveredSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = [
            'id',
            'country',
            'city',
            'order_type',
            'street_address_1',
            'street_address_2',
            'full_name',
            'owner',
            'postal_code',
            'email',
        ]


class ForLogisticUserProfileSerializer(serializers.ModelSerializer):
    orders_in_progress = serializers.SerializerMethodField(read_only=True)
    delivered_orders = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = UserProfile
        fields = ['id', 'language', 'orders_in_progress', 'delivered_orders']

    @staticmethod
    def get_orders_in_progress(obj):
        qs = Order.objects.filter(
            owner_id=obj.user.id,
            status__in={OrderStatus.IN_PRODUCTION, OrderStatus.TO_BE_SHIPPED},
        )
        return ForLogisticOrderInProgressSerializer(qs, many=True).data

    @staticmethod
    def get_delivered_orders(obj):
        qs = Order.objects.filter(
            owner_id=obj.user.id,
            status__in={OrderStatus.DELIVERED},
        )
        return ForLogisticOrderDeliveredSerializer(qs, many=True).data


class ForLogisticUserSerializer(serializers.ModelSerializer):
    profile = ForLogisticUserProfileSerializer(read_only=True)

    class Meta:
        model = User
        fields = [
            'id',
            'profile',
        ]


class ForLogisticRegionSerializer(serializers.ModelSerializer):
    currency = CurrencySerializer(read_only=True)

    class Meta:
        model = Region
        fields = ['id', 'name', 'currency']


class ForLogisticGalleryItemDescriptionSerializer(serializers.Serializer):
    name = serializers.CharField(read_only=True)
    material = serializers.CharField(read_only=True)
    dimensions = serializers.CharField(read_only=True)

    class Meta:
        fields = [
            'name',
            'material',
            'dimensions',
        ]


class ForLogisticGalleryItemSerializer(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    pattern_name = serializers.CharField(read_only=True, source='get_pattern_name')
    is_t03_wardrobe = serializers.BooleanField()
    product_type = serializers.CharField(read_only=True)
    weight = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        source='get_weight_from_cached_serialization',
    )
    item_description = ForLogisticGalleryItemDescriptionSerializer(
        read_only=True,
        source='get_item_description',
    )
    cover_only = serializers.SerializerMethodField(read_only=True)

    def get_cover_only(self, obj):
        return getattr(obj, 'covers_only', False)

    class Meta:
        fields = [
            'id',
            'pattern_name',
            'is_t03_wardrobe',
            'product_type',
            'weight',
            'item_description',
            'cover_only',
        ]


class ForLogisticOrderItemSerializer(serializers.ModelSerializer):
    content_type_model = serializers.ReadOnlyField(source='content_type.model')
    region = ForLogisticRegionSerializer(read_only=True)
    order_item = ForLogisticGalleryItemSerializer(read_only=True)
    price_number = serializers.DecimalField(
        source='get_price_number', max_digits=12, decimal_places=2
    )
    free_return = ForLogisticFreeReturnSerializer()

    class Meta:
        model = OrderItem
        fields = [
            'id',
            'created_at',
            'region',
            'order_item',
            'price_number',
            'quantity',
            'invoice_product_name',
            'content_type_model',
            'price',
            'region_price',
            'region_promo_value',
            'deleted',
            'free_return',
        ]


class ForLogisticOrderSerializer(serializers.ModelSerializer):
    from producers.internal_api.serializers import ForLogisticPackagingSerializer

    items = ForLogisticOrderItemSerializer(many=True)
    region = ForLogisticRegionSerializer(read_only=True)
    owner = ForLogisticUserSerializer(read_only=True)
    invoice_set = serializers.SerializerMethodField()
    is_outside_eu = serializers.SerializerMethodField()
    is_free = serializers.BooleanField()
    is_production_mix = serializers.BooleanField()
    complaint_notification_type = serializers.ReadOnlyField(
        source='get_complaint_notification_type'
    )
    phone = serializers.SerializerMethodField()
    complaints = ForLogisticOrderComplaintSerializer(read_only=True, many=True)
    estimated_delivery_date = serializers.SerializerMethodField()
    promised_delivery_range_start = serializers.SerializerMethodField()
    promised_delivery_range_end = serializers.SerializerMethodField()
    group_packaging = ForLogisticPackagingSerializer(
        source='get_group_packaging', many=True
    )

    def get_invoice_set(self, obj):
        qs = obj.invoice_set.exclude(status=InvoiceStatus.CORRECTING_DRAFT).exclude(
            status=InvoiceStatus.CORRECTING_PREVIEW,
        )
        return ForLogisticInvoiceSerializer(qs, many=True).data

    class Meta:
        model = Order
        fields = [
            'id',
            'created_at',
            'is_to_be_shipped_complaint',
            'is_to_be_shipped_complaint_express_replacement',
            'country',
            'vat',
            'country_area',
            'items',
            'order_type',
            'order_source',
            'assembly',
            'status',
            'parent_order_id',
            'first_name',
            'last_name',
            'total_price',
            'region_total_price',
            'region',
            'placed_at',
            'chosen_payment_method',
            'estimated_delivery_time',
            'complaint_notification_type',
            'paid_at',
            'settled_at',
            'items_type',
            'postal_code',
            'owner',
            'street_address_1',
            'street_address_2',
            'company_name',
            'floor_number',
            'order_notes',
            'hard_parking',
            'above_3rd_floor',
            'no_elevator',
            'invoice_postal_code',
            'city',
            'invoice_city',
            'phone',
            'phone_prefix',
            'email',
            'invoice_set',
            'complaints',
            'is_company',
            'notes',
            'cs_notes',
            'is_outside_eu',
            'is_free',
            'is_production_mix',
            'is_estimated_delivery_date',
            'estimated_delivery_date',
            'promised_delivery_range_start',
            'promised_delivery_range_end',
            'group_packaging',
        ]

    def get_is_outside_eu(self, obj: Order):
        region = obj.get_region(dont_use_cache=True)
        return not region.is_eu

    def get_phone(self, obj: Order) -> [str | None]:
        if obj.phone is None:
            return obj.phone

        no_trailing_phone_prefix_or_zeros = obj.phone.replace(' ', '').lstrip('0')

        if obj.phone_prefix:
            if no_trailing_phone_prefix_or_zeros.startswith(obj.phone_prefix):
                no_trailing_phone_prefix_or_zeros = (
                    no_trailing_phone_prefix_or_zeros.lstrip(obj.phone_prefix)
                )
            elif no_trailing_phone_prefix_or_zeros.startswith(
                obj.phone_prefix.replace('+', '')
            ):
                no_trailing_phone_prefix_or_zeros = (
                    no_trailing_phone_prefix_or_zeros.lstrip(
                        obj.phone_prefix.replace('+', '')
                    )
                )
        return no_trailing_phone_prefix_or_zeros.lstrip('0')

    def get_estimated_delivery_date(self, obj):
        if obj.is_estimated_delivery_date:
            return obj.estimated_delivery_date.strftime('%Y-%m-%dT%H:%M:%S.%f')
        return None

    def get_promised_delivery_range_start(self, obj):
        start_date = obj.get_promised_delivery_date_range()[0]
        return start_date.strftime('%Y-%m-%d') if start_date else None

    def get_promised_delivery_range_end(self, obj):
        end_date = obj.get_promised_delivery_date_range()[1]
        return end_date.strftime('%Y-%m-%d') if end_date else None


class LogisticSMSSerializer(serializers.Serializer):
    logistic_order_id = serializers.IntegerField(read_only=True)
    sms_event_class = LogisticSMSEventClassField()
    extra_kwargs = serializers.DictField()

    class Meta:
        fields = [
            'logistic_order_id',
            'sms_event_class',
            'extra_kwargs',
        ]


class OrderNoteSerializer(serializers.Serializer):
    order_id = serializers.PrimaryKeyRelatedField(queryset=Order.objects.all())
    note = serializers.CharField()


class LogisticOrderCarrierSerializer(serializers.Serializer):
    order_id = serializers.PrimaryKeyRelatedField(queryset=Order.objects.all())
    logistic_order_id = serializers.IntegerField()
    carrier = serializers.CharField()


class LogisticOrderAssemblyTypeSerializer(serializers.Serializer):
    assembly_type = serializers.ChoiceField(
        choices=AssemblyTypeChoices,
        allow_null=True,
        required=True,
    )
