import logging

from django.contrib.auth import get_user_model
from django.db import transaction
from django.db.models import Prefetch
from rest_framework import (
    mixins,
    status,
    viewsets,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.decorators import action
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response

from custom import permissions
from gallery.models import SampleBox
from orders.enums import OrderStatus
from orders.internal_api.deserializers import (
    AppendOrderNoteSerializer,
    ReplaceOrderNoteSerializer,
)
from orders.internal_api.report_serializers import (
    ForLogisticReportDetailedOrderSerializer,
)
from orders.internal_api.serializers import (
    ForLogisticOrderSerializer,
    LogisticOrderCarrierSerializer,
    LogisticSMSSerializer,
    OrderNoteSerializer,
    ProductSerializer,
)
from orders.models import (
    Order,
    OrderStatusHistory,
)
from orders.order_notes import (
    append_order_note,
    replace_assembly_type_order_note,
)
from orders.utils import (
    update_carrier,
    update_notes,
)
from producers.choices import ProductStatus
from producers.models import (
    Product,
)
from warehouse.enums import StockSampleBoxType
from warehouse.tools import SampleManager

User = get_user_model()

logger = logging.getLogger('cstm')


class OrderViewSet(
    viewsets.GenericViewSet,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
):
    queryset = Order.objects.all()
    serializer_class = ForLogisticOrderSerializer
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    @action(
        methods=['PATCH'],
        detail=True,
        url_path='replace-order-notes',
        url_name='replace-order-notes',
    )
    def replace_order_notes(self, request, *args, **kwargs):
        order = self.get_object()
        serializer = ReplaceOrderNoteSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        replace_assembly_type_order_note(
            order,
            assembly_type_old=serializer.validated_data['assembly_type_old'],
            assembly_type_new=serializer.validated_data['assembly_type_new'],
        )
        return Response(
            data=ForLogisticOrderSerializer(order).data,
            status=status.HTTP_200_OK,
        )

    @action(
        methods=['PATCH'],
        detail=True,
        url_path='append-order-notes',
        url_name='append-order-notes',
    )
    def append_order_notes(self, request, *args, **kwargs):
        order = self.get_object()
        serializer = AppendOrderNoteSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        notes = serializer.validated_data['notes']
        append_order_note(order, notes)

        return Response(
            data=ForLogisticOrderSerializer(order).data,
            status=status.HTTP_200_OK,
        )

    @action(
        methods=['POST'],
        detail=True,
        url_path='sent-to-customer',
        url_name='sent-to-customer',
    )
    def sent_to_customer(self, request, *args, **kwargs):
        order = self.get_object()
        order.change_status(OrderStatus.SHIPPED, should_callback_logistic=False)

        for order_item in order.items.all():
            furniture_item = order_item.order_item
            if isinstance(furniture_item, SampleBox):
                SampleManager.take_sample_from_stock(
                    furniture_item,
                    order.pk,
                    order.owner,
                    order_item.quantity,
                    StockSampleBoxType.TO_SEND.value,
                )

        return Response(
            data=ForLogisticOrderSerializer(order).data, status=status.HTTP_200_OK
        )

    @action(methods=['POST'], url_path='set-serialized-logistic-order', detail=True)
    def refresh_serialized_logistic_order(self, request, pk):
        order = get_object_or_404(Order, id=pk)
        order.set_serialized_logistic_info(request.data['serialized_logistic_order'])
        serializer = ForLogisticOrderSerializer(order)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(methods=['POST'], url_path='delete-serialized-logistic-order', detail=True)
    def delete_serialized_logistic_order(self, request, pk):
        order = get_object_or_404(Order, id=pk)
        order.delete_serialized_logistic_info(request.data['logistic_order_id'])
        serializer = ForLogisticOrderSerializer(order)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(methods=['POST'], url_path='change-status', detail=True)
    def change_status(self, request, pk):
        order = get_object_or_404(Order, id=pk)
        order_status = request.data['status']
        order.change_status(order_status, should_callback_logistic=False)
        if order_status in {OrderStatus.TO_BE_SHIPPED, OrderStatus.SHIPPED}:
            product_status = {
                OrderStatus.TO_BE_SHIPPED: ProductStatus.TO_BE_SHIPPED,
                OrderStatus.SHIPPED: ProductStatus.SENT_TO_CUSTOMER,
            }[order_status]

            for product in order.product_set.all():
                product.status_updater.change_status(
                    product_status,
                    owner=request.user,
                    should_callback_logistic=False,
                )
        serializer = ForLogisticOrderSerializer(order)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(methods=['POST'], url_path='bulk-update-notes', detail=False)
    @transaction.atomic
    def bulk_update_notes(self, request):
        data = request.data.get('data')
        serializer = OrderNoteSerializer(data=data, many=True)
        serializer.is_valid(raise_exception=True)
        updated_orders = update_notes(serializer.validated_data)

        serializer = ForLogisticOrderSerializer(updated_orders, many=True)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(
        methods=['PATCH'], url_path='bulk-update-logistic-orders-carrier', detail=False
    )
    def bulk_update_logistic_orders_carrier(self, request):
        serializer = LogisticOrderCarrierSerializer(
            data=request.data.get('data'),
            many=True,
        )
        serializer.is_valid(raise_exception=True)
        updated_orders = update_carrier(serializer.validated_data)

        serializer = ForLogisticOrderSerializer(updated_orders, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(methods=['GET'], url_path='for-report', detail=False)
    def detail_for_report(self, request):
        order_ids = request.data['ids']
        orders = Order.objects.filter(id__in=order_ids)
        orders_by_id = {}
        for order in orders:
            orders_by_id[order.id] = ForLogisticReportDetailedOrderSerializer(
                order
            ).data

        return Response(orders_by_id, status=status.HTTP_200_OK)

    @action(
        methods=['GET'], url_path='get-product-status-date-by-order-id', detail=False
    )
    def get_product_status_date_by_order_id(self, request):
        order_ids = request.query_params.getlist('ids')

        if not order_ids:
            return Response(
                {'error': 'Order IDs are required as query parameters.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            order_ids = [int(order_id) for order_id in order_ids]
        except ValueError:
            return Response(
                {'error': 'Order IDs must be integers.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        products = Product.objects.filter(order_id__in=order_ids).prefetch_related(
            'product_status_history'
        )

        serializer = ProductSerializer(products, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(
        methods=['GET'], url_path='to-be-shipped-status-changes-by-id', detail=False
    )
    def to_be_shipped_status_changes_by_id(self, request):
        order_ids = request.data['ids']

        orders = Order.objects.filter(id__in=order_ids).prefetch_related(
            Prefetch(
                'status_history',
                queryset=OrderStatusHistory.objects.filter(
                    status=OrderStatus.TO_BE_SHIPPED,
                ),
                to_attr='to_be_shipped_status_history',
            )
        )

        status_changed_by_order_id = {}
        for order in orders:
            status_change_dt = [
                status_change.created_at.strftime('%Y-%m-%d %H:%M:%S')
                for status_change in order.to_be_shipped_status_history
            ]

            status_changed_by_order_id[order.id] = (
                ', '.join(status_change_dt) if status_change_dt else '-'
            )

        return Response(status_changed_by_order_id, status=status.HTTP_200_OK)

    @action(methods=['POST'], url_path='send-logistic-sms', detail=True)
    def send_logistic_sms(self, request, pk):
        order = get_object_or_404(Order, pk=pk)
        serializer = LogisticSMSSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        sms_event_class = serializer.data['sms_event_class']
        sms_event_class(
            user=order.owner,
            email=order.email,
            logistic_order_id=request.data['logistic_order_id'],
            phone=order.full_phone,
            **serializer.data['extra_kwargs'],
        )

        return Response({}, status=status.HTTP_201_CREATED)
