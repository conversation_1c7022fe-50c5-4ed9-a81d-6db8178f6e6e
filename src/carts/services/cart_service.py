import logging

from functools import wraps
from typing import (
    TYPE_CHECKING,
    Optional,
)

from django.core.exceptions import (
    MultipleObjectsReturned,
    ObjectDoesNotExist,
)
from django.db.models import (
    QuerySet,
    Sum,
)
from django.db.models.functions import Coalesce
from django.utils import timezone
from rest_framework.request import Request

from carts.choices import CartStatusChoices
from carts.constants import MAX_CART_SIZE, MAX_VOUCHERS_PER_CART
from carts.exceptions import (
    InactiveCartException,
    MaxCartSizeError,
    MinCartItemSizeError,
)
from carts.models import (
    CART_ITEM_FIELDS,
    Cart,
    CartItem,
)
from gallery.types import FurnitureType
from orders.choices import OrderSource
from orders.enums import OrderStatus
from orders.utils import has_free_assembly_service
from pricing_v3.services.price_calculators import CartPriceCalculator
from regions.models import Region
from regions.services.limitations import LimitationService
from services.services.old_sofa_collection import OldSofaCollectionService
from services.services.white_gloves_delivery import WhiteGlovesDeliveryService
from vouchers.enums import VoucherOrigin
from vouchers.exceptions import MaxNumberVouchersReached, MaxUnstackableVouchersReached

if TYPE_CHECKING:
    from django.contrib.auth.models import User

    from orders.models import (
        Order,
        OrderItem,
    )
    from vouchers.models import Voucher


def cart_editable(method):
    """Decorator to check if the cart status allows edits."""

    @wraps(method)
    def wrapper(self, *args, **kwargs):
        if self.cart.status not in [CartStatusChoices.ACTIVE, CartStatusChoices.DRAFT]:
            raise InactiveCartException()
        return method(self, *args, **kwargs)

    return wrapper


logger = logging.getLogger('cstm')


class CartService:
    """Service for Cart operations."""

    def __init__(self, cart: 'Cart') -> None:
        self.cart: Cart = cart

    @classmethod
    def get_cart(
        cls,
        user: 'User',
        prefetch_related_fields: list[str] | None = None,
        select_related_fields: list[str] | None = None,
        update_strike_through_promo: bool = True,
    ) -> Cart | None:
        queryset = Cart.objects.filter(owner=user, status=CartStatusChoices.ACTIVE)

        if select_related_fields:
            queryset = queryset.select_related(*select_related_fields)

        if prefetch_related_fields:
            queryset = queryset.prefetch_related(*prefetch_related_fields)

        cart = queryset.last()
        if update_strike_through_promo:
            if cart and CartService._update_for_strike_through_promo(cart):
                cls.recalculate_cart(cart)

        return cart

    @classmethod
    def create_cart(cls, user: 'User') -> Cart:
        cart = Cart(
            owner=user,
            status=CartStatusChoices.ACTIVE,
            region=user.profile.region or Region.get_other(),
        )
        cart.set_vat_fields()
        cart.save()

        return cart

    @classmethod
    def get_or_create_cart(
        cls,
        user: 'User',
        prefetch_related_fields: list[str] | None = None,
        select_related_fields: list[str] | None = None,
    ) -> Cart:
        cart = cls.get_cart(
            user,
            prefetch_related_fields=prefetch_related_fields,
            select_related_fields=select_related_fields,
        )
        return cart or cls.create_cart(user)

    @classmethod
    def get_cart_related_order(cls, user: 'User') -> Optional['Order']:
        if cart := cls.get_cart(user):
            return cart.order

    def get_cart_size(self) -> int:
        return self.cart.material_items.aggregate(
            cart_size=Coalesce(Sum('quantity'), 0)
        )['cart_size']

    def get_items_with_prefetched_furniture(self) -> QuerySet[CartItem]:
        return self.cart.items.prefetch_related('cart_item')

    @cart_editable
    def add_to_cart(
        self, sellable_item: FurnitureType, quantity: int = 1, recalculate: bool = True
    ) -> None:
        if self.cart.is_empty and not self.cart.first_item_added_at:
            self.cart.first_item_added_at = timezone.now()
        elif self.get_cart_size() > MAX_CART_SIZE:
            raise MaxCartSizeError

        cart_item = self._create_cart_item(sellable_item, quantity=quantity)
        self._update_for_strike_through_promo(self.cart, save=False)

        self.adjust_services(item=cart_item)
        self._update_analytic_fields()
        if recalculate:
            self.recalculate_cart(self.cart)

    @cart_editable
    def increase_item_quantity(self, item: 'CartItem') -> None:
        if self.get_cart_size() + 1 > MAX_CART_SIZE:
            raise MaxCartSizeError
        item.quantity += 1
        item.save(update_fields=['quantity'])

        self.adjust_services(item=item)
        self.recalculate_cart(cart=item.cart)

    @cart_editable
    def decrease_item_quantity(self, item: 'CartItem') -> None:
        if item.quantity - 1 < 1:
            raise MinCartItemSizeError
        item.quantity -= 1
        item.save(update_fields=['quantity'])

        self.adjust_services(item=item)
        self.recalculate_cart(cart=item.cart)

    @cart_editable
    def delete_from_cart(
        self,
        sellable_item_id: int,
        content_type: str,
    ) -> None:
        cart_item = self.get_items_with_prefetched_furniture().get(
            object_id=sellable_item_id,
            content_type__model=content_type,
        )

        if (
            # check if sellable item is not connected with any order items
            not cart_item.sellable_item.order_items.exists()
            and not cart_item.is_service
            # SKUs are just a list of regular products, non-configurable
            and not cart_item.is_sku
        ):
            cart_item.sellable_item.delete()

        cart_item.delete()

        self.adjust_services(item=cart_item)
        # cart has prefetched items
        self.cart.refresh_from_db()

        self._update_analytic_fields()
        self.recalculate_cart(self.cart)

    def sync_with_order(
        self,
        request: Request = None,
        source: int = OrderSource.UNKNOWN_SOURCE,
    ) -> 'Order':
        order = self._get_order() or self._create_order(source)
        order.clear_methods_cache()

        if request:
            order.update_ab_tests(request)

        self._sync_cart(order)
        self._sync_items(order)

        return order

    def handle_paid_order(self) -> None:
        self.cart.status = CartStatusChoices.ORDERED
        self.cart.save(update_fields=['status'])

        order = self.cart.order
        if order and not order.is_parent_for_split():
            if unpaid_items := self._get_unpaid_items(order):
                new_cart = CartService.create_cart(self.cart.owner)
                for item in unpaid_items:
                    new_cart.items.add(item)

                self.recalculate_cart(new_cart)

        self.cart.delete()

    def change_status_to_active(self) -> None:
        has_active_cart = Cart.objects.filter(
            owner=self.cart.owner,
            status=CartStatusChoices.ACTIVE,
        ).exists()

        if has_active_cart:
            return

        self.cart.status = CartStatusChoices.ACTIVE
        self.cart.save(update_fields=['status'])

    def change_status_to_draft(self) -> None:
        self.cart.status = CartStatusChoices.DRAFT
        self.cart.save(update_fields=['status'])

    def change_status_to_payment_pending(self) -> None:
        self.cart.status = CartStatusChoices.PAYMENT_PENDING
        self.cart.save(update_fields=['status'])

    @cart_editable
    def change_region(self, region: Region) -> None:
        if region == self.cart.region:
            return

        self.cart.region = region
        self.cart.clear_methods_cache()
        self._update_services(region=region)
        self._update_items_for_new_region(region)
        self._update_for_strike_through_promo(self.cart, save=False)
        self.recalculate_cart(self.cart, check_vat=True)

    @cart_editable
    def change_assembly(self, activate: bool) -> None:
        if (
            activate is True
            and not LimitationService(region=self.cart.region).is_assembly_available
        ):
            raise ValueError(
                'Assembly is not available for this region or it is a fast track'
            )

        self.cart.assembly = activate
        self.cart.items.get_without_assembly_service_required().update(
            with_assembly=activate
        )

        self._refresh_checkout_state(analytics=True)

    @cart_editable
    def change_old_sofa_collection(self, activate: bool) -> None:
        limitation_service = LimitationService(region=self.cart.region)
        if activate and limitation_service.is_old_sofa_collection_available is False:
            raise ValueError('Service is not available for this region.')

        service = OldSofaCollectionService(self.cart)
        if activate:
            service.add_service()
        else:
            service.remove_service()

        self._refresh_checkout_state(analytics=True)

    @cart_editable
    def change_white_gloves_delivery(self, activate: bool) -> None:
        white_gloves_delivery = WhiteGlovesDeliveryService(self.cart)
        if activate:
            white_gloves_delivery.add_service()
        else:
            white_gloves_delivery.remove_service()
        self._refresh_checkout_state(analytics=True)

    @cart_editable
    def adjust_old_sofa_collection(self) -> None:
        # without recalculation
        service = OldSofaCollectionService(self.cart)
        if not self.cart.has_old_sofa_collection:
            return
        elif not self.cart.has_legit_sotty:
            service.remove_service()
        else:
            service.adjust_price()

    @cart_editable
    def adjust_white_gloves_delivery(self) -> None:
        # without recalculation
        white_gloves_delivery = WhiteGlovesDeliveryService(self.cart)
        # if delivery service, do nothing
        if not self.cart.has_white_gloves_delivery and not self.cart.assembly:
            return
        elif not self.cart.has_legit_sotty:
            white_gloves_delivery.remove_service()
        elif (
            not self.cart.has_white_gloves_delivery
            and self.cart.assembly
            and LimitationService(
                region=self.cart.region
            ).is_white_gloves_delivery_available
        ):
            white_gloves_delivery.add_service()
        else:
            white_gloves_delivery.adjust_price()

    @cart_editable
    def reset_promo(self) -> None:
        self.cart.clear_promo()
        self._refresh_checkout_state(analytics=False)

    @cart_editable
    def add_voucher(self, voucher: 'Voucher', check_vat: bool = False) -> None:
        if not voucher.is_stackable and self.cart.main_voucher:
            raise MaxUnstackableVouchersReached()
        if self.cart.vouchers.count() >= MAX_VOUCHERS_PER_CART:
            raise MaxNumberVouchersReached()
        self.cart.vouchers.add(voucher)
        self._refresh_checkout_state(analytics=False, check_vat=check_vat)

    @cart_editable
    def remove_voucher(self, voucher: 'Voucher', check_vat: bool = False) -> None:
        self.cart.vouchers.remove(voucher)
        self._refresh_checkout_state(analytics=False, check_vat=check_vat)

    @staticmethod
    def recalculate_cart(cart: 'Cart', check_vat: bool = False) -> None:
        CartPriceCalculator(instance=cart).calculate(check_vat=check_vat)

    @classmethod
    def create_cart_for_order(
        cls,
        order: 'Order',
        status: CartStatusChoices = CartStatusChoices.ACTIVE,
    ) -> Cart:
        now = timezone.now()
        cart = Cart(
            order=order,
            owner=order.owner,
            status=status,
            first_item_added_at=now,
            items_changed_at=now,
            price_updated_at=now,
            vat=order.vat or '',
            **order.common_fields,
        )
        cart.save()

        cart.vouchers.set(order.vouchers.all())

        order_items = order.items.prefetch_related('order_item')
        cart_items_to_create = []
        for order_item in order_items:
            cart_item = CartItem(
                cart=cart,
                cart_item=order_item.order_item,
                **order_item.fields_dict,
            )
            cart_items_to_create.append(cart_item)

        CartItem.objects.bulk_create(cart_items_to_create)
        if order.status == OrderStatus.CART:
            order.status = OrderStatus.DRAFT
            order.save(update_fields=['status'])

        return cart

    def order_exists(self) -> bool:
        return self._get_order() is not None

    @property
    def has_corduroy(self) -> bool:
        for cart_item in self.cart.items.all():
            if cart_item.is_corduroy:
                return True

        return False

    @property
    def has_s01(self) -> bool:
        for cart_item in self.cart.items.all():
            if cart_item.is_s01:
                return True

        return False

    @property
    def has_t03(self) -> bool:
        for cart_item in self.cart.items.all():
            if cart_item.is_t03:
                return True

        return False

    def _get_unpaid_items(self, order: 'Order') -> list[CartItem]:
        """Get unpaid items from the cart.

        This handles a case when user added some items to the cart (in another tab)
        and then paid the order. When creating new cart, we want to add these new unpaid
        items to the new cart.
        """
        order_sellable_items = [
            item.sellable_item for item in order.items.prefetch_related('order_item')
        ]
        unpaid_cart_items = []
        for cart_item in self.get_items_with_prefetched_furniture():
            if cart_item.sellable_item not in order_sellable_items:
                unpaid_cart_items.append(cart_item)  # noqa: PERF401

        return unpaid_cart_items

    def _get_order(self) -> Optional['Order']:
        order_statuses_editable = {
            OrderStatus.DRAFT,
            OrderStatus.PAYMENT_FAILED,
            OrderStatus.PAYMENT_PENDING,
        }
        if self.cart.order and self.cart.order.status in order_statuses_editable:
            return self.cart.order

    def _create_order(self, source: int) -> 'Order':
        from orders.models import Order

        order = Order.objects.create(
            owner=self.cart.owner,
            status=OrderStatus.DRAFT,
            order_source=source,
            first_item_added_at=self.cart.first_item_added_at,
            items_changed_at=self.cart.items_changed_at,
            price_updated_at=self.cart.price_updated_at,
            **self.cart.common_fields,
        )
        self.cart.order = order
        self.cart.save(update_fields=['order'])

        return order

    def _sync_cart(self, order: 'Order') -> None:
        """Sync cart with order.

        Make sure that cart has the same values as the order.
        """
        for field, value in self.cart.common_fields.items():
            setattr(order, field, value)

        order.save(update_fields=self.cart.common_fields.keys())
        order.vouchers.set(self.cart.vouchers.all())
        self._handle_barter_deal(order)

    @staticmethod
    def _handle_barter_deal(order: 'Order') -> None:
        """Update order barter deal data after syncing with Cart."""
        from orders.models import OrderBarterData

        if order.is_barter:
            order.barter_data.delete()

        try:
            barter_deal_voucher = order.vouchers.get(
                origin=VoucherOrigin.INFLUENCERS_BARTER_DEAL,
                barter_deal__currency=order.region.currency,
            )
        except ObjectDoesNotExist:
            pass
        except MultipleObjectsReturned:
            logger.error(
                f'Order with id: {order.id} has multiple barter vouchers assigned to it'
            )
        else:
            OrderBarterData.objects.create(
                order=order,
                barter_deal=barter_deal_voucher.barter_deal,
            )

    def _sync_items(self, order: 'Order') -> None:
        """Sync order items with cart items.

        Make sure that order has all the items from the cart, no more no less.
        """
        cart_items = self.get_items_with_prefetched_furniture()
        order_items = order.items.prefetch_related('order_item')

        self._create_order_items_for_cart_items(order, order_items, cart_items)
        self._remove_order_items_without_substitutes_in_cart(order_items, cart_items)

    @staticmethod
    def _create_order_items_for_cart_items(
        order: 'Order',
        order_items: QuerySet['OrderItem'],
        cart_items: QuerySet[CartItem],
    ) -> None:
        from orders.models import OrderItem

        order_items_to_create = []
        order_items_to_update = []

        for cart_item in cart_items:
            try:
                order_item = order_items.get(
                    object_id=cart_item.object_id,
                    content_type=cart_item.content_type,
                )
            except OrderItem.DoesNotExist:  # noqa: PERF203
                order_item = OrderItem(
                    order=order,
                    order_item=cart_item.cart_item,
                    **cart_item.fields_dict,
                )
                order_items_to_create.append(order_item)
            else:
                for field, value in cart_item.fields_dict.items():
                    setattr(order_item, field, value)

                order_items_to_update.append(order_item)

        if order_items_to_create:
            OrderItem.objects.bulk_create(order_items_to_create)

        if order_items_to_update:
            OrderItem.objects.bulk_update(order_items_to_update, CART_ITEM_FIELDS)

    @staticmethod
    def _remove_order_items_without_substitutes_in_cart(
        order_items: QuerySet['OrderItem'],
        cart_items: QuerySet[CartItem],
    ) -> None:
        from orders.models import OrderItem

        cart_furniture = [item.sellable_item for item in cart_items]
        order_item_ids_to_delete = []
        for order_item in order_items:
            if order_item.order_item not in cart_furniture:
                order_item_ids_to_delete.append(order_item.id)  # noqa: PERF401

        OrderItem.objects.filter(id__in=order_item_ids_to_delete).delete()

    def _update_items_for_new_region(self, region: Region) -> None:
        limitation_service = LimitationService(region=region)
        cart_items = self.get_items_with_prefetched_furniture()

        for cart_item in cart_items:
            if self._is_item_invalid_for_region(cart_item, limitation_service):
                self.delete_from_cart(
                    cart_item.object_id,
                    cart_item.content_type.model,
                )

            cart_item.clear_methods_cache()

        cart_items.update(region=region)

    @staticmethod
    def _is_item_invalid_for_region(
        item: CartItem, limitation_service: LimitationService
    ) -> bool:
        return any(
            [
                item.is_corduroy and not limitation_service.is_corduroy_available,
                item.is_t03 and not limitation_service.is_t03_available,
                item.is_s01 and not limitation_service.is_s01_available,
                (
                    item.is_old_sofa_collection_service
                    and not limitation_service.is_old_sofa_collection_available
                ),
                (
                    item.is_white_gloves_delivery_service
                    and not limitation_service.is_white_gloves_delivery_available
                ),
            ]
        )

    def _update_analytic_fields(self) -> None:
        now = timezone.now()
        self.cart.items_changed_at = self.cart.price_updated_at = now

    def _create_cart_item(
        self, sellable_item: FurnitureType, quantity: int
    ) -> CartItem:
        cart_item = CartItem(
            cart=self.cart,
            cart_item=sellable_item,
            region=self.cart.region,
            quantity=quantity,
            price=0,
            price_net=0,
            region_price=0,
            region_price_net=0,
            free_assembly_service=False,
            with_assembly=sellable_item.is_assembly_service_required,
        )

        if has_free_assembly_service(cart_item):
            cart_item.free_assembly_service = True
            cart_item.assembly_price = 0
            cart_item.region_assembly_price = 0

        cart_item.save()
        return cart_item

    @staticmethod
    def _update_for_strike_through_promo(cart: 'Cart', save: bool = True) -> bool:
        from promotions.utils import strikethrough_promo

        updated = False
        strikethrough_promo = strikethrough_promo(region=cart.region)
        if not strikethrough_promo:
            vouchers = cart.vouchers.all()
            for voucher in vouchers:
                if not voucher.is_active():
                    cart.vouchers.remove(voucher)
                    updated = True
        elif not cart.main_voucher:
            cart.vouchers.add(strikethrough_promo.promo_code)
            updated = True
        return updated

    def _update_services(self, region: Region) -> None:
        """
        Update delivery services based on availability and other active services
        """
        limitation_service = LimitationService(region=region)
        self._deactivate_services(service=limitation_service)
        if self.cart.delivery_service_used:
            self._activate_services(service=limitation_service)

    def _deactivate_services(self, service: LimitationService) -> None:
        if self.cart.assembly and not service.is_assembly_available:
            self.change_assembly(activate=False)
        if (
            self.cart.has_white_gloves_delivery
            and not service.is_white_gloves_delivery_available
        ):
            self.change_white_gloves_delivery(activate=False)

    def _activate_services(self, service: LimitationService) -> None:
        if not self.cart.assembly and service.is_assembly_available:
            self.change_assembly(activate=True)
        if (
            not self.cart.has_white_gloves_delivery
            and service.is_white_gloves_delivery_available
            and self.cart.has_legit_sotty
        ):
            self.change_white_gloves_delivery(activate=True)

    def adjust_services(self, item: CartItem) -> None:
        """
        Update services in the cart, if changes affect related item
        E.g. Increasing legit sotty quantity influences old sofa collection price
        """
        if item.is_legit_sotty:
            self.adjust_old_sofa_collection()
            self.adjust_white_gloves_delivery()

    def _refresh_checkout_state(
        self, analytics: bool = True, check_vat: bool | None = None
    ) -> None:
        if analytics:
            self._update_analytic_fields()
        if check_vat is None:
            self.recalculate_cart(self.cart)
        else:
            self.recalculate_cart(self.cart, check_vat=check_vat)
        if order := self._get_order():
            self._sync_cart(order)
            self._sync_items(order)
