import datetime
import logging
import random
import uuid

from itertools import chain
from typing import Optional

from django.apps import apps
from django.conf import settings
from django.contrib.auth import login
from django.contrib.auth.models import User
from django.core.cache import cache
from django.db import (
    models,
    transaction,
)
from django.db.models.signals import post_save
from django.utils import (
    timezone,
    translation,
)
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from carts.services.cart_service import CartService
from custom.enums import LanguageEnum
from custom.metrics import metrics_client
from custom.utils.decorators import cache_function
from custom.utils.strings import sanitize_incoming_string
from dixa.models import DixaWebHookEvent
from gallery.enums import FurnitureStatusEnum
from orders.choices import OrderSource
from regions.cached_region import CachedRegionData
from regions.geo_guessr import get_ip_address
from regions.mixins import RegionalizedMixin
from regions.models import (
    Country,
    Region,
)
from regions.utils import get_region_from_url
from user_profile.choices import (
    Gender,
    RegistrationSource,
    UserType,
)
from user_profile.managers import UserProfileManager
from user_profile.models import AddressDataAbstractModel
from user_profile.services.user_profile_auth import create_user_profile
from vouchers.models import Voucher

logger = logging.getLogger('cstm')


class UserProfile(RegionalizedMixin, AddressDataAbstractModel):
    user_type = models.IntegerField(choices=UserType.choices, default=UserType.CUSTOMER)
    is_business_type = models.BooleanField(default=False)
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        related_name='profile',
        on_delete=models.CASCADE,
    )
    updated_at = models.DateTimeField(auto_now=True)

    facebook_id = models.BigIntegerField('facebook id', blank=True, null=True)

    # TODO: probably could be removed
    account_name = models.CharField(  # noqa: DJ001
        'user name in account',
        max_length=256,
        null=True,
        blank=True,
    )
    language = models.CharField(
        max_length=8,
        choices=LanguageEnum.choices,
        default=LanguageEnum(settings.LANGUAGE_CODE),
    )
    gender = models.PositiveSmallIntegerField(
        _('gender'), default=Gender.NONE, choices=Gender.choices
    )
    job = models.CharField('job', max_length=256, default='', blank=True)
    notes = models.TextField(blank=True, default='')

    sms_agreed = models.BooleanField(default=False)
    newsletter_agreed = models.BooleanField(default=False)
    external_marketing_profile_created = models.BooleanField(default=False)
    registration_source = models.PositiveSmallIntegerField(
        choices=RegistrationSource.choices, default=RegistrationSource.WEB
    )
    registration_referrer = models.TextField(blank=True, null=True)  # noqa: DJ001
    registration_referrer_uri = models.TextField(blank=True, null=True)  # noqa: DJ001
    registration_ip = models.CharField(max_length=45, blank=True, null=True)  # noqa: DJ001
    registration_guest = models.ForeignKey(
        'UserProfile',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    registration_first_contact = models.DateTimeField(
        blank=True,
        null=True,
        auto_now_add=True,
    )
    registration_country = models.CharField(  # noqa: DJ001
        blank=True,
        max_length=32,
        null=True,
    )
    registration_user_agent = models.CharField(  # noqa: DJ001
        blank=True,
        max_length=512,
        null=True,
    )
    has_mobile_app = models.BooleanField(default=False)
    region = models.ForeignKey(
        'regions.Region',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    accepted_changed_rows = models.DateTimeField(blank=True, null=True)

    additional_data = models.JSONField(blank=True, default=dict)

    objects = UserProfileManager()

    class Meta(object):
        verbose_name = 'User profile'
        verbose_name_plural = 'User profiles'

    def __str__(self):
        return 'User {} is a {}'.format(
            self.user,
            self.get_user_type_display(),
        )

    def save(self, *args, **kwargs):
        from customer_service.models import CSUserProfile
        from customer_service.tasks import update_or_create_cs_user_profile

        super().save(*args, **kwargs)
        if CSUserProfile.should_update_or_create_from_user_profile(self):
            transaction.on_commit(
                lambda: update_or_create_cs_user_profile.delay(self.pk)
            )

    @property
    def full_phone(self):
        return (
            f'{self.phone_prefix}{self.phone}'
            if self.phone and self.phone_prefix
            else None
        )

    @property
    def status_re_cache_key(self):
        return '{}_user_status'.format(self.user_id)

    @property
    def status_response(self):
        if getattr(self, '_status_response', False):
            return self._status_response
        else:
            return cache.get(self.status_re_cache_key)

    @status_response.setter
    def status_response(self, response):
        if response:
            self._status_response = response
            cache.set(self.status_re_cache_key, response, 30 * 60)
        else:
            self._status_response = None
            cache.delete(self.status_re_cache_key)

    @status_response.deleter
    def status_response(self):
        self._status_response = None
        cache.delete(self.status_re_cache_key)

    def get_connected_accounts(self):
        return list(self.userprofile_set.all())

    def library_jetty_set(self):
        return [
            jetty for jetty in self.user.jetty_set.all() if jetty.furniture_status == 1
        ]

    def get_locale(self, request=None):
        if request:
            self.language = request.LANGUAGE_CODE

        return LanguageEnum.get_locale(
            self.language,
            united_states=False,
        )

    def get_library_items(self, with_prefetched_images=False):
        user_furniture_sets = (
            self.user.jetty_set,
            self.user.sotty_set,
            self.user.watty_set,
        )
        library_items = []
        for user_furniture_set in user_furniture_sets:
            queryset = user_furniture_set.filter(
                furniture_status=FurnitureStatusEnum.SAVED
            )
            if with_prefetched_images:
                queryset = queryset.prefetch_related('additional_images')
            library_items.extend(list(queryset))

        library_items.sort(key=lambda x: x.created_at, reverse=True)

        return library_items

    def clear_library_item_number_cache(self):
        cache.delete(f'lin_{self.user_id}')

    def get_library_item_number(self):
        lin = cache.get('lin_%d' % self.user_id)
        if lin is None:
            user_wishlists = (
                self.user.jetty_set,
                self.user.sotty_set,
                self.user.watty_set,
            )
            lin = sum(
                qs.filter(furniture_status=FurnitureStatusEnum.SAVED).count()
                for qs in user_wishlists
            )
            cache.set('lin_%d' % self.user.id, lin, 60 * 60 * 24)
        return lin

    def has_empty_address(self):
        if (
            self.first_name not in [None, '']
            and self.last_name not in [None, '']
            and self.country not in [None, '']
            and self.street_address_1 not in [None, '']
        ):
            return False
        else:
            return True

    def get_language_code(self):
        return LanguageEnum.get_locale(self.language).lower()

    @staticmethod
    def create_and_login_guest(request, register_referer=''):
        if region := get_region_from_url(request.META.get('HTTP_REFERER', None)):
            region = region.cached_region_data
        else:
            region = CachedRegionData(**request.session['cached_region'])

        user = User.objects.create_user(
            ''.join(random.choice('0123456789ABCDEF!@#$%^&*()&^') for _ in range(29))  # noqa: S311
        )
        fields_to_update = [
            'user_type',
            'language',
            'registration_ip',
            'registration_referrer',
            'registration_referrer_uri',
            'registration_source',
            'registration_user_agent',
        ]
        profile = user.profile

        if advertising_consents := request.session.get('advertising_consents'):
            profile.additional_data['advertising_consents'] = advertising_consents
            fields_to_update.append('additional_data')

        profile.user_type = UserType.GUEST_CUSTOMER
        profile.language = (
            request.LANGUAGE_CODE or translation.get_language_from_request(request)
        )
        profile.registration_ip = get_ip_address(request)
        if request.session.get('user_utm_source'):
            profile.registration_referrer = (
                str(request.session.get('user_utm_source')) + register_referer
            )
        else:
            profile.registration_referrer = request.get_full_path()
        profile.registration_referrer_uri = sanitize_incoming_string(
            request.session.get('HTTP_REFERER'), max_length=512
        )
        if 'contact_ts' in request.session:
            profile.registration_first_contact = timezone.make_aware(
                datetime.datetime.fromtimestamp(float(request.session['contact_ts']))
            )
            fields_to_update += ['registration_first_contact']
        user_agent = sanitize_incoming_string(
            request.META.get('HTTP_USER_AGENT', ''), max_length=512
        )
        profile.registration_source = (
            UserProfile.get_registration_source_from_http_agent(user_agent)
        )
        profile.registration_user_agent = user_agent
        try:
            profile.region = Region.objects.get(id=region.id)
            fields_to_update += ['region']
        except Region.DoesNotExist:
            logger.warning('Region %s:%s does not exists', region.id, region.name)
        profile.save(update_fields=fields_to_update)
        agent_type = (
            'mobile'
            if request.user_agent.is_mobile or request.user_agent.is_tablet
            else 'web'
        )
        user.backend = 'django.contrib.auth.backends.ModelBackend'
        login(request, user)
        metrics_client().increment(
            'registration',
            1,
            tags=[
                f'agent_type:{agent_type}',
                'user_type:guest',
                'user_region_name:{}'.format(
                    profile.region.name if profile.region else '?',
                ),
            ],
        )
        metrics_client().increment(
            'backend.login',
            1,
            tags=[f'agent_type:{agent_type}', 'user_type:guest'],
        )
        if 'promo_code' in request.session:
            promo_code = request.session['promo_code']
            try:
                voucher = Voucher.objects.get(
                    active=True, code=promo_code, quantity_left__gt=0
                )
            except Voucher.DoesNotExist:
                logger.warning(
                    '[user_id=%s] Given voucher does not exist <- %s',
                    user.id,
                    promo_code,
                )
                return user

            cart = CartService.get_or_create_cart(user)
            CartService(cart).add_voucher(voucher)

            if hasattr(request, 'session') and 'promo_code' in request.session:
                del request.session['promo_code']

        return user

    @staticmethod
    def get_registration_source_from_http_agent(user_agent):
        if user_agent is not None:
            user_agent = sanitize_incoming_string(user_agent.lower())
            if 'iphone' in user_agent:
                return 1
            elif 'ipad' in user_agent:
                return 2
            elif 'android' in user_agent:
                return 6
            else:
                return 0
        return RegistrationSource.UNKNOWN

    @property
    def first_buy_date(self):
        from orders.models import PaidOrders

        o = PaidOrders.objects.filter(owner=self.user).last()
        return o.paid_at if o else None

    def change_region(self, new_region: Region) -> None:
        if new_region == self.region:
            return

        new_country = new_region.get_country(without_cache=True)
        if new_country is None or not (
            new_country is None or new_country.language_code in LanguageEnum.values
        ):
            self.language = settings.LANGUAGE_CODE
            if new_country is not None:
                self.country = new_country.name
        else:
            self.country = new_country.name
            self.language = new_country.language_code

        self.region = new_region
        self.save(update_fields=['country', 'language', 'region'])
        self.clear_methods_cache()

    def change_region_by_country(self, country_name):
        try:
            country = Country.objects.get(name__iexact=country_name)
            region = country.region
        except Country.DoesNotExist:
            logger.warning(
                (
                    '[user_id=%s] User has supplied an unsupported '
                    'country=%s. Setting other region.'
                ),
                self.user.id,
                country_name,
            )
            region = Region.get_other()
        self.change_region(region)

        return region

    @property
    def cached_region_data(self) -> CachedRegionData:
        return self.region.cached_region_data

    def get_absolute_url(self):
        return f'/admin/user_profile/userprofile/{self.id}/'

    def get_checkout_url(self):
        Cart = apps.get_model('carts', 'Cart')
        cart = Cart.objects.filter(owner_id=self.user).order_by('-created_at').first()
        if cart:
            url = f'checkout/{cart.id}?uuid={self.user.id}'
        else:
            url = 'account'
        return url

    def get_user_reviews(self):
        from reviews.models import Review

        reviews = Review.latest_objects.filter(order__in=self.user.order_set.all())
        return reviews

    def get_user_summary(self):
        from complaints.models import Complaint

        resp = []
        reviews = self.get_user_reviews()

        orders = [order for order in self.user.order_set.all() if order.status != 9]

        if reviews.count() > 0:
            reviews_description = ''
            reviews_description += 'Has {} reviews ('.format(reviews.count())
            for review in reviews:
                reviews_description += '{}* with sentiment: {}, '.format(
                    review.score, review.get_review_sentiment_display()
                )
            reviews_description += ').'
            resp.append(reviews_description)
        else:
            resp.append('Has no reviews.')

        resp.append('')

        claims = Complaint.objects.filter(product__order__in=orders)

        if claims.count() > 0:
            for claim in claims:
                claim_description = ''
                claim_description += format_html(
                    (
                        'Has claim with status: <strong>{}</strong>, '
                        'complaint type: {}, responsibility: {}'
                    ),
                    claim.get_status_display(),
                    claim.complaint_type,
                    claim.responsibility,
                )
                if claim.refund:
                    claim_description += ', it received refund'
                if claim.reproduction:
                    claim_description += ', it required reproduction'
                if claim.assembly_team_intervention:
                    claim_description += ', it required assembly team intervention'
                if not claim.reported_date:
                    claim_description += '<br/> It has no reported date - CALL IT'
                else:
                    if claim.production_ordered_date:
                        claim_description += format_html(
                            (
                                '.<br/>It was reported {}, <br/>ordered '
                                'in production: {} (after {} days)'
                            ),
                            claim.reported_date,
                            claim.production_ordered_date,
                            (claim.production_ordered_date - claim.reported_date).days,
                        )
                        if claim.production_released_date:
                            claim_description += format_html(
                                (
                                    ' <br/>released from production: {} '
                                    '(after next {} days, {} days from reported date)'
                                ),
                                claim.production_released_date,
                                (
                                    claim.production_released_date
                                    - claim.production_ordered_date
                                ).days,
                                (
                                    claim.production_released_date - claim.reported_date
                                ).days,
                            )
                            if claim.shipment_date:
                                claim_description += format_html(
                                    (
                                        ' <br/>sent to customer: {}(after next {} '
                                        'days, {} days from reported date)'
                                    ),
                                    claim.shipment_date,
                                    (
                                        claim.shipment_date
                                        - claim.production_released_date
                                    ).days,
                                    (claim.shipment_date - claim.reported_date).days,
                                )
                    else:
                        claim_description += format_html(
                            (
                                '.<br/>It was reported {}, not yet ordered '
                                'in production (and its {} days later)'
                            ),
                            claim.reported_date,
                            (datetime.date.today() - claim.reported_date).days,
                        )
                resp.append(claim_description)
        else:
            resp.append('Has no claims.')

        resp.append('')

        if len(orders) > 0:
            description = ''
            description += format_html(
                'Has <strong>{}</strong> orders, from latest:', len(orders)
            )
            for order in orders:
                description += format_html(
                    '<br/>Id: <strong>{}</strong>, {} items, {}. ',
                    order.id,
                    order.items.count(),
                    order.get_status_display(),
                )
                if order.paid_at:
                    description += format_html(
                        '<br/>Paid at: {}, Estimated delivery: {}',
                        order.paid_at.date(),
                        order.estimated_delivery_time.date()
                        if order.estimated_delivery_time
                        else 'no date',
                    )
                    logistic_info = (
                        order.logistic_info[0] if order.logistic_info else None
                    )
                    if logistic_info and logistic_info.sent_to_customer:
                        description += format_html(
                            '<br/>sent to customer: {} (after {} days from paid)',
                            logistic_info.sent_to_customer,
                            (
                                logistic_info.sent_to_customer - order.paid_at.date()
                            ).days,
                        )
                        if logistic_info.delivered_date:
                            description += format_html(
                                (
                                    '<br/>delivered: {} (after {} days from paid, '
                                    '{} days diff than estimated)'
                                ),
                                logistic_info.delivered_date,
                                (
                                    logistic_info.delivered_date - order.paid_at.date()
                                ).days,
                                (
                                    logistic_info.delivered_date
                                    - order.estimated_delivery_time.date()
                                ).days
                                if order.estimated_delivery_time
                                else 'no estimated date',
                            )
            resp.append(description)
        else:
            resp.append('Has no orders')

        resp.append('')

        correction_invoices = list(
            chain.from_iterable(list(order.invoice_set.all()) for order in orders)
        )
        invoices_count = len(correction_invoices)
        if invoices_count > 0:
            description = ''
            description += format_html(
                'Has <strong>{}</strong> corrections:', invoices_count
            )
            for inv in correction_invoices:
                description += format_html(
                    '<br/>{}, issued at: {} with reason <strong>{}</strong><br/>',
                    inv.pretty_id,
                    inv.issued_at.date(),
                    inv.corrected_notes,
                )
            resp.append(description)
        else:
            resp.append('Has no correction invoices.')

        from django_mailer.models import Message

        emails = {order.email for order in orders}
        mails = Message.objects.filter(to_address__in=emails).order_by('date_created')
        if None in emails:
            emails.remove(None)
        emails_string = ', '.join(emails)
        resp.append(
            format_html(
                'Got <b>{}</b> emails({}). Latest one was sent <b>{}</b><br/>',
                mails.count(),
                emails_string,
                mails.last().date_created if mails else 'NEVER',
            )
        )
        resp.append('')
        try:
            type_form_count = prepare_typeform_form(orders, count_only=True)
        except Exception:
            type_form_count = 0
        if type_form_count > 0:
            resp.append(
                format_html(
                    'Has <strong>{}</strong> typeform surveys.', type_form_count
                )
            )
        else:
            resp.append(format_html('Has no typeform surveys.', type_form_count))
        self.add_dixa_conversation_links(resp, emails)
        return '<br/>'.join(resp)

    @staticmethod
    def add_dixa_conversation_links(result_list, order_emails):
        emails_in_dixa = DixaWebHookEvent.objects.filter(
            requester_email__in=order_emails
        ).values_list('requester_email', flat=True)
        for email in emails_in_dixa:
            url = mark_safe(f'https://tylko.dixa.com/contacts/user/?q={email}')  # noqa: S308
            result_list.append(
                format_html(
                    (
                        'Dixa conversations for email {}: - '
                        '<a href="{}">go to conversations</a>'
                    ),
                    email,
                    url,
                )
            )

    @property
    def is_warehouse_operator(self):
        return self.user_type == UserType.WAREHOUSE_OPERATOR

    @property
    def is_producer(self):
        return self.user_type == UserType.PRODUCER


def prepare_typeform_form(orders, count_only=False):
    type_form = []
    for key in settings.TYPEFORM_FORMS:
        form, data = get_type_form_data(key)
        for o in orders:
            form_data = get_type_form_info(data, o.pk)
            if form_data:
                type_form.append(
                    {'order': o.pk, 'key': key, 'form': form, 'data': form_data}
                )
    if count_only:
        return len(type_form)
    else:
        # let's build easier to handle dict
        return type_form


def get_type_form_info(type_form_data, order_id):
    for page in type_form_data:
        for item in page['items']:
            if 'hidden' in item:
                if int(item['hidden']['order_id']) == order_id:
                    return item['answers']


@cache_function(cache_period=7200)
def get_type_form_data(key):
    import requests

    page_size = 1000
    url = 'https://api.typeform.com/forms/{}'.format(key)
    headers = {'authorization': 'bearer {}'.format(settings.TYPEFORM_TOKEN)}
    r = requests.get(url, headers=headers)
    forms = r.json()

    url = 'https://api.typeform.com/forms/{}/responses?page_size={}'.format(
        key, page_size
    )
    headers = {'authorization': 'bearer {}'.format(settings.TYPEFORM_TOKEN)}
    r = requests.get(url, headers=headers)
    data = r.json()
    page_count = data['page_count'] if 'page_count' in data else 0
    pages = [
        data,
    ]
    if page_count > 1:
        i = 1
        while True:
            i = i + 1
            url_format = (
                'https://api.typeform.com/forms/{}/responses?page_size={}&page={}'
            )
            url = url_format.format(key, page_size, i)
            r = requests.get(url, headers=headers)
            pages.append(r.json())
            if i == page_count:
                break
    return forms, pages


# todo a candidate to be removed (isn't it rewritten to middleware?)
def create_profile(sender, **kw):
    user = kw['instance']
    if kw['created']:
        create_user_profile(user)


post_save.connect(create_profile, sender=User)


class UserLibraryManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().exclude(user_type=UserType.STAFF)


class UserLibraries(UserProfile):
    objects = UserLibraryManager()

    class Meta(object):
        proxy = True
        verbose_name = 'User libraries'
        verbose_name_plural = 'User Libraries'


class AccessToken(models.Model):
    token = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(db_index=True, null=True, blank=True)

    class Meta(object):
        abstract = True

    def is_token_valid(self):
        if (
            self.created_at + datetime.timedelta(days=7) > timezone.now()
            and self.used_at is None
        ):
            return True
        return False

    @staticmethod
    def create_token():
        return uuid.uuid4().hex


class ValidPasswordResetTokenManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                used_at__isnull=True,
                created_at__gt=timezone.now()
                - datetime.timedelta(days=settings.PASSWORD_RESET_TOKEN_EXPIRATION),
            )
        )


class PasswordResetToken(AccessToken):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    objects = models.Manager()
    objects_valid = ValidPasswordResetTokenManager()


class LoginAccessToken(AccessToken):
    VALID_FOR_WEEKS = 8
    NEW_TOKEN_CREATE_WEEKS = 4

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )

    def __str__(self):
        return self.token

    @classmethod
    def get_or_create_for_user(cls, user: User) -> str:
        if user.is_staff or user.is_superuser:
            return ''

        return cls.get_for_user(user) or cls.create_for_user(user)

    @staticmethod
    def get_for_user(user: User) -> Optional[str]:
        if login_access_token := LoginAccessToken.objects.filter(user=user).last():
            is_valid = login_access_token.is_token_valid()
            should_create_new = login_access_token.should_create_new_token()
            if is_valid and not should_create_new:
                return login_access_token.token

        return None

    @staticmethod
    def create_for_user(user: User) -> str:
        login_access_token = LoginAccessToken.create_token()
        LoginAccessToken.objects.create(user=user, token=login_access_token)
        return login_access_token

    def is_token_valid(self):
        valid_for = datetime.timedelta(weeks=self.VALID_FOR_WEEKS)
        return self.created_at + valid_for > timezone.now()

    def should_create_new_token(self):
        new_token_create_weeks = datetime.timedelta(weeks=self.NEW_TOKEN_CREATE_WEEKS)
        return timezone.now() - self.created_at < new_token_create_weeks


class LoginAccessTokenEmail24(LoginAccessToken):
    class Meta:
        proxy = True

    @staticmethod
    def create_for_user(user):
        login_access_token = LoginAccessToken.create_token()
        LoginAccessToken.objects.create(user=user, token=login_access_token)
        return login_access_token

    def is_token_valid(self):
        return (
            self.created_at + datetime.timedelta(days=settings.EMAIL24_VALID_FOR_DAYS)
            > timezone.now()
        )


class RetargetingBlacklistToken(AccessToken):
    email = models.EmailField(db_index=True, unique=True)

    def __str__(self):
        return 'RetargetingBlackListToken[email={} token={} used_at={}]'.format(
            self.email,
            self.token,
            self.used_at,
        )

    @staticmethod
    def get_or_create_for_email(email):
        rbt, c = RetargetingBlacklistToken.objects.get_or_create(  # noqa: RUF059
            email=email,
            defaults={
                'email': email,
                'token': RetargetingBlacklistToken.create_token(),
            },
        )
        return rbt

    def is_token_valid(self):
        return True


class UserProspect(models.Model):
    email = models.CharField(max_length=512, null=True, blank=True)  # noqa: DJ001
    name = models.CharField(max_length=512, null=True, blank=True)  # noqa: DJ001
    origin = models.TextField(null=True, blank=True)  # noqa: DJ001
    description = models.TextField(null=True, blank=True)  # noqa: DJ001
    event_date = models.DateTimeField(null=True, blank=True)
    was_contacted = models.BooleanField(default=False)
    prospect_source = models.IntegerField(
        choices=OrderSource.choices,
        default=OrderSource.UNKNOWN_SOURCE,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def has_account(self):
        if self.email is None:
            return False
        elif User.objects.filter(username=self.email).count() > 0:
            return True
        else:
            return False

    @classmethod
    def create_from_s4l(
        cls, email: Optional[str], description: Optional[str]
    ) -> 'UserProspect':
        return cls.objects.create(
            prospect_source=OrderSource.MOBILE_NATIVE_IOS,
            email=email,
            origin='save for later app',
            event_date=timezone.now(),
            description=description,
        )

    def __str__(self):  # noqa: DJ012
        return 'Prospect {} for {}, added {}'.format(
            self.id,
            self.email,
            self.created_at,
        )
