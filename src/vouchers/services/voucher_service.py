from operator import attrgetter
from typing import (
    TYPE_CHECKING,
    Optional,
    Union,
)

from django.db.models import Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from orders.utils import get_voucher_group_for_region
from vouchers.enums import VoucherStatusMessages
from vouchers.exceptions import MaxNumberVouchersReached, MaxUnstackableVouchersReached
from vouchers.models import Voucher
from vouchers.services.voucher_validator import VouchersValidator

if TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order


class VoucherService:
    def __init__(self, instance: Union['Order', 'Cart'], code: Optional[str]):
        self.code = code
        self.instance = instance
        self.amount_after_voucher = None

        self.region = instance.get_region()
        self.given_value = instance.get_total_price_number_before_discount()
        self.voucher_group = get_voucher_group_for_region(code, self.region)
        self.voucher = self._get_voucher()

    def process_voucher(self) -> dict[str, str]:
        if not self.voucher:
            return {
                'is_active': False,
                'status': VoucherStatusMessages.VOUCHER_CONDITIONS_ERROR,
                'message': _('There is no voucher with %(code)s code')
                % {'code': self.code},
            }
        now = timezone.now()

        filter_query = Q(active=True, start_date__lte=now, end_date__gte=now)
        if self.region:
            filter_query &= Q(configs__enabled_regions__id=self.region.id)

        voucher_promotions = self.voucher.promotion_set.all()
        voucher_active_promotions = voucher_promotions.filter(filter_query)
        if voucher_promotions and not voucher_active_promotions.exists():
            return {
                'is_active': False,
                'status': VoucherStatusMessages.VOUCHER_CONDITIONS_ERROR,
                'message': _('Promotion %(code)s is not active') % {'code': self.code},
            }

        try:
            self._apply_voucher()
        except (MaxUnstackableVouchersReached, MaxNumberVouchersReached):
            return {
                'is_active': False,
                'status': VoucherStatusMessages.VOUCHER_LIMIT,
                'message': _(
                    'No additional vouchers can be applied to this order. '
                    'Please remove the current voucher to apply a new one.'
                ),
            }
        else:
            return {
                'is_active': True,
                'status': VoucherStatusMessages.OK,
                'message': _('Promo Code accepted.'),
            }

    def remove_voucher(self) -> None:
        from carts.services.cart_service import CartService

        if not self.voucher:
            return
        cart_service = CartService(self.instance)
        cart_service.remove_voucher(voucher=self.voucher, check_vat=True)

    def _apply_voucher(self):
        from carts.services.cart_service import CartService

        cart_service = CartService(self.instance)
        cart_service.add_voucher(voucher=self.voucher, check_vat=True)

    def _get_voucher(self) -> Optional[Voucher]:
        if self.voucher_group is None:
            return self._get_default_voucher()
        for voucher_candidate in sorted(
            self.voucher_group.voucher_set.all(),
            key=attrgetter('value'),
            reverse=True,
        ):
            if VouchersValidator.check_if_voucher_applies(
                instance=self.instance, voucher=voucher_candidate
            ):
                return voucher_candidate
        return self._get_default_voucher()

    def _get_default_voucher(self) -> Optional[Voucher]:
        voucher = (
            Voucher.objects.prefetch_related('discounts')
            .filter(code__iexact=self.code)
            .first()
        )
        if voucher and VouchersValidator.check_if_voucher_applies(
            instance=self.instance, voucher=voucher
        ):
            return voucher
