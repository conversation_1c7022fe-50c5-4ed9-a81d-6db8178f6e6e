from django.utils.translation import gettext_lazy as _
from rest_framework import status
from rest_framework.exceptions import APIException

from vouchers.enums import VoucherStatusMessages


class VoucherCodeUniquenessError(Exception):
    """Raised when three trials of generating unique voucher code failed"""


class MaxUnstackableVouchersReached(Exception):  # noqa: N818
    """Raised when attempting to add an unstackable voucher to a cart or order
    that already contains another unstackable voucher.
    """


class MaxNumberVouchersReached(Exception):  # noqa: N818
    """Raised when attempting to add a voucher to a cart or order that already
    contains the maximum number of vouchers allowed.
    """


class CartNotFoundException(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = _('Cart not found.')
    default_code = 'cart_not_found'

    def __init__(self, detail=None):
        if detail is None:
            detail = {
                'is_active': False,
                'status': VoucherStatusMessages.CART_NOT_FOUND,
                'message': self.default_detail,
            }
        super().__init__(detail=detail)
