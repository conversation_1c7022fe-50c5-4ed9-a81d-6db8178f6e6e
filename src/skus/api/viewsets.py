from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.decorators import action
from rest_framework.generics import mixins
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.viewsets import GenericViewSet

from carts.exceptions import MaxCartSizeError
from carts.services.cart_service import CartService
from custom.viewsets import SellableItemViewSetInterface
from events.domain_events.marketing_events import CartUpdateEvent
from skus.api.serializers import SkuVariantSerializer
from skus.models import SkuVariant
from user_profile.decorators import create_and_login_user


class SkuVariantViewSet(
    mixins.RetrieveModelMixin,
    mixins.ListModelMixin,
    GenericViewSet,
    SellableItemViewSetInterface,
):
    swagger_tags = ['SKUs']
    permission_classes = (AllowAny,)
    authentication_classes = [
        *api_settings.DEFAULT_AUTHENTICATION_CLASSES,
        TokenAuthentication,
    ]
    queryset = SkuVariant.objects.all()
    serializer_class = SkuVariantSerializer

    @create_and_login_user(register_referer='webgl addtocart')
    @action(
        methods=['post'],
        detail=True,
    )
    def add_to_cart(self, request, *args, **kwargs):
        item = self.get_object()
        user = request.user

        cart = CartService.get_or_create_cart(user)
        try:
            CartService(cart).add_to_cart(item)
        except MaxCartSizeError:
            return Response(
                {'error': 'cart_max_size'}, status=status.HTTP_400_BAD_REQUEST
            )
        CartUpdateEvent(
            user=user,
            cart_id=cart.id,
            total_price=int(cart.total_price),
            is_sample_box=False,
        )
        del user.profile.status_response
        self.request.user.profile.clear_library_item_number_cache()
        return Response({'cart_id': cart.id}, status=status.HTTP_201_CREATED)
