from django.contrib.contenttypes.fields import GenericRelation
from django.db import models
from django.utils.translation import gettext_lazy as _

from custom.models.behaviours import Timestampable
from gallery.models.furniture_abstract import SellableItemAbstract
from skus.enums import SkuStatusChoices
from skus.models.abstracts import (
    SkuCartAbstract,
    SkuCheckoutAbstract,
)


class SkuCategory(Timestampable):
    name = models.CharField(max_length=250)

    def __str__(self):
        return self.name


class Sku(Timestampable):
    name = models.CharField(max_length=250)
    status = models.CharField(
        choices=SkuStatusChoices.choices,
        default=SkuStatusChoices.DRAFT,
        max_length=36,
    )
    sku_category = models.ForeignKey(
        SkuCategory, related_name='products', on_delete=models.CASCADE
    )

    def __str__(self):
        return f'{self.name} ({self.sku_category})'


class SkuImage(models.Model):
    image = models.ImageField(upload_to='sku_products')
    sku_variant = models.ForeignKey(
        'SkuVariant', related_name='images', on_delete=models.CASCADE
    )
    sort_order = models.IntegerField(null=True)

    class Meta:
        ordering = ['sort_order']
        indexes = [
            models.Index(fields=['sort_order'], name='sort_order_idx'),
        ]


class SkuVariant(
    Timestampable,
    SellableItemAbstract,
    SkuCartAbstract,
    SkuCheckoutAbstract,
):
    sku = models.ForeignKey(Sku, related_name='variants', on_delete=models.CASCADE)
    external_id = models.CharField(max_length=255, blank=True, null=True)  # noqa: DJ001
    name = models.CharField(max_length=255)
    translation_key = models.CharField(max_length=255)
    weight = models.FloatField(
        blank=True,
        null=True,
    )
    price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
    )

    cart_items = GenericRelation('carts.CartItem', related_query_name='sku_variant')
    order_items = GenericRelation('orders.OrderItem', related_query_name='sku_variant')

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['external_id'],
                name='unique_external_id',
                condition=models.Q(external_id__isnull=False),
            ),
        ]

    def __str__(self):
        return f'{self.name}'

    @property
    def is_sku_product(self):
        return True

    def get_item_url(self):
        return ''

    def get_delivery_time_max_week(self, *args, **kwargs):
        return 0

    def get_furniture_type(self):
        return 'skuvariant'

    def get_item_description(self) -> dict[str, str]:
        return {
            'name': _(self.translation_key),
            # TODO: material should be taken from some SKU fields
            'material': '',
            'dimensions': '',
        }
