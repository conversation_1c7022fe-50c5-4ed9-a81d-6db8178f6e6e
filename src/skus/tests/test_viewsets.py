from decimal import (
    ROUND_HALF_UP,
    Decimal,
)

from django.urls import reverse
from rest_framework import status


class TestSkuVariantViewSet:
    def test_add_to_cart(
        self,
        cart_factory,
        sku_variant_factory,
        api_client,
    ):
        cart = cart_factory(items=[])
        sku = sku_variant_factory()
        api_client.force_authenticate(user=cart.owner)
        url = reverse('sku-variants-add-to-cart', args=[sku.id])

        response = api_client.post(url, data={})

        cart.refresh_from_db()
        assert response.status_code == status.HTTP_201_CREATED
        assert cart.items.count() == 1
        assert cart.total_price == Decimal(sku.price).quantize(
            Decimal('1'), rounding=ROUND_HALF_UP
        )
