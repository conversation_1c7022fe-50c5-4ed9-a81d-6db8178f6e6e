<template>
  <main class="relative min-h-screen reset-modern-navigation-page-padding">
    <Head>
      <Title>{{ $t('hp.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        v-bind:content="$t('hp.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        v-bind:content="$t('hp.meta.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        v-bind:content="$t('hp.meta.description')"
      />
    </Head>

    <div class="relative why-tylko">
      <SectionVideoParallax
        data-section="video-hero"
        v-bind="{
          videoPlaceHolder: 'about-tylko/videoPlaceholder',
          videoParams:{
            videoId: aboutVideoId,
            embedOptions: {
              fitStrategy: 'cover',
              hasSound: false
            },
          },
          videoAspectRatio: {
            mobile: 2 / 3,
            desktop: 16 / 9
          },
          videoWrapperClass: ' after:absolute after:bottom-0 after:left-0 after:bg-gradient-to-b after:w-full after:h-1/3 after:from-transparent after:to-[#00000050] after:z-1',
          imageAlt: $t('whytylko.claim.headline'),
        }"
      >
        <BaseButton
          ref="button"
          variant="filled-dark"
          v-bind="{ trackData: {} }"
          class="absolute bottom-16 right-16 lg:bottom-[26px] lg:right-56 pointer-events-auto z-1 h-48"
          v-on="{ click: () => toggleManifestoModal(true) }"
        >
          {{ $t('whytylko.claim.cta') }}
        </BaseButton>
      </SectionVideoParallax>

      <LazySectionAnimatedBorders
        hydrate-on-visible
        v-bind="{
          bgClass: 'bg-beige-200',
          bordersBgColor: '#E7E3DF',
          headingCopy: $t('whytylko.claim.headline'),
          headerClass: 'semibold-28 md:semibold-54 pt-64 md:pt-96 xl2:pt-144',
          hideButton: true,
          usps
        }"
      />

      <UiAnimatedHeadlineSection
        data-section="section-perfect-fit"
        class="bg-beige-100"
        v-bind="{
          sectionWrapperClasses: 'relative z-1 !py-48 lg:!py-80',
          headingCopy: $t('whytylko.perfect_fit.headline'),
          subheadingCopy: $t('whytylko.perfect_fit.subheadline')
        }"
      >
        <div class="text-center grid-container md:text-left">
          <div class="overflow-hidden">
            <LazyBaseVideoWistia
              hydrate-on-visible
              class="w-full aspect-square md:aspect-video"
              v-bind="{
                videoId: {
                  desktop: 'h0nz2lu1dh',
                  mobile: 'r7jfl3xcph'
                },
                embedOptions: {
                  hasSound: false,
                }
              }"
            />
          </div>
        </div>
      </UiAnimatedHeadlineSection>
    </div>

    <UiAnimatedHeadlineSection
      data-section="section-personas"
      class="overflow-hidden py-80 bg-[#282B35]"
      v-bind="{
        headingCopy: $t('whytylko.personas.subheadline'),
        headingClasses: 'text-white',
        sectionWrapperClasses: '!py-48 lg:!py-80'
      }"
    >
      <div class="grid-container md:text-left">
        <LazyCarouselGeneric
          hydrate-on-visible
          is-dark-mode
        >
          <CarouselGenericCard
            v-for="(slide, index) in personasSlides"
            v-bind:key="`personas-${index}`"
            class="md-max:!w-[278px]"
          >
            <CardInsta
              v-bind="{
                imagePath: slide.imgPath,
                imageAlt: slide.text,
                title: slide.text
              }"
            />
          </CarouselGenericCard>
        </LazyCarouselGeneric>

        <div class="flex justify-center mt-24 lg:mt-32">
          <BaseLink
            class="min-h-[48px]"
            v-bind="{
              href: $addLocaleToPath('plp'),
              variant: 'accent',
              trackData: { eventLabel: 'cta', eventPath: $addLocaleToPath('plp') }
            }"
          >
            {{ $t('whytylko.personas.cta') }}
          </BaseLink>
        </div>
      </div>
    </UiAnimatedHeadlineSection>

    <section class="bg-beige-100 py-24">
      <ClientOnly>
        <!-- TrustBox widget - Carousel -->
        <div class="trustpilot-widget" data-locale="en-GB" data-template-id="53aa8912dec7e10d38f59f36" data-businessunit-id="59f5c4fe0000ff0005aef6e1" data-style-height="140px" data-style-width="100%" data-token="c0798adc-0b82-4c21-a5b7-1b7b1d204bd2" data-stars="1,2,3,4,5" data-review-languages="en">
          <a href="https://uk.trustpilot.com/review/tylko.com" target="_blank" rel="noopener">Trustpilot</a>
        </div>
        <!-- End TrustBox widget -->
      </ClientOnly>
    </section>

    <UiAnimatedHeadlineSection
      data-section="section-produce"
      class="overflow-hidden pt-32 pb-64 bg-beige-100"
      v-bind="{
        subheadingCopy: $t('whytylko.produce.subheadline'),
        headingCopy: $t('whytylko.produce.headline'),
        sectionWrapperClasses: '!py-48 lg:!py-80'
      }"
    >
      <div class="grid-container">
        <LazyCarouselGeneric
          hydrate-on-visible
          v-bind="{
            columnsOnDesktop: 3
          }"
        >
          <CarouselGenericCard
            v-for="(slide, index) in produceSlides"
            v-bind:key="`personas-${index}`"
            class="md-max:!w-[278px]"
          >
            <div class="!w-[278px] md:!w-auto overflow-hidden after:h-[80%] after:w-full after:absolute after:bottom-0 after:left-0">
              <BasePicture
                type="M T SD LD XLD"
                picture-classes="w-full"
                img-classes="w-full"
                v-bind="{
                  isRetinaUploaded: false,
                  alt: $t(slide.title),
                  path: slide.imgPath
                }"
              />
              <div class="absolute left-0 bottom-0 w-full px-16 py-32 flex flex-col items-start bg-gradient-to-b justify-end h-3/5 from-transparent to-[#00000080] text-white z-1">
                <div class="grid lg:w-3/4">
                  <h3
                    class="semibold-24 lg:semibold-32 max-w-[80%] lg:min-h-[90px]"
                    v-html="$t(slide.title)"
                  />
                  <p
                    class="mt-16 normal-14 lg:normal-16 lg:min-h-[90px]"
                    v-html="$t(slide.body)"
                  />
                  <p
                    class="mt-16 uppercase semibold-14 lg:semibold-18"
                    v-html="$t(slide.claim)"
                  />
                </div>
              </div>
            </div>
          </CarouselGenericCard>
        </LazyCarouselGeneric>
      </div>
    </UiAnimatedHeadlineSection>

    <UiAnimatedHeadlineSection
      data-section="section-perfect-fit"
      class="bg-beige-100 !pt-0 lg:!pt-80"
      v-bind="{
        sectionWrapperClasses: 'relative z-1',
        headingCopy: $t('whytylko.assembly.headline'),
        subheadingCopy: $t('whytylko.assembly.subheadline'),
        subheadingClasses: 'text-[#28304A]',
        headingClasses: 'text-[#28304A]',
      }"
    >
      <LazyBaseVideoWistia
        hydrate-on-visible
        class="w-full aspect-[9/16] md:aspect-video"
        v-bind="{
          videoId: {
            desktop: '3uwbx04xuj',
            mobile: 'a9nstn88lg'
          },
          embedOptions: {
            hasSound: false,
          }
        }"
      />
    </UiAnimatedHeadlineSection>

    <div class="px-16 py-48 text-white lg:py-80 bg-[#282B35]">
      <div class="grid-container">
        <div class="relative grid-cols after:bg-white/[0.38] after:h-full after:w-1 after:mx-auto after:absolute after:top-0 after:left-0 after:right-0 lg-max:after:hidden">
          <div class="col-span-12 md:col-span-8 md:col-start-3 lg:col-span-3 lg:col-start-2">
            <IconPuzzle class="w-24 h-24 text-white lg:w-32 lg:h-32" />
            <h2
              class="my-16 lg:my-24 semibold-24 lg:semibold-32"
              v-html="$t('whytylko.assembly.subheadline1')"
            />
            <p
              class="normal-16"
              v-html="$t('whytylko.assembly.usp1')"
            />
          </div>
          <hr class="col-span-12 my-24 lg:hidden opacity-[0.38]">
          <div class="col-span-12 md:col-span-8 md:col-start-3 lg:col-span-3 lg:col-start-8">
            <IconVan class="w-24 h-24 text-white lg:w-32 lg:h-32" />
            <h2
              class="my-16 lg:my-24 semibold-24 lg:semibold-32"
              v-html="$t('whytylko.assembly.subheadline2')"
            />
            <p
              class="normal-16"
              v-html="$t('whytylko.assembly.usp2')"
            />
          </div>
        </div>
      </div>
    </div>

    <UiAnimatedHeadlineSection
      data-section="section-produce"
      class="pb-32 overflow-hidden py-80 bg-beige-100"
      v-bind="{
        subheadingCopy: $t('whytylko.longevity.subheadline'),
        headingCopy: $t('whytylko.longevity.headline'),
        sectionWrapperClasses: '!py-48 lg:!py-80'
      }"
    >
      <div class="grid-container">
        <LazyCarouselGeneric
          hydrate-on-visible
          v-bind="{
            columnsOnDesktop: 3
          }"
        >
          <CarouselGenericCard
            v-for="(slide, index) in longevitySlides"
            v-bind:key="`longevity-${index}`"
            class="md-max:!w-[278px]"
          >
            <div class="!w-[278px] md:!w-auto overflow-hidden after:h-[80%] after:w-full after:absolute after:bottom-0 after:left-0">
              <BasePicture
                type="M T SD LD XLD"
                picture-classes="w-full"
                img-classes="w-full"
                v-bind="{
                  isRetinaUploaded: false,
                  alt: $t('whytylko.longevity.headline'),
                  path: slide.imgPath
                }"
              />
              <div class="absolute left-0 bottom-0 w-full px-16 py-32 flex flex-col items-start bg-gradient-to-b justify-end h-4/5 from-transparent to-[#00000080] text-white z-1">
                <div class="grid lg:w-3/4">
                  <h3
                    class="semibold-24 lg:semibold-32 max-w-[80%] lg:min-h-[90px]"
                    v-html="$t(slide.title)"
                  />
                  <p
                    class="mt-16 normal-14 lg:normal-16 lg:min-h-[90px]"
                    v-html="$t(slide.body)"
                  />
                  <p
                    class="mt-16 uppercase semibold-14 lg:semibold-18"
                    v-html="$t(slide.claim)"
                  />
                </div>
              </div>
            </div>
          </CarouselGenericCard>
        </LazyCarouselGeneric>
      </div>
    </UiAnimatedHeadlineSection>

    <div class="relative">
      <LazySectionVideoParallax
        hydrate-on-visible
        data-section="video-sustainability"
        v-bind="{
          videoAspectRatio: {
            mobile: 2 / 3,
            desktop: 16 / 9
          },
          isStatic: true,
          imageAlt: $t('whytylko.sustainability.body'),
          imagePath: 'lp/why-tylko/sustainability'
        }"
      />
      <LazySectionAnimatedBorders
        hydrate-on-visible
        v-bind="{
          bordersBgColor: '#D7E5EF',
          bgClass: 'bg-[#D7E5EF]',
          hideButton: true,
          subheadingCopy: $t('whytylko.sustainability.subheadline'),
          headingCopy: $t('whytylko.sustainability.body'),
          buttonCopy: $t('whytylko.sustainability.cta'),
          buttonCta: '/Tylko_ESG_report_digital_EN_19.pdf'
        }"
      />
    </div>

    <UiAnimatedHeadlineSection
      data-section="section-produce"
      class="pb-32 py-80 bg-beige-100"
      v-bind="{
        subheadingCopy: $t('whytylko.lobby.headline'),
        headingCopy: $t('whytylko.lobby.subheadline'),

      }"
    >
      <LazySectionCategoriesCarousel
        hydrate-on-visible
        v-bind="{
          carouselName: 'categories-carousel',
          items:furnitureCategoriesArray
        }"
      />
    </UiAnimatedHeadlineSection>

    <section class="pb-48 lg:pb-80 bg-beige-100">
      <div class="grid-container">
        <div class="grid-cols">
          <div class="col-span-12 lg:col-span-5 lg:col-start-2">
            <BasePicture
              type="M T SD LD XLD"
              picture-classes="w-[calc(100%+32px)] md:w-full md-max:-ml-16 block"
              img-classes="w-full"
              v-bind="{
                isRetinaUploaded: false,
                alt: $t('whytylko.story.headline'),
                path: 'lp/why-tylko/story'
              }"
            />
          </div>
          <div class="col-span-12 mt-8 lg:col-span-4 lg:col-start-8 lg-max:mt-32 lg:mt-0">
            <h4
              class="mb-16 uppercase semibold-14 xl:semibold-18 md:mb-24 text-neutral-800"
              v-html="$t('whytylko.story.headline')"
            />
            <p
              class="normal-12 lg:normal-24 text-neutral-900"
              v-html="$t('whytylko.story.body')"
            />
          </div>
        </div>
      </div>
    </section>

    <LazyModalFullWidth
      v-if="isManifestoModalOpen"
      v-model="isManifestoModalOpen"
      :hydrate-when="isManifestoModalOpen"
      v-bind="{
        overlayClasses: 'bg-[#1D1E1F]'
      }"
      v-on:update:model-value="toggleManifestoModal(false)"
    >
      <div class="items-center min-h-full py-24 grid-container grid-cols">
        <div class="col-span-12 md:col-span-10 md:col-start-2">
          <BaseVideoWistia
            class="w-full h-full aspect-[2/3] md:aspect-[16/9]"
            v-bind="{
              videoId: aboutLongVideoId,
              hasControls: true,
              embedOptions: {
                hasSound: true,
                hasControls: true,
                muted: false,
              },
            }"
          />
        </div>
      </div>
    </LazyModalFullWidth>
  </main>
</template>

<script setup lang="ts">
import useAboutVideoId from '~/composables/aboutTylko/useAboutVideoId';

const i18n = useI18n();

const { aboutVideoId, aboutLongVideoId } = useAboutVideoId();

const { $addLocaleToPath } = useNuxtApp();
const { pickCategoriesInOrder } = useCategories();

const resetVideo = ref<boolean>(false);
const isManifestoModalOpen = ref<boolean>(false);

const toggleManifestoModal = (value:boolean) => {
  isManifestoModalOpen.value = value;
  resetVideo.value = isManifestoModalOpen.value;
};

const produceSlides = [
  {
    title: 'whytylko.produce.title1',
    body: 'whytylko.produce.body1',
    imgPath: 'lp/why-tylko/produce/1',
    videoPath: 'video/why-tylko/produce/1',
    claim: 'whytylko.produce.subtitle1'
  },
  {
    title: 'whytylko.produce.title2',
    body: 'whytylko.produce.body2',
    imgPath: 'lp/why-tylko/produce/2',
    videoPath: 'video/why-tylko/produce/2',
    claim: 'whytylko.produce.subtitle2'
  },
  {
    title: 'whytylko.produce.title3',
    body: 'whytylko.produce.body3',
    imgPath: 'lp/why-tylko/produce/3',
    videoPath: 'video/why-tylko/produce/3',
    claim: 'whytylko.produce.subtitle3'
  }
];

const longevitySlides = [
  {
    title: 'whytylko.longevity.title1',
    body: 'whytylko.longevity.body1',
    imgPath: 'lp/why-tylko/longevity/1',
    claim: 'whytylko.longevity.subtitle1'
  },
  {
    title: 'whytylko.longevity.title2',
    body: 'whytylko.longevity.body2',
    imgPath: 'lp/why-tylko/longevity/2',
    claim: 'whytylko.longevity.subtitle2'
  },
  {
    title: 'whytylko.longevity.title3',
    body: 'whytylko.longevity.body3',
    imgPath: 'lp/why-tylko/longevity/3',
    claim: 'whytylko.longevity.subtitle3'
  }
];

const usps = [
  { title: i18n.t('whytylko.hero.usp1') },
  { title: i18n.t('whytylko.hero.usp2') },
  { title: i18n.t('whytylko.hero.usp3') },
  { title: i18n.t('whytylko.hero.usp4') }
];

const personasSlides = [
  {
    text: 'whytylko.personas.1-fittostyle',
    imgPath: 'lp/why-tylko/personas/1'
  },
  {
    text: 'whytylko.personas.2-fittospace',
    imgPath: 'lp/why-tylko/personas/3'
  },
  {
    text: 'whytylko.personas.3-fittofunction',
    imgPath: 'lp/why-tylko/personas/2'
  },
  {
    text: 'whytylko.personas.4-fittostyle',
    imgPath: 'lp/why-tylko/personas/4'
  },
  {
    text: 'whytylko.personas.5-fittospace',
    imgPath: 'lp/why-tylko/personas/5'
  },
  {
    text: 'whytylko.personas.6-fittofunction',
    imgPath: 'lp/why-tylko/personas/6'
  }
] as const;

const furnitureCategoriesArray = pickCategoriesInOrder(['sideboard', 'wallstorage', 'bookcase', 'wardrobe', 'tvstand', 'all']).map(
  ({ nameKey, name, urlPathKey }) => ({
    label: i18n.t(nameKey) as string,
    trackLabel: name,
    picturePath: `lp/why-tylko/lobby/${name}`,
    href: `${$addLocaleToPath('plp')}${i18n.t(urlPathKey) === '/' ? '' : i18n.t(urlPathKey)}`
  })
);
</script>
