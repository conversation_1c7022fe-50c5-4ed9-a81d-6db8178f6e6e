import { useToast } from 'vue-toastification';
import { useGlobal } from '~/stores/global';
import { ADD_TO_WISHLIST_BY_ID } from '~/api/wishlist';
import { REMOVE_ITEM } from '~/api/cart';
import type { CartItem } from '~/types/userStatus';

export default function () {
  const { isSignedIn, cartId } = storeToRefs(useGlobal());
  const i18n = useI18n();
  const toast = useToast();
  const { $logException } = useNuxtApp();
  const { fetchUserStatus } = useCartStatus();

  const removeItemFromCart = async (cartItemId: number, itemModel: FurnitureModel) => {
    try {
      await REMOVE_ITEM(cartId.value, cartItemId, itemModel);
      await fetchUserStatus();
    } catch (error) {
      $logException(error);
      toast.error(i18n.t('common.error.connection'));
    }
  };

  const saveItemToWishlist = async (furnitureId: number, furnitureModel: FurnitureModel) => {
    if (isSignedIn.value) {
      try {
        await ADD_TO_WISHLIST_BY_ID(furnitureId, furnitureModel);
      } catch (error) {
        $logException(error);
        toast.error(i18n.t('common.error.connection'));
      }
    }
  };

  const moveItemToWishlist = async (item: CartItem) => {
    if (isSignedIn.value) {
      await ADD_TO_WISHLIST_BY_ID(item.itemId, item.itemFurnitureType);
      await REMOVE_ITEM(cartId.value, item.itemId, item.itemFurnitureType);
      await fetchUserStatus();

      toast.success(i18n.t('scart.notify.item_removed'));
    }
  };

  return {
    removeItemFromCart,
    saveItemToWishlist,
    moveItemToWishlist
  };
}
