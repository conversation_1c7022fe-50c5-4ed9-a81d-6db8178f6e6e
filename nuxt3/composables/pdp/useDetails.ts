import { isNil } from 'lodash-es';

import { COLORS_BASIC } from '~/consts/colors';
import { getTranslatedColor } from '~/composables/useColorName';
import { type ParamKey, type Param, PARAMS_CONFIG, COLOR_CODES } from '~/consts/productPage';

export default () => {
  const { t } = useI18n();

  const setParamsValues = (data: any) => {
    return {
      width: data.width,
      height: data.height,
      depth: data.depth,
      color: !isNil(data.materialValue) ? data.materialValue : null,
      feet: (!data.longLegs && !data.plinth && data.standardFeetHeight && '8 - 18') || null,
      legs: (data.longLegs && '108') || null,
      legsCount: data.legsCount || null,
      plinth: (!data.longLegs && data.plinth && '100') || null,
      shelfMaxLoad: data.shelfMaxLoad || null,
      compartmentMaxLoad: data.compartmentMaxLoad || null,
      doors: data.doors || null,
      drawers: (!data.internalDrawers && !data.externalDrawers && data.drawers) || null,
      mergedDrawers: data.drawerMerge || null,
      internalDrawers: data.internalDrawers || null,
      externalDrawers: data.externalDrawers || null,
      cableOpening: data.cableManagement || null,
      additionalStorage: data.additionalUpperStorage || data.additionalBottomStorage || null,
      hangingRail: ('barSubtypes' in data && (data.barSubtypes === 'z'
        ? 'pdp.product_details.params.hanging_rail.value.front_facing'
        : 'pdp.product_details.params.hanging_rail.value.standard')) || null,
      lighting: data.lighting || null,
      backpanels: !!data.backpanels || !!data.backPanels || null,
      overheadStorage: data.overheadStorage || null,
      topBoardSeams: (data.width > 240 && data.height > 63 && data.topBoardSeamsCount) || null,
      distanceFromTheFloor: data.shelfType === 6 ? '23 - 31' : data.shelfType === 8 ? '100 - 110' : null,
      furniturePlacement: data.shelfType === 6 || data.shelfType === 8 ? 'pdp.product_details.params.floor' : data.shelfType === 7 ? 'pdp.product_details.params.wall' : null,
      feetLevellers: data.shelfType === 6 ? '8' : data.shelfType === 8 ? '12' : null,
      internalShelfMaxLoad: data.shelfType === 7
        ? (data.width / data.columnCount) >= 601
            ? '10'
            : '15'
        : data.shelfType === 6 || data.shelfType === 8
          ? data.depth === 36
            ? '20'
            : '30'
          : null,
      countertopMaxLoad: data.shelfType === 7 && data.columnCount
        ? (data.width / data.columnCount) >= 601
            ? 10 * data.columnCount
            : 15 * data.columnCount
        : (data.shelfType === 6 || data.shelfType === 8) && data.columnCount
            ? 30 * data.columnCount
            : null,
      drawerMaxLoad: data.shelfType === 6 || data.shelfType === 8 ? '30' : data.shelfType === 7 ? '10' : null
    };
  };

  const getParamValue = (data: any, key: keyof typeof PARAMS_CONFIG) => {
    const paramValue = data[key];
    const paramType = PARAMS_CONFIG[key].type;

    if (paramType === 'color') { return paramValue; }
    if (paramType === 'count') { return `x ${paramValue}`; }
    if (paramType === 'measurement') { return `${paramValue} ${PARAMS_CONFIG[key].unit}`; }
    if (paramType === 'yes/no') { return (paramValue && t('pdp.product_details.params.yes')) || null; }
    if (typeof paramValue === 'string') { return t(paramValue); }

    return paramValue;
  };

  const getAdditionalTextKey = (data: any, config: Param['additionalText']) => {
    if (!config) { return null; }

    if (typeof config === 'string') { return config; }

    let label: string | undefined;
    const configKeys = Object.keys(config) as ParamKey[];

    for (let i = 0; i < configKeys.length; i++) {
      const key = configKeys[i];

      if (data[key]) {
        label = config[key];
        break;
      }
    }

    return label || null;
  };

  const ralCodes = (colorInfo: any) => {
    if (!colorInfo.value) { return null; };

    let colorKeys = colorInfo.value.key!;

    if (typeof colorKeys === 'string') {
      colorKeys = [colorKeys];
    }

    return colorKeys.map((key: any) => {
      const colorCodeInfo = COLOR_CODES[key];

      if ('custom' in colorCodeInfo) {
        return {
          id: key,
          translationKey: colorCodeInfo.custom
        };
      }

      const colorTranslation = getTranslatedColor(COLORS_BASIC[key].label);

      if ('ral' in colorCodeInfo && 'ncs' in colorCodeInfo) {
        return {
          id: key,
          translationKey: 'pdp.product_details.params.color.ral_codes.ral_with_ncs',
          values: {
            color: colorTranslation,
            ralCode: colorCodeInfo.ral,
            ncsCode: colorCodeInfo.ncs
          }
        };
      }

      if ('ral' in colorCodeInfo) {
        return {
          id: key,
          translationKey: 'pdp.product_details.params.color.ral_codes.ral_only',
          values: {
            color: colorTranslation,
            ralCode: colorCodeInfo.ral
          }
        };
      }

      if ('ncs' in colorCodeInfo) {
        return {
          id: key,
          translationKey: 'pdp.product_details.params.color.ral_codes.ncs_only',
          values: {
            color: colorTranslation,
            ncsCode: colorCodeInfo.ncs
          }
        };
      }

      return null;
    });
  };

  const materialData = (variant: string, material?: any, finish?: any) => {
    switch (variant) {
      case 'constructionText':
        return {
          title: 'pdp.product_details.material.construction.title',
          text: `pdp.product_details.material.construction.body.${material}`
        };

      case 'edgeConstructionText':
        return {
          title: 'pdp.product_details.material.construction.title',
          text: `pdp.product_details.material.construction.body.finish.${finish}.edge`
        };

      case 'toneShelvesConstructionText':
        return {
          title: 'pdp.product_details.material.construction.title',
          text: `pdp.product_details.material.construction.body.tone-shelves.${finish}`
        };

      case 'finishText':
        return {
          title: 'pdp.product_details.material.construction.finish.title',
          text: `pdp.product_details.material.finish.body.${material}`,
          imagePath: true
        };

      case 'premiumMatteBlackFinishText':
        return {
          title: 'pdp.product_details.material.construction.finish.title',
          text: 'pdp.product_details.material.finish.premium_matte_black'
        };

      case 'edgeFinishText':
        return {
          title: 'pdp.product_details.material.construction.finish.title',
          text: `pdp.product_details.material.finish.body.finish.${finish}.edge`
        };

      case 'toneShelvesFinishText':
        return {
          title: 'pdp.product_details.material.construction.finish.title',
          text: 'pdp.product_details.material.finish.body.tone-shelves'
        };

      case 'deskFinishText':
        return {
          title: 'pdp.product_details.material.construction.finish.title',
          text: 'pdp.product_details.material.finish.body.desk'
        };

      case 'qualityAndFeelText':
        return {
          title: 'pdp.product_details.material.quality_and_feel.title',
          text: `pdp.product_details.material.quality_and_feel.body.${material}`
        };

      case 'edgeQualityAndFeelText':
        return {
          title: 'pdp.product_details.material.quality_and_feel.title',
          text: `pdp.product_details.material.quality_and_feel.body.finish.${finish}.edge`
        };

      case 'toneShelvesFittingsText':
        return {
          title: 'pdp.product_details.fittings.title.tone-shelves',
          text: 'pdp.product_details.fittings.body.tone-shelves'
        };
    }
  };

  const materialImage = (colorName: string, material: string) => {
    if (material !== 'veneer') { return null; }

    return `pdp/product-details/finish/01v/${colorName}`;
  };

  const detailsData = (variant: string, optionalUrl?: string) => {
    switch (variant) {
      case 'smartAndSafeText':
        return {
          title: 'pdp.product_details.delivery_and_assembly.smart_and_safe_delivery.title',
          text: 'pdp.product_details.delivery_and_assembly.smart_and_safe_delivery.body'
        };

      case 'edgeSmartAndSafeText':
        return {
          title: 'pdp.product_details.delivery_and_assembly.smart_and_safe_delivery.edge.title',
          text: 'pdp.product_details.delivery_and_assembly.smart_and_safe_delivery.edge.body'
        };

      case 'toneShelfOrderText':
        return {
          title: 'pdp.product_details.sustainability.order.title.tone-shelves',
          text: 'pdp.product_details.sustainability.order.body.tone-shelves'
        };

      case 'insidePackageText':
        return {
          title: 'pdp.product_details.delivery_and_assembly.package.title',
          text: 'pdp.product_details.delivery_and_assembly.package.body'
        };

      case 'edgeInsidePackageText':
        return {
          title: 'pdp.product_details.delivery_and_assembly.package.edge.title',
          text: 'pdp.product_details.delivery_and_assembly.package.edge.body'
        };
      case 'toneShelfProducedText':
        return {
          title: 'pdp.product_details.sustainability.produced.title.tone-shelves',
          text: 'pdp.product_details.sustainability.produced.body.tone-shelves'
        };

      case 'deliveryAndAssemblyText':
        return {
          title: 'pdp.product_details.delivery_and_assembly.package.title',
          text: 'pdp.product_details.delivery_and_assembly.package.body'
        };

      case 'toneShelfCardboardText':
        return {
          title: 'pdp.product_details.sustainability.cardboard.title.tone-shelves',
          text: 'pdp.product_details.sustainability.cardboard.body.tone-shelves'
        };

      case 'edgeSimpleClickText':
        return {
          title: 'pdp.product_details.delivery_and_assembly.simple_click.edge.title',
          text: 'pdp.product_details.delivery_and_assembly.simple_click.edge.body'
        };

      case 'toneShelfDesignText':
        return {
          title: 'pdp.product_details.sustainability.design.title.tone-shelves',
          text: 'pdp.product_details.sustainability.design.body.tone-shelves'
        };

      case 'optionalAssemblyText':
        return {
          title: 'pdp.product_details.delivery_and_assembly.optional_assembly.title',
          text: 'pdp.product_details.delivery_and_assembly.optional_assembly.body',
          link: {
            text: 'pdp.product_details.delivery_and_assembly.optional_assembly.link',
            url: optionalUrl
          },
          additionalText: 'pdp.product_details.delivery_and_assembly.optional_assembly.small_text'
        };

      case 'edgeOptionalAssemblyText':
        return {
          title: 'pdp.product_details.delivery_and_assembly.optional_assembly.edge.title',
          text: 'pdp.product_details.delivery_and_assembly.optional_assembly.edge.body',
          link: {
            text: 'pdp.product_details.delivery_and_assembly.optional_assembly.edge.link',
            url: optionalUrl
          },
          additionalText: 'pdp.product_details.delivery_and_assembly.optional_assembly.edge.small_text'
        };

      case 'assemblyText':
        return {
          title: 'pdp.product_details.assembly_service_included.free_assembly.title',
          text: 'pdp.product_details.assembly_service_included.free_assembly.body',
          link: {
            text: 'pdp.product_details.assembly_service_included.free_assembly.link',
            url: optionalUrl
          }
        };

      case 'toneShelfPartsText':
        return {
          title: 'pdp.product_details.sustainability.parts.title.tone-shelves',
          text: 'pdp.product_details.sustainability.parts.body.tone-shelves'
        };
    }
  };

  const safetyData = (variant: string, optionalUrl?: string, categoryTranslation?: string) => {
    switch (variant) {
      case 'kidFriendlyText':
        return {
          title: 'pdp.product_details.safety.kid_friendly.title',
          text: 'pdp.product_details.safety.kid_friendly.body'
        };

      case 'edgeKidFriendlyText':
        return {
          title: 'pdp.product_details.safety.kid_friendly.edge.title',
          text: 'pdp.product_details.safety.kid_friendly.edge.body'
        };

      case 'wallSecuringText':
        return {
          title: 'pdp.product_details.safety.wall_securing.title',
          text: 'pdp.product_details.safety.wall_securing.body.shelf',
          link: {
            text: 'pdp.product_details.safety.wall_securing.link',
            url: optionalUrl
          }
        };

      case 'edgeWallSecuringText':
        return {
          title: 'pdp.product_details.safety.wall_securing.title',
          text: 'pdp.product_details.sefety.wall_securing.edge.body',
          link: {
            text: 'pdp.product_details.safety.wall_securing.link',
            url: optionalUrl
          }
        };

      case 'toneShelvesWallSecuringText':
        return {
          title: 'pdp.product_details.safety.wall_securing.title',
          text: 'pdp.product_details.sefety.wall_securing.tone-shelves.body'
        };

      case 'wardrobeWallSecuringText':
        return {
          title: 'pdp.product_details.safety.wall_securing.title',
          text: 'pdp.product_details.sefety.wall_securing.body.tone'
        };

      case 'wallHangingText':
        return {
          title: 'pdp.product_details.safety.wall_hanging.title',
          text: 'pdp.product_details.safety.wall_hanging.body',
          link: {
            text: 'pdp.product_details.safety.wall_hanging.link',
            url: optionalUrl
          }
        };

      case 'toneShelvesWallHangingText':
        return {
          title: 'pdp.product_details.sefety.wall_hanging.tone-shelves.title',
          text: 'pdp.product_details.sefety.wall_hanging.tone-shelves.body'
        };

      case 'roomDividerText':
        return {
          title: 'pdp.product_details.safety.room_divider.title.shelf',
          text: 'pdp.product_details.safety.room_divider.body.shelf'
        };

      case 'edgeRoomDividerText':
        return {
          title: t('pdp.product_details.sefety.room_divider.title.category', { category: categoryTranslation }),
          text: t('pdp.product_details.sefety.room_divider.body.category', { category: categoryTranslation })
        };

      case 'wardrobeRoomDividerText':
        return {
          title: 'pdp.product_details.sefety.room_divider.title.wardrobe',
          text: 'pdp.product_details.sefety.room_divider.body.wardrobe'
        };

      case 'safetyManualText':
        return {
          title: 'pdp.product_details.sefety.safety_manual.title',
          text: 'pdp.product_details.sefety.safety_manual.body',
          link: {
            text: 'pdp.product_details.sefety.safety_manual.link',
            url: optionalUrl
          }
        };
    }
  };

  return {
    ralCodes,
    safetyData,
    detailsData,
    materialData,
    materialImage,
    getParamValue,
    setParamsValues,
    getAdditionalTextKey
  };
};
