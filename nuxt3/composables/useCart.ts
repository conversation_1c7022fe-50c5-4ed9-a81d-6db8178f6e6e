import { useToast } from 'vue-toastification';
import { isNil } from 'lodash-es';
import { useGlobal } from '~/stores/global';
import { useApi } from '~/composables/useApi';
import { useScartStore } from '~/stores/scart';
import { type PRODUCT_TYPE_KEYS, TYPES } from '~/consts/types';
import type { EcoTaxResponse } from '~/types/cart';
import { CREATE_CART, ECO_TAX } from '~/api/cart';
import type { CartItem } from '~/types/userStatus';
import useColors from '~/composables/useColors';
import { getTranslatedColor } from '~/composables/useColorName';
import { ADD_TO_CART_PRESET } from '~/api/pdp';

export default function () {
  const { $logException, $i18n } = useNuxtApp();
  const cart = useScartStore();
  const global = useGlobal();
  const { cartId } = storeToRefs(global);
  const { hasAssemblyPossible, whiteGlovesDeliveryPossible } = storeToRefs(cart);
  const toast = useToast();
  const { fetchUserStatus, openSidebarCart } = useCartStatus();

  const transformToJettyWatty = (itemFurnitureType: string) => {
    if (itemFurnitureType === 'ivy') {
      return 'jetty';
    } else if (itemFurnitureType === 'wardrobe') {
      return 'watty';
    } else {
      return itemFurnitureType;
    }
  };

  const removeItem = async (id: number, type: any) => {
    const { error } = await useApi(`/api/v2/cart/${global.cartId}/delete_from_cart/`, {
      method: 'post',
      body: {
        sellable_item_id: id,
        content_type: transformToJettyWatty(type)
      }
    });

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(error.value);
      return;
    }

    await fetchUserStatus();

    toast.success($i18n.t('scart.notify.item_removed'));
  };

  const handleAssembly = async (add: Boolean) => {
    const { error } = await useApi(`/api/v2/cart/${global.cartId}/change_assembly/`, {
      method: 'post',
      body: {
        assembly: add
      }
    });

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(error.value);
      return;
    }

    await fetchUserStatus();
  };

  const handleWhiteGlovesDelivery = async (add: Boolean) => {
    const { error } = await useApi(`/api/v2/cart/${global.cartId}/change_delivery_services/`, {
      method: 'post',
      body: {
        ...(hasAssemblyPossible.value && {
          assembly: add
        }),
        ...(whiteGlovesDeliveryPossible.value && {
          white_gloves_delivery: add
        })
      }
    });

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(error.value);
      return;
    }

    await fetchUserStatus();
  };

  const handlePromo = async (promocode = '') => {
    const { data, error } = await useApi(`/api/v1/check_promo/${promocode ? `${promocode}/` : ''}`, {
      method: 'post'
    });

    if (error.value) {
      toast.error($i18n.t(promocode ? 'scart.notify.promo_invalid' : 'common.error.connection'));
      $logException(error.value);
    } else if (data.value) {
      if (promocode === '') {
        await fetchUserStatus();
      } else {
        cart.PROMOCODE_UPDATE({ ...data.value, promocode });
      }

      toast.success($i18n.t(promocode ? 'scart.notify.promo_added' : 'scart.notify.assembly_removed'));
    }

    return data.value;
  };

  const { getItemName } = CART_ITEM_NAME();
  const { getColor } = useColors();
  const isItFurniture = (cartItem: CartItem) => cartItem.itemFurnitureType !== 'sample_box';
  const isItSampleBox = (cartItem: CartItem) => cartItem.itemFurnitureType === 'sample_box';

  const getCartItemColor = (cartItem: CartItem) => {
    if (isItFurniture(cartItem)) {
      return getColor(cartItem.shelf_type, cartItem.material)?.nameKey;
    } else if (isItSampleBox(cartItem)) {
      return getColor(cartItem.shelf_type, cartItem.color)?.nameKey;
    }
  };

  const getCartItemName = (cartItem: CartItem) =>
    isItFurniture(cartItem)
      ? getItemName(cartItem.itemFurnitureType, cartItem.pattern_name, cartItem.category, cartItem.shelf_type)
      : $i18n.t(getSampleBoxNameKey(cartItem.shelf_type));// $i18n.t(cartItem.name_key);

  const getCartItemColorName = (cartItem: CartItem) => {
    const color = isItFurniture(cartItem)
      ? getColor(cartItem.shelf_type, cartItem.material)?.nameKey
      : (cartItem?.name_key || cartItem?.pattern_name || cartItem?.itemDescriptionMaterial);

    return getTranslatedColor(color, ' / ');
  };

  const getCartItemImage = (cartItem: CartItem) => {
    if (isItFurniture(cartItem)) {
      return cartItem?.itemImageUrl;
    } else if (isItSampleBox(cartItem)) {
      return `lp/sample/sets/${cartItem.variant_type}`;
    }
  };

  const createCart = async () => {
    const { data, error } = await CREATE_CART();

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(error.value);
    } else if (data.value?.id) {
      cartId.value = data.value.id;
    }
  };

  const isCartIdValid = () => !isNil(cartId.value) && cartId.value !== '';

  const getCartId = async () => {
    if (!isCartIdValid()) {
      await createCart();
    }

    return cartId.value && parseInt(cartId.value);
  };

  const handelAddToCartPreset = async (productId: string, model: string, quantity: number = 1) => {
    await getCartId();

    if (!isCartIdValid()) {
      toast.error($i18n.t('common.error.connection'));
      $logException(`Error: handelAddToCartPreset - cartId is not valid. Unable to add item to cart. productId: ${productId},model ${model}`);
    } else {
      try {
        await ADD_TO_CART_PRESET(productId, model, cartId.value, quantity);
        await openSidebarCart();
      } catch (error) {
        toast.error($i18n.t('common.error.connection'));
        $logException(error.value);
      }
    }
  };

  return {
    removeItem,
    handleAssembly,
    handlePromo,
    handelAddToCartPreset,
    isItFurniture,
    isItSampleBox,
    getCartItemName,
    getCartItemColorName,
    getCartItemColor,
    getCartItemImage,
    handleWhiteGlovesDelivery
  };
}

const types = TYPES();

export const CART_ANALYTICS = (eventCategory: string = 'cart_edit') => {
  const cartEvent = (eventAction: string, eventLabel: number | string = '') => ({
    eventCategory,
    eventAction,
    eventLabel: `${eventLabel}`
  });

  return {
    cartEvent
  };
};

export const CART_ITEM_NAME = () => {
  const { t } = useI18n();
  const { $logException } = useNuxtApp();

  const getItemName = (itemFurnitureType: PRODUCT_TYPE_KEYS, patternName: string, category: string, shelfType: string | number) => {
    if (itemFurnitureType === 'sample_box') {
      const item = types.samplebox[patternName];

      return t(item?.nameKey);
    }

    if (itemFurnitureType === 'skuvariant') {
      return 'skuvariant';
    }

    const item: any = Object.values(types[itemFurnitureType]).find((type: any) => type.shelfType === +shelfType);

    const typeKey = item?.nameKey;
    const categoryKey = item?.categories[category]?.nameKey;

    if (typeKey && categoryKey) {
      return shelfType === 10 ? `${t(categoryKey)}` : `${t(typeKey)} ${t(categoryKey)}`;
    } else {
      $logException(`Item name not recognized - type: ${typeKey}, categoryKey: ${categoryKey}`);

      return '-';
    }
  };

  return {
    getItemName
  };
};

export const handleRecycleTax = () => {
  const ecoTaxModalData = ref<EcoTaxResponse | null>(null);
  const isRecycleTaxModalOpen = ref(false);
  const isPendingState = ref(false);
  const { $i18n } = useNuxtApp();
  const toast = useToast();
  const { cartId } = storeToRefs(useGlobal());

  const showRecycleTaxModal = async () => {
    if (!cartId.value) { return; }
    isPendingState.value = true;
    const { data, error } = await ECO_TAX(cartId.value as string);

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      console.error('Error while fetching recycle tax value', error);
    } else if (data.value) {
      ecoTaxModalData.value = data.value;
      isRecycleTaxModalOpen.value = true;
      isPendingState.value = false;
    }
  };

  return {
    ecoTaxModalData,
    isRecycleTaxModalOpen,
    showRecycleTaxModal,
    isPendingState
  };
};

const sampleNameKeysByShelfType = {
  0: 'lp.samples.sets.original-classic.subtitle',
  1: 'lp.samples.sets.original-modern.subtitle',
  2: 'lp.samples.sets.original-classic.subtitle',
  3: 'lp.samples.sets.tone-wardrobes.subtitle',
  4: 'lp.samples.sets.edge.title',
  5: 'lp.samples.sets.edge.title',
  6: 'lp.samples.sets.tone-shelving.subtitle',
  7: 'lp.samples.sets.tone-shelving.subtitle',
  8: 'lp.samples.sets.tone-shelving.subtitle',
  10: 'lp.samples.sets.sofas.title'
};

function getSampleBoxNameKey (shelfType: SHELF_TYPE) {
  return sampleNameKeysByShelfType[shelfType];
}
