const dev = process.env.NODE_ENV !== 'production';
const staticPath = '/nuxt3-statics';
const fontFiles = [
  '/webfonts/MessinaSansWeb-Bold.woff',
  '/webfonts/MessinaSansWeb-SemiBold.woff',
  '/webfonts/MessinaSansWeb-Regular.woff',
  '/webfonts/MessinaSansWeb-Light.woff',
  '/webfonts/MessinaSansWeb-Bold.woff2',
  '/webfonts/MessinaSansWeb-SemiBold.woff2',
  '/webfonts/MessinaSansWeb-Regular.woff2',
  '/webfonts/MessinaSansWeb-Light.woff2'
];
const faviconDir = `${staticPath}/${dev ? 'dev' : 'prod'}`;

const head = {
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
    { name: 'theme-color', content: '#ffffff' },

    // Google
    { name: 'google', value: 'notranslate' },

    // Pinterest
    { name: 'p:domain_verify', content: 'af453366b8129efaca68bf9c659fa83f' },
    { name: 'p:domain_verify', content: '64f280ea528bae6144728bdb8f2dcd19' },
    { name: 'p:domain_verify', content: 'af453366b8129efaca68bf9c659fa83f' },

    // Twitter
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:site', content: '@tylko_furniture' },
    { name: 'twitter:creator', content: '@tylko_furniture' },

    // Facebook
    { property: 'fb:app_id', content: '739339722848856' },

    // Open graph defaults
    { hid: 'og:image', name: 'og:image', content: 'https://tylko.com/r_static/share-preview-images/tylko-meta-preview-image.jpg' },

    //  https://nuxtjs.org/docs/2.x/concepts/server-side-rendering#use-a-meta-tag-to-stop-the-transformation
    { name: 'format-detection', content: 'telephone=no' },

    // Skip auto generated package.json variables
    { name: 'og:site_name', skip: true },
    { name: 'apple-mobile-web-app-title', skip: true }
  ],
  link: [
    ...fontFiles.map(file => ({
      rel: 'preload',
      href: `${staticPath}${file}`,
      as: 'font',
      type: 'font/woff',
      crossorigin: 'anonymous'
    })),
    { rel: 'apple-touch-icon', href: `${faviconDir}/180x180.png` },
    { rel: 'icon', type: 'image/x-icon', href: `${faviconDir}/favicon.ico` },
    { rel: 'icon', type: 'image/png', sizes: '16x16', href: `${faviconDir}/16x16.png` },
    { rel: 'icon', type: 'image/png', sizes: '32x32', href: `${faviconDir}/32x32.png` },
    { rel: 'icon', type: 'image/png', sizes: '96x96', href: `${faviconDir}/96x96.png` },
    { rel: 'icon', type: 'image/png', sizes: '192x192', href: `${faviconDir}/192x192.png` }
  ],
  script: [
    {
      hid: 'focus-polyfill',
      src: 'https://cdn.jsdelivr.net/npm/focus-visible@5.2.1/dist/focus-visible.min.js',
      defer: true
    },
    {
      hid: 'pubsub',
      src: 'https://cdnjs.cloudflare.com/ajax/libs/pubsub-js/1.9.5/pubsub.min.js',
      defer: true
    },
    {
      hid: 'use-berry-script',
      src: 'https://api.useberry.com/integrations/liveUrl/scripts/useberryScript.js',
      key: 'use-berry-scxript',
      defer: true,
      fetchPriority: 'low'
    },
    {
      hid: 'trustpilot-bootstrap',
      src: 'https://widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js',
      key: 'trustpilot-bootstrap',
      defer: true
    },
    {
      textContent: 'window.__NUXT__.path = window.location.pathname + window.location.search',
      tagPosition: 'bodyClose'
    }
  ]
};

export default head;
