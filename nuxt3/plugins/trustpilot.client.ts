export default defineNuxtPlugin((nuxtApp) => {
  const loadAllTrustpilotWidgets = (): void => {
    if (typeof window === 'undefined') { return; }
    const trustpilot: any = (window as any).Trustpilot;
    if (!trustpilot) { return; }

    const widgetElements = document.querySelectorAll('.trustpilot-widget');
    widgetElements.forEach((element) => {
      try {
        trustpilot.loadFromElement(element as Element, true);
      } catch {}
    });
  };

  const startObserver = (): void => {
    if (typeof window === 'undefined') { return; }
    const trustpilot: any = (window as any).Trustpilot;
    if (!trustpilot) { return; }

    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type !== 'childList') { continue; }
        mutation.addedNodes.forEach((node) => {
          if (!(node instanceof Element)) { return; }

          if (node.classList?.contains('trustpilot-widget')) {
            try {
              trustpilot.loadFromElement(node as Element, true);
            } catch {}
          } else {
            const nested = node.querySelector?.('.trustpilot-widget');

            if (nested) {
              try {
                trustpilot.loadFromElement(nested as Element, true);
              } catch {}
            }
          }
        });
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });
  };

  const scheduleLoad = (attemptsRemaining = 10): void => {
    if (typeof window === 'undefined') { return; }

    if ((window as any).Trustpilot) {
      setTimeout(loadAllTrustpilotWidgets, 0);
      startObserver();

      return;
    }

    if (attemptsRemaining <= 0) { return; }
    setTimeout(() => scheduleLoad(attemptsRemaining - 1), 300);
  };

  nuxtApp.hook('app:mounted', () => {
    scheduleLoad();
  });

  nuxtApp.hook('page:finish', () => {
    scheduleLoad();
  });
});
