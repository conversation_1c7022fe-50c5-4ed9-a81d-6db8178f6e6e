<template>
  <div ref="promocodeInput">
    <Transition
      name="slide-fade"
      mode="out-in"
    >
      <div
        v-if="cart.vouchers?.length"
        :class="shourldDsisplayPromocodeInput ? 'pb-[21px]' : ''"
      >
        <div
          v-for="(voucher, index) in cart.vouchers"
          :key="`voucher-${index}`"
          class="flex items-center justify-between mb-12"
          :class="shourldDsisplayPromocodeInput ? 'last:!mb-0' : ''"
        >
          <div
            class="flex items-center"
          >
            <BaseButton
              v-if="displayPromoCodeRemoveButton"
              variant="custom"
              type="button"
              class="box-content"
              style="--spinner-color: #1D1E1F;"
              v-bind="{
                trackData: {},
                pending: loadingButtons[index],
                'data-testid': 'cart-promocode-remove'
              }"
              v-on="{ click: () => removePromocode(voucher.code, index) }"
            >
              <template #icon>
                <div class="rounded-full bg-neutral-900 w-24 h-24">
                  <IconPlus class="w-[24px] h-[24px] mr-8 text-black transform rotate-45 text-white" />
                </div>
              </template>
            </BaseButton>
            <div class="ml-16">
              <span
                class="normal-16 text-neutral-900 py-16"
                v-html="voucher.is_gift_card ? $t('cart_discount_card_caption') : $t('checkout.code_name')"
              />:
              <span
                class="ml-4 text-neutral-900 uppercase"
                data-testid="promo-code-applied"
                v-html="voucher.code"
              />
            </div>
          </div>
          <span>-{{ format(voucher.value) }}</span>
        </div>
      </div>
    </Transition>
    <FormKit
      v-if="shourldDsisplayPromocodeInput"
      v-model="formValues"
      type="form"
      v-bind="{
        config: { validationVisibility: 'submit' },
        disabled: loading,
        actions: false,
        incompleteMessage: false
      }"
      v-on:submit="submitForm"
    >
      <FormKit
        id="cart-promocode"
        type="tyText"
        data-testid="cart-promocode-input"
        name="promocode"
        v-bind="{
          label: $t('checkout_discount.code_box'),
          validationMessages:{
            required: $t('scart.notify.promo_invalid'),
          }
        }"
        clear-class="hidden"
        inner-class="flex"
        autocomplete="off"
      >
        <template #button>
          <BaseButton
            variant="accent"
            class="rounded-4 semibold-16 -m-[1px] relative"
            type="submit"
            data-testid="cart-submit-promocode"
            v-bind="{
              disabled: loading ,
              trackData: {},
            }"
          >
            <UiDotsLoader
              v-if="loading"
              class="[&.spinner>div]:!h-[10px] [&.spinner>div]:!w-[10px] [&.spinner]:!space-x-2"
              bounce-class="bg-white"
            />
            <span :class="{ 'invisible' : loading }">
              {{ $t('scart.promocode_apply') }}
            </span>
          </BaseButton>
        </template>
      </FormKit>
    </FormKit>
  </div>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toastification';
import { CART_ANALYTICS } from '~/composables/useCart';
import { useTrackInteraction } from '~/composables/useTracking';
import { useScartStore } from '~/stores/scart';
import usePrice from '~/composables/usePrice';

const props = defineProps({
  displayPromoCodeRemoveButton: {
    type: Boolean,
    default: true
  },
  eventCategory: {
    type: String,
    required: true
  },
  disablePromocodeAddition: {
    type: Boolean,
    default: false
  }
});

const formValues = ref({ promocode: '' });
const loading = ref(false);
const toast = useToast();
const cart = useScartStore();
const { format } = usePrice();
const { cartEvent } = CART_ANALYTICS(props.eventCategory);
const { $i18n } = useNuxtApp();
const { fetchUserStatus } = useCartStatus();
const promocodeInput = ref(null);
const loadingButtons = reactive(cart.vouchers?.reduce((acc, val, index) => {
  return { ...acc, [index]: false };
}, {}));
const trackInteraction = useTrackInteraction(promocodeInput);

const isPromocodeInputVisible = ref(false);

const submitForm = async (_, node) => {
  try {
    const result = await handlePromo(formValues.value.promocode);
    await fetchUserStatus();

    result?.status === 'ok' && trackInteraction(cartEvent('AddPromocode'));
    hendleResultMessage(result, node);
    formValues.value.promocode = '';
  } catch (e) {
    hendleResultMessage({ message: $i18n.t('common.error.connection') }, node);
  }
};

const shourldDsisplayPromocodeInput = computed(() => {
  return cart.vouchers?.length < 2;
});

const handlePromo = async (promocode = '') => {
  return await $fetch<{status: string, message:string}>(`/api/v1/check_promo/${promocode ? `${promocode}/` : ''}`, {
    method: 'post',
    ignoreResponseError: true
  });
};

const removePromo = async (promocode: string) => {
  return await $fetch<{status: string, message:string}>(`/api/v1/remove_promo/${promocode}/`, {
    method: 'post',
    ignoreResponseError: true
  });
};

const removePromocode = async (code, buttonIndex) => {
  loadingButtons[buttonIndex] = true;

  try {
    const result = await removePromo(code);
    result?.status === 'ok' && trackInteraction(cartEvent('RemovePromocode'));
    hendleResultMessage(result);
    await fetchUserStatus();
  } catch (e) {
    hendleResultMessage();
  } finally {
    loadingButtons[buttonIndex] = false;
  }

  isPromocodeInputVisible.value = false;
  formValues.value.promocode = '';
};

const hendleResultMessage = (result?: {status?: string, message?: string}, node?: any) => {
  if (result?.message) {
    node?.setErrors(result?.message);
  } else {
    toast.error($i18n.t('common.error.connection'));
  }
};
</script>

<style lang="scss">
.formkit-messages {
  margin-top: 8px;
}

.formkit-message {
  padding-left: 0;
}
</style>
