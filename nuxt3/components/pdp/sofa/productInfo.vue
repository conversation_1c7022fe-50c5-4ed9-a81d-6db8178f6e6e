<template>
  <div class="relative bg-white lg:rounded-12 w-full p-16 md:p-32 lg:px-24 lg:py-16 lg2:py-24 xl:px-48 xl:py-32">
    <BaseButton
      variant="custom"
      class="absolute lg-max:right-16 lg-max:-top-64 lg:-left-64 xl:-left-80 lg:top-0
              bg-beige-100/90 hover:bg-beige-100 transition-all basic-transition
              shadow-icon rounded-full cursor-pointer w-48 h-48 [--spinner-color:#1D1E1F]"
      data-testid="s4l-heart-button"
      v-bind="{
        disabled: !isWishlistButtonActive,
        pending: isWishlistLoading,
        trackData: {
          eventCategory: 'pdp_smooth',
          eventAction: 'S4L',
          eventLabel: 'S4L'
        }
      }"
      v-on="{ click: handleS4L }"
    >
      <template #icon>
        <IconHeart
          class="w-24 h-24 m-12"
          data-testid="s4l-heart-icon"
          :class="!isWishlistButtonActive && 'text-orange fill-orange'"
        />
      </template>
    </BaseButton>
    <div class="flex justify-between items-center">
      <span class="semibold-12 text-success-500 uppercase">{{ $t('plp.board.product_card.label.new_arrival') }}</span>
      <span
        class="flex justify-between items-center"
        data-testid="review-score"
      >
        <IconStarSmooth class="inline-block w-12 mr-4" />
        <span class="normal-16 text-neutral-900">{{ `${reviewsAverageScore}/5` }}</span>
      </span>
    </div>
    <h1
      class="text-neutral-900 semibold-20 mt-8"
      data-testid="sofa-info-title"
    >
      {{ $t('common.smooth') }}
    </h1>
    <p
      class="mt-2 text-neutral-900 normal-14"
      data-testid="sofa-info-seo-name"
    >
      {{ seoTitles[10][material] }}
    </p>

    <BaseProductPrice
      class="mt-8"
      data-testid="sofa-info-price"
      v-bind="{ priceWithDiscount, price, omnibusPrice }"
    >
      <template #regularPrice="{ priceRegular, taxInfo }">
        <PriceRegular
          v-bind="{
            priceRegular,
            taxInfo
          }"
        />
      </template>

      <template #strikeThroughPrice="{ priceRegular, priceWithDiscount, savedAmount, omnibusPrice, taxInfo }">
        <PriceOmnibus
          v-bind="{
            priceRegular,
            priceWithDiscount,
            savedAmount,
            omnibusPrice,
            taxInfo
          }"
        />
      </template>

      <template #promoCodePrice="{ priceRegular, priceWithDiscount, code, taxInfo }">
        <PriceWithPromoCode
          v-bind="{
            priceRegular,
            priceWithDiscount,
            code,
            taxInfo
          }"
        />
      </template>
      <template #badge>
        <BaseBadge
          v-if="extraData?.customBadges && extraData?.customLabel && shelfType === 10"
          variant="custom"
          class="align-super inline-flex py-2 px-8 mt-8 ml-4"
          :style="{
            backgroundColor: extraData?.customBadgeTextColor,
            color: extraData?.customBadgeBackgroundColor
          }"
          data-testid="product-card-badge"
        >
          {{ extraData?.translateLabel ? $t(extraData?.customLabel) : extraData?.customLabel }}
        </BaseBadge>
      </template>
    </BaseProductPrice>

    <p
      class="text-black normal-16 my-16"
      data-testid="sofa-info-description"
    >
      {{ $t('pdp.sofa.intro.description') }}
    </p>
    <hr class="text-[#dddfe0]">
    <p
      class="mt-16 flex justify-between items-center normal-16 text-neutral-900"
      data-testid="sofa-info-dimensions"
    >
      {{ $t('pdp.product_details.params.button.dimensions') }}
      <span>{{ `${width}x${depth} cm` }}</span>
    </p>
    <p
      class="mt-16 flex justify-between items-center normal-16 text-neutral-900"
      data-testid="sofa-info-fabric"
    >
      {{ $t('common.fabric.title') }}
      <span>{{ $t(`common.fabric.${fabric}`) }}</span>
    </p>
    <p
      class="mt-16 flex justify-between items-center normal-16 text-neutral-900"
      data-testid="sofa-info-color"
    >
      {{ $t('common.configurator.colour') }}
      <span class="normal-16 text-neutral-900">{{ $t(materialObj.translation) }}</span>
    </p>
    <div
      v-if="!s01Available || (!corduroyAvailable && isCorduroyMaterial)"
      ref="ctaButtonsEl"
    >
      <div v-if="!s01Available">
        {{ $t('pdp.sofa_availability.not_available_in_region') }}
      </div>
      <div v-else-if="!corduroyAvailable && isCorduroyMaterial">
        {{ $t('pdp.sofa_availability.corduroy_not_available') }}
      </div>
    </div>
    <div
      v-else
      ref="ctaButtonsEl"
      class="flex md-max:flex-col flex-wrap gap-16 mt-32 md-max:mb-16"
    >
      <BaseLink
        variant="accent"
        class="whitespace-nowrap flex flex-1 items-center justify-center"
        data-testid="configure-yours-button"
        :href="GET_SOFA_CONFIGURATOR_URL(productId)"
        v-bind="{ trackData: {} }"
      >
        <template #icon>
          <IconCartPencil class="w-24 h-24" />
        </template>
        <template #default>
          {{ $t('common.configure') }}
        </template>
      </BaseLink>
      <BaseButton
        variant="outlined"
        class="whitespace-nowrap flex-1"
        data-testid="a2c-button"
        v-bind="{
          pending: isLoading,
          trackData: {
            eventCategory: 'pdp_smooth',
            eventAction: 'A2C',
            eventLabel: 'A2C'
          }
        }"
        v-on="{
          click: handleA2c,
        }"
      >
        <template #icon>
          <IconCart />
        </template>
        <template #default>
          {{ $t('common.configurator.add_to_cart') }}
        </template>
      </BaseButton>
    </div>
    <div class="flex justify-center items-center mt-16">
      <p
        class="text-neutral-750 semibold-14"
        data-testid="sofa-info-eu-made"
      >
        {{ $t('common.made_in_eu') }}
      </p>
      <span class="inline-block w-4 h-4 rounded-full bg-neutral-750 mx-4" />
      <p
        class="text-neutral-750 semibold-14"
        data-testid="sofa-info-delivery"
      >
        {{ $t('scart.expected_delivery.label', { range: `${delivery.min}-${delivery.max}` }) }}
      </p>
    </div>
    <div class="text-center mt-12">
      <BaseButton
        variant="custom"
        class="inline-block border-neutral-900 text-14 font-semibold border-solid cursor-pointer border-b-[1px]"
        data-testid="sofa-info-payment-info-button"
        v-bind="{
          trackData: {
            eventCategory: 'drawer',
            eventAction: 'payment-information',
            eventLabel: 'opened'
          }
        }"
        v-on:click="isPaymentDrawerOpen = true"
      >
        {{ $t('common.configurator.payment_information') }}
      </BaseButton>
    </div>

    <ClientOnly>
      <LazyModalSaveForLater
        v-if="configuratorData && isSaveForLaterModalOpen && !newS4LModal"
        v-model="isSaveForLaterModalOpen"
        :hydrate-when="isSaveForLaterModalOpen"
        v-bind="{
          configuratorData
        }"
      />

      <LazyModalSaveForLaterNew
        v-if="configuratorData && isSaveForLaterModalOpen && newS4LModal"
        v-model="isSaveForLaterModalOpen"
        :hydrate-when="isSaveForLaterModalOpen"
        v-bind="{
          configuratorData
        }"
      />

      <LazyModalWishlist
        v-if="isWishlistNotificationModalVisible && wishlistItem"
        v-model="isWishlistNotificationModalVisible"
        :hydrate-when="isWishlistNotificationModalVisible"
        v-bind="{
          wishlistItem
        }"
      />

      <Teleport :to="(isSm || AB_TESTS_NAVIGATION_2025) ? 'body' : '#nav-portal' ">
        <div
          class="w-full bg-white pointer-events-auto shadow-icon transition-transform basic-transition"
          :class="[
            (isSm || AB_TESTS_NAVIGATION_2025) ? 'fixed bottom-0 z-2 translate-y-full' : '-translate-y-full',
            {
              '!translate-y-0': isProductBarVisible
            }
          ]"
        >
          <div class="grid-container flex gap-16 items-center justify-between py-8 lg:py-12">
            <div class="text-neutral-900 md-max:hidden">
              <h1 class="semibold-18 line-clamp-1">
                {{ seoTitles[10][material] }}
              </h1>
              <h2 class="normal-14 line-clamp-1">
                {{ $t('scart.expected_delivery.label', { range: `${delivery.min}-${delivery.max}` }) }}
              </h2>
            </div>
            <div class="md-max:hidden flex-1 text-right">
              <BaseProductPrice
                class="mt-8"
                v-bind="{ priceWithDiscount, price, omnibusPrice }"
              >
                <template #regularPrice="{ priceRegular, taxInfo }">
                  <PriceRegular
                    v-bind="{
                      priceRegular,
                      taxInfo
                    }"
                  />
                </template>

                <template #strikeThroughPrice="{ priceRegular, priceWithDiscount, savedAmount, omnibusPrice, taxInfo }">
                  <PriceOmnibus
                    v-bind="{
                      priceRegular,
                      priceWithDiscount,
                      savedAmount,
                      omnibusPrice,
                      taxInfo
                    }"
                  />
                </template>

                <template #promoCodePrice="{ priceRegular, priceWithDiscount, code, taxInfo }">
                  <PriceWithPromoCode
                    v-bind="{
                      priceRegular,
                      priceWithDiscount,
                      code,
                      taxInfo
                    }"
                  />
                </template>
              </BaseProductPrice>
            </div>
            <aside
              class="flex items-center gap-8 md-max:flex-1"
              :class="{
                'lg-max:flex-row-reverse': isUserComesFromFacebook,
              }"
            >
              <BaseLink
                variant="accent"
                data-testid="configure-yours-sticky-bar"
                class="text-center md-max:flex-1 !leading-1 md-max:max-h-40 h-full md-max:semibold-14 md-max:order-2"
                :href="GET_SOFA_CONFIGURATOR_URL(productId)"
                v-bind="{
                  disabled: !s01Available || (!corduroyAvailable && isCorduroyMaterial),
                  trackData: {
                    eventLabel: 'configureYoursStickyBar'
                  }
                }"
              >
                {{ $t('common.configure') }}
              </BaseLink>
              <BaseButton
                class="text-center md-max:flex-1 !leading-1 md-max:max-h-40 h-full md-max:semibold-14"
                data-testid="navigation-add-to-cart-sticky-bar"
                variant="outlined"
                v-bind="{
                  disabled: !s01Available || (!corduroyAvailable && isCorduroyMaterial),
                  pending: isLoading,
                  trackData: {
                    eventLabel: 'addToCartStickyBar'
                  },
                  title: $t('common.configurator.add_to_cart'),
                }"
                v-on="{ click: handleA2c }"
              >
                {{ $t('common.configurator.add_to_cart') }}
              </BaseButton>
            </aside>
          </div>
        </div>
      </Teleport>
    </ClientOnly>

    <LazyPdpDrawerPaymentInformation
      v-if="isPaymentDrawerOpen"
      v-model="isPaymentDrawerOpen"
      :hydrate-when="isPaymentDrawerOpen"
    />
  </div>
</template>

<script setup lang="ts">
import { useElementVisibility, useCssVar } from '@vueuse/core';
import { getSottyMaterialById, SofaFinishType } from '~/consts/shelfType10';

import useSofaConfiguratorUrl from '~/composables/useSofaConfiguratorUrl';
import useAddToWishList from '~/composables/pdp/useAddToWishList';
import useMq from '~/composables/useMq';
import { pdpAnalytics } from '~/composables/pdp/pdpAnalytics';

const { productId } = defineProps({
  productId: {
    type: String,
    required: true
  }
});

const $gtm = useGtm();

const { isSm } = useMq();
const { extraData } = useGlobal();
const { isFooterVisible } = storeToRefs(useHeaderStore());
const { isWishlistNotificationModalVisible, wishlistItem, addToUserWishlistSofa } = useAddToWishList();

const { handelAddToCartPreset } = useCart();
const { GET_SOFA_CONFIGURATOR_URL } = useSofaConfiguratorUrl();

const {
  reviewsAverageScore,
  isSignedIn,
  s01Available,
  corduroyAvailable,
  IS_USER_COMES_FROM_FACEBOOK: isUserComesFromFacebook,
  AB_TESTS_NAVIGATION_2025,
  AB_TEST_VALUE
} = storeToRefs(useGlobal());

const {
  price,
  priceWithDiscount,
  omnibusPrice,
  width,
  depth,
  material,
  delivery,
  preview,
  seoTitles,
  fabric,
  priceInEuro,
  configuratorType,
  furnitureCategory,
  priceWithDiscountInEuro,
  shelfType
} = storeToRefs(usePdpStore());

const analiticsData = {
  material: material.value,
  shelfType: shelfType.value,
  priceInEuro: priceInEuro.value,
  patternName: null,
  configuratorType: configuratorType.value,
  furnitureCategory: furnitureCategory.value,
  priceWithDiscountInEuro: priceWithDiscountInEuro.value,
  contentType: 'sotty'
};

const { viewItemGA4, a2cGa4Event, s4lGa4Event } = pdpAnalytics(analiticsData);

const isCorduroyMaterial = computed(() => fabric.value === SofaFinishType.CORDUROY);

const materialObj = getSottyMaterialById(material.value!);

const isLoading = ref(false);
const isMounted = ref(false);
const pageLoaded = ref(false);
const configuratorData = ref(null);
const isWishlistLoading = ref(false);
const isPaymentDrawerOpen = ref(false);
const ctaButtonsEl = ref<HTMLElement>();
const isWishlistButtonActive = ref(true);
const isSaveForLaterModalOpen = ref(false);
const newS4LModal = computed(() => AB_TEST_VALUE.value('new_s4l_2025')?.isActive);

const handleA2c = async () => {
  isLoading.value = true;

  try {
    await handelAddToCartPreset(productId, 'sotty');

    const data = await a2cGa4Event(productId);

    if (data) {
      $gtm?.push({ ecommerce: null });
      $gtm?.push(data);
    }
  } catch (error) {

  } finally {
    isLoading.value = false;
  }
};

const handleS4L = async () => {
  if (isSignedIn.value) {
    isWishlistLoading.value = true;

    try {
      const data = await addToUserWishlistSofa(productId);

      isWishlistButtonActive.value = false;

      const eventData = await s4lGa4Event({
        material: material.value,
        content_type: analiticsData.contentType,
        shelfId: data?.created_furniture_id,
        pattern_name: 'not applicable',
        configuratorType: configuratorType.value,
        base_preset: productId,
        region_price_with_discount_in_euro: priceWithDiscountInEuro.value,
        category: furnitureCategory.value,
        region_price_in_euro: priceInEuro.value,
        shelf_type: shelfType.value,
        delivery_time_min: delivery.value?.min,
        delivery_time_max: delivery.value?.max
      }, data?.created_furniture_id);

      $gtm?.push({ ecommerce: null });
      $gtm?.push(eventData);
    } catch (error) {

    } finally {
      isWishlistLoading.value = false;
    }
  } else {
    $gtm?.push({
      event: 'popup_saveitem',
      eventAction: 'open',
      eventCategory: 'popup',
      eventLabel: undefined,
      eventValue: undefined
    });

    configuratorData.value = {
      endpoint: 'sotty',
      productId,
      screenshot: preview.value,
      presetImg: true,
      content_type: analiticsData.contentType,
      pattern_name: 'not applicable',
      configuratorType: configuratorType.value,
      base_preset: productId,
      region_price_with_discount_in_euro: priceWithDiscountInEuro.value,
      category: furnitureCategory.value,
      region_price_in_euro: priceInEuro.value,
      shelf_type: shelfType.value,
      material: material.value,
      delivery_time_min: delivery.value?.min,
      delivery_time_max: delivery.value?.max
    };

    isSaveForLaterModalOpen.value = true;
  }
};

const isProductBarVisible = computed(() => {
  const shouldBeVisible = !isProductCardVisible.value && !isFooterVisible.value && isMounted.value && pageLoaded.value;
  return shouldBeVisible;
});

const ribbonHeightCssVar = useCssVar('--dixa-icon-offset');
const isProductCardVisible = useElementVisibility(ctaButtonsEl);

onMounted(() => {
  $gtm?.push({ ecommerce: null });
  $gtm?.push(viewItemGA4(productId));

  nextTick(() => (isMounted.value = true));

  setTimeout(() => {
    pageLoaded.value = true;
  }, 100);
});

watch(isProductBarVisible, (value) => { ribbonHeightCssVar.value = value ? '56px' : '0px'; });
</script>

<style  lang="scss">
@screen lg-max {
  .dixa-messenger-namespace .dixa-messenger-wrapper {
    bottom: 96px !important;
  }
}
@screen lg {
  .dixa-messenger-namespace .dixa-messenger-wrapper {
    @apply transition-transform duration-300 ease-in-out -translate-y-[var(--dixa-icon-offset)];
  }
}
</style>
