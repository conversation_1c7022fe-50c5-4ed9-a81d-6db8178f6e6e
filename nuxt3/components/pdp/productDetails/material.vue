<template>
  <PdpProductDetailsUiGroup
    id="PDPProductDetailsMaterial"
    v-bind="{
      title: $t('pdp.product_details.material.headline'),
      eventLabel: 'Material'
    }"
  >
    <div class="flex flex-col gap-32">
      <PdpProductDetailsUiArticle
        v-for="(item, index) in items"
        v-bind:key="index"
        v-bind="{
          title: item?.title,
          text: item?.text,
          imagePath: item?.imagePath ? imagePath : null,
        }"
      />
    </div>
  </PdpProductDetailsUiGroup>
</template>

<script setup lang="ts">
import type { FurnitureParameters } from './productDetails.vue';

import useColors from '~/composables/useColors';
import useDetails from '~/composables/pdp/useDetails';

import { FURNITURE_TYPE_TO_MATERIAL, TYPE_TO_LINE } from '~/consts/types';

const props = defineProps({
  furnitureCategory: {
    type: String,
    required: true
  },
  furnitureDynamicParameters: {
    type: Object as PropType<FurnitureParameters>,
    required: true
  }
});

const { getColor } = useColors();
const { materialData, materialImage } = useDetails();

const material = computed(() => FURNITURE_TYPE_TO_MATERIAL[props.furnitureDynamicParameters.furnitureType]);
const colorInfo = computed(() => getColor(props.furnitureDynamicParameters.furnitureType, props.furnitureDynamicParameters.materialValue));
const materialFinish = computed(() => colorInfo.value.isWooden ? 'wood' : colorInfo.value.isPlywood ? 'plywood' : colorInfo.value.isSolid ? 'solid' : null);
const toneFinish = computed(() => props.furnitureDynamicParameters.shelfType === 8 ? 'expression2' : 'expression1');

const isPremiumMatteBlack = computed(() => props.furnitureDynamicParameters.materialValue === 6);

const isToneShelf = computed(() => {
  return TYPE_TO_LINE[props.furnitureDynamicParameters.furnitureType] === 'tone' && props.furnitureCategory !== 'wardrobe';
});

const getFirstItem = computed(() => {
  if (TYPE_TO_LINE[props.furnitureDynamicParameters.furnitureType] === 'edge') {
    return materialData('edgeConstructionText', null, materialFinish.value);
  } else if (isToneShelf.value) {
    return materialData('toneShelvesConstructionText', null, toneFinish.value);
  } else {
    return materialData('constructionText', material.value);
  }
});

const getSecondItem = computed(() => {
  if (TYPE_TO_LINE[props.furnitureDynamicParameters.furnitureType] === 'edge') {
    return materialData('edgeFinishText', null, materialFinish.value);
  } else if (isToneShelf.value) {
    return materialData('toneShelvesFinishText');
  } else {
    return props.furnitureCategory === 'desk'
      ? materialData('deskFinishText')
      : isPremiumMatteBlack.value
        ? materialData('premiumMatteBlackFinishText')
        : materialData('finishText', material.value);
  }
});

const getThirdItem = computed(() => {
  if (TYPE_TO_LINE[props.furnitureDynamicParameters.furnitureType] === 'edge') {
    return materialData('edgeQualityAndFeelText', null, materialFinish.value);
  } else if (isToneShelf.value) {
    return materialData('toneShelvesFittingsText');
  } else {
    return materialData('qualityAndFeelText', material.value);
  }
});

const items = computed(() => [
  getFirstItem.value,
  getSecondItem.value,
  getThirdItem.value
]);

const imagePath = computed(() => materialImage(colorInfo.value.name, material.value));
</script>
