<template>
  <footer
    ref="footerRef"
    class="bg-neutral-900 text-white"
    data-observe-view="Footer"
    data-section="section-footer"
    data-testid="footer"
  >
    <!-- Title -->
    <div class="grid-container pt-64 pb-48 lg:py-96">
      <h1
        class="semibold-32 lg:semibold-44 xl:semibold-54"
        data-testid="footer-title"
      >
        {{ t('footer.hero_title') }}
      </h1>
    </div>

    <div class="grid-container flex flex-col lg:flex-row gap-x-32 text-white">
      <!-- Link columns -->

      <div class="grow grid grid-flow-row lg:flex lg:justify-between gap-16 lg:gap-32">
        <BaseAccordion
          v-for="column in navigationLinkColumns"
          :key="'footer_accordion_' + column.title"
          as="section"
          data-testid="footer-accordion"
          v-bind="{
            class: column?.classes,
            labelClasses: column.accordionAlwaysExpanded ? '' : 'lg-max:cursor-pointer lg:cursor-default',
            accordionWrapperClasses: `grid grid-rows-[1fr] lg-max:overflow-hidden lg:!block normal-16 lg:normal-14
                                      ${column?.classes}
                                      ${column.accordionAlwaysExpanded ? '!block' : ''}
                                      `
          }"
        >
          <template #title="{ isExpanded }">
            <div class="relative">
              <span class="semibold-28 lg:semibold-24">{{ column.title }}</span>
              <component
                :is="isExpanded ? IconButtonMinus : IconButtonPlus"
                class="w-48 h-48 lg:hidden absolute right-0 top-1/2 transform -translate-y-1/2 p-8"
                data-testid="footer-accordion-button"
                :class="{
                  'hidden': column.accordionAlwaysExpanded
                }"
              />
            </div>
          </template>
          <template #content>
            <ul
              class="mt-16 pb-32"
              data-testid="footer-accordion-list"
              :class="column.ulClasses || 'flex flex-col gap-8'"
            >
              <li
                v-for="link in column.items"
                :key="link.attrs['data-testid'] as string"
                :class="link.classes"
                data-testid="footer-accordion-list-item"
              >
                <component
                  :is="link.component || BaseLink"
                  variant="underline-link"
                  v-bind="{
                    ...link.attrs,
                  }"
                  class="normal-16 lg:normal-14 inline-block whitespace-nowrap"
                  data-testid="footer-accordion-list-item-link"
                >
                  <template
                    v-if="link.icon"
                    #icon
                  >
                    <component
                      :is="link.icon"
                      class="inline-block lg:mr-8 w-28 h-28 lg:w-16 lg:h-16"
                    />
                  </template>
                  {{ link.content }}
                </component>
              </li>
            </ul>
          </template>
        </BaseAccordion>
      </div>

      <div class="lg:shrink">
        <!-- Shipping -->
        <p
          class="semibold-24"
          data-testid="footer-shipping"
          v-html="t('Shipping to')"
        />
        <BaseButton
          variant="custom"
          class="mt-16 normal-16 border border-white rounded-8 px-16 py-12 flex items-center gap-4 lg-max:w-full"
          data-testid="footer-change-region"
          :track-data="{ eventLabel: 'footer-change-region' }"
          v-on:click="isChangeRegionModalOpen = true"
        >
          <img
            class="w-20 h-20"
            data-testid="footer-change-region-icon"
            loading="lazy"
            fetchpriority="low"
            v-bind="{
              alt: regionName,
              src: `/nuxt3-statics/img/flags/${regionName}.svg`
            }"
          >
          {{ translatedRegion(regionName) }} <span>|</span> {{ currentLanguageName }}
          <IconCaretDown
            class="block ml-auto"
            width="24"
            height="24"
            data-testid="footer-change-region-icon-caret"
          />
        </BaseButton>

        <!-- PAYMENTS -->
        <p
          class="semibold-16 mt-48"
          data-testid="footer-payment-methods"
          v-html="t('footer.payment_methods')"
        />
        <div
          class="mt-16 flex gap-8 flex-wrap text-white"
          data-testid="footer-payment-icons"
        >
          <img
            v-for="(icon, index) in icons"
            :key="`${index}-${icon}`"
            data-testid="footer-payment-icon"
            loading="lazy"
            fetchpriority="low"
            :src="`/nuxt3-statics/img/payment-icons/white/${icon.toLowerCase()}.svg`"
          >
        </div>

        <!-- Security -->
        <div
          class="mt-48"
          data-testid="footer-security"
        >
          <p
            class="semibold-16"
            v-html="t('footer.header_trustedshop')"
          />
          <p
            class="normal-12"
            v-html="t('footer.header_trustedshop2')"
          />
          <div class="mt-16 flex gap-16">
            <IconSsl
              width="42"
              height="42"
            />
            <BaseLink
              variant="custom"
              v-bind="{ href: t('url.trusted-shops'), trackData: {} }"
              target="_blank"
              rel="noopener"
            >
              <IconTrustedShopsFooter
                class="block transition-opacity short-transition opacity-100 hover:opacity-80"
                width="42"
                height="42"
              />
            </BaseLink>
          </div>
          <ClientOnly>
            <!-- TrustBox widget - Micro Star -->
            <div
              class="trustpilot-widget mt-16 max-w-fit -left-32 relative"
              data-locale="en-GB"
              data-template-id="5419b732fbfb950b10de65e5"
              data-businessunit-id="59f5c4fe0000ff0005aef6e1"
              data-style-height="20px"
              data-style-width="100%"
              data-theme="dark"
              data-token="6c295277-f5f0-4325-8c08-d10a7acd5fa4"
              data-text-color="#ffffff"
            >
                <a
                  href="https://uk.trustpilot.com/review/tylko.com"
                  target="_blank"
                  rel="noopener"
                >
                  Trustpilot
                </a>
            </div>
            <!-- End TrustBox widget -->
          </ClientOnly>
        </div>
      </div>
    </div>

    <!-- TYLKO LOGO -->
    <div
      class="flex justify-center
              gap-x-32 lg:gap-x-64
              mx-16 mt-64 pb-64
              lg:mt-64 lg:pb-32
              xl2:mt-96 xl2:pb-48"
      data-testid="footer-tylko-logo"
    >
      <IconTylkoLogo
        class="grow w-full md:max-w-[300px] lg2:max-w-[400px] xl:max-w-[520px]"
        data-testid="footer-tylko-logo-icon"
      />
    </div>

    <ClientOnly>
      <LazyModalWhatsApp
        v-if="isWhatsAppModalOpen"
        v-model="isWhatsAppModalOpen"
        :hydrate-when="isWhatsAppModalOpen"
      />
      <LazyTheFooter2025ChangeRegionModal
        v-if="isChangeRegionModalOpen"
        v-model="isChangeRegionModalOpen"
        :hydrate-when="isChangeRegionModalOpen"
      />
    </ClientOnly>
  </footer>
</template>

<script setup lang="ts">
import { useElementVisibility, useMounted } from '@vueuse/core';
import BaseLink from '~/components/base/link.vue';
import BaseButton from '~/components/base/button.vue';
import { regionPaymentMethods } from '~/utils/regionPaymentMethods';
import IconSocialFacebook from '~/assets/icons/social/facebook.svg';
import IconSocialInstagram from '~/assets/icons/social/instagram.svg';
import IconSocialPinterest from '~/assets/icons/social/pinterest.svg';
import IconSocialTiktok from '~/assets/icons/social/tiktok.svg';
import IconButtonMinus from '~/assets/icons/minus.svg';
import IconButtonPlus from '~/assets/icons/plus.svg';
const { t } = useI18n();
const { $dixa, $addLocaleToPath } = useNuxtApp();

const cookieStore = useTyCookieStore();
const { isCookiesManagerModalOpen } = storeToRefs(cookieStore);
const isWhatsAppModalOpen = ref(false);
const isChangeRegionModalOpen = ref(false);

const { translatedRegion } = useRegions();
const { regionName } = storeToRefs(useGlobal());
const { currentLanguageName } = useLocale();

const { isFooterVisible } = storeToRefs(useHeaderStore());
const footerRef = ref<HTMLElement>();
const isVisible = useElementVisibility(footerRef);
watch(isVisible, (value: boolean) => (isFooterVisible.value = value), { immediate: true });

const icons = regionPaymentMethods[regionName.value];
const isDixaVisible = useState('isDixaEnabled', () => false);

const showCookiesSettings = () => {
  isCookiesManagerModalOpen.value = true;
};

const openLiveChat = () => {
  $dixa.toggleWidget(true);
};

const showWhatsApp = () => {
  isWhatsAppModalOpen.value = !isWhatsAppModalOpen.value;
};

const isMounted = useMounted();
const functionalCookie = computed(() => cookieStore.consents.functional);

const navigationLinkColumns : Array<{
  title: string;
  items: Array<{
    attrs: Record<string, unknown>;
    content: string;
    classes?: string;
    icon?: any;
    component?: typeof BaseLink | typeof BaseButton | string;
  }>
  classes?: string;
  ulClasses?: string;
  accordionAlwaysExpanded?: boolean;
}> = [
  // Company
  {
    title: t('footer.company'),
    items: [
      {
        attrs: {
          href: $addLocaleToPath('lp.about-tylko'),
          trackData: {},
          'data-testid': 'footer-abouttylko'
        },
        content: t('footer.abouttylko')
      },
      {
        attrs: {
          href: t('url.showrooms'),
          trackData: {},
          'data-testid': 'footer-showrooms'
        },
        content: 'Showrooms'
      },
      {
        attrs: {
          href: t('url.journal'),
          target: '_blank',
          trackData: {},
          'data-testid': 'footer-journal'
        },
        content: t('footer.journal')
      },
      {
        attrs: {
          href: $addLocaleToPath('lp.press'),
          trackData: {},
          'data-testid': 'footer-press'
        },
        content: t('footer.press')
      },
      {
        attrs: {
          href: t('url.jobs'),
          target: '_blank',
          trackData: {},
          'data-testid': 'footer-jobs'
        },
        content: t('footer.jobs')
      },
      {
        attrs: {
          href: $addLocaleToPath('investor-relations'),
          trackData: {},
          'data-testid': 'footer-investor-relations'
        },
        content: t('footer.investor_relations')
      },
      {
        attrs: {
          href: $addLocaleToPath('lp.tylkopro'),
          trackData: {},
          'data-testid': 'footer-tylkoforbusiness-program'
        },
        content: t('menu.labels.tylko_pro')
      }
    ]
  },

  // Buying guide
  {
    title: t('footer.buying_guide'),
    items: [
      {
        attrs: {
          href: $addLocaleToPath('faq'),
          trackData: {},
          'data-testid': 'footer-faq'
        },
        content: t('footer.faq')
      },
      {
        attrs: {
          href: $addLocaleToPath('terms'),
          trackData: {},
          'data-testid': 'footer-terms'
        },
        content: t('footer.terms_of_service')
      },
      {
        attrs: {
          href: $addLocaleToPath('privacy-policy'),
          trackData: {},
          'data-testid': 'footer-privacy-policy'
        },
        content: t('footer.privacy_policy')
      },
      {
        attrs: {
          trackData: {},
          'data-testid': 'footer-cookies',
          onClick: showCookiesSettings
        },
        content: t('footer.edit_cookies'),
        component: BaseButton
      },
      {
        attrs: {
          href: $addLocaleToPath('shipping'),
          trackData: {},
          'data-testid': 'footer-shipping'
        },
        content: t('footer.shipping_and_returns')
      },
      {
        attrs: {
          href: $addLocaleToPath('contact') + '?topic=order_status',
          trackData: {},
          'data-testid': 'footer-delivery_status'
        },
        content: t('menu.labels.delivery_status')
      }
    ]
  },

  // Customer service
  {
    title: t('footer.customer_service'),
    accordionAlwaysExpanded: true,
    items: [
      {
        attrs: {
          href: $addLocaleToPath('contact'),
          trackData: {},
          'data-testid': 'footer-contact'
        },
        content: t('footer.contact_details'),
        classes: 'order-2'
      },
      {
        attrs: {
          trackData: {},
          'data-testid': 'footer-whatsapp',
          onClick: showWhatsApp
        },
        content: 'WhatsApp',
        component: BaseButton,
        classes: 'order-3'
      },
      {
        attrs: {
          target: '_blank',
          trackData: {},
          href: 'https://tylko.whistlelink.com/',
          'data-testid': 'footer-report_violation'
        },
        content: t('footer.report_violation'),
        classes: 'order-5'
      },
      {
        attrs: { },
        component: 'span',
        content: t('const.other.contact_hours1'),
        classes: 'order-1'
      },
      {
        attrs: { },
        component: 'span',
        content: t('const.other.contact_hours2'),
        classes: 'order-1'
      },
      ...(isMounted.value && functionalCookie.value && isDixaVisible.value
        ? [{
            attrs: {
              trackData: { eventLabel: 'footer-livechat' },
              'data-testid': 'footer-whatsapp',
              onClick: openLiveChat
            },
            content: t('common.livechat'),
            component: BaseButton,
            classes: 'order-3'
          }]
        : [])
    ]
  },

  // Social media
  {
    title: t('footer.follow_us'),
    accordionAlwaysExpanded: true,
    ulClasses: 'flex lg:flex-col gap-32 lg:gap-8',
    items: [
      {
        attrs: {
          target: '_blank',
          href: 'https://www.facebook.com/tylko',
          trackData: {},
          'data-testid': 'social-facebook'
        },
        classes: 'lg-max:[&_span]:hidden',
        icon: IconSocialFacebook,
        content: 'Facebook'
      },
      {
        attrs: {
          target: '_blank',
          trackData: {},
          'data-testid': 'social-instagram',
          href: 'https://instagram.com/tylko/'
        },
        classes: 'lg-max:[&_span]:hidden',
        icon: IconSocialInstagram,
        content: 'Instagram'
      },
      {
        attrs: {
          target: '_blank',
          href: 'https://www.pinterest.com/tylko/',
          trackData: {},
          'data-testid': 'social-pinteres'
        },
        classes: 'lg-max:[&_span]:hidden',
        icon: IconSocialPinterest,
        content: 'Pinterest'
      },
      {
        attrs: {
          target: '_blank',
          href: 'https://www.tiktok.com/@tylko.com',
          trackData: {},
          'data-testid': 'social-tiktok'
        },
        classes: 'lg-max:[&_span]:hidden',
        icon: IconSocialTiktok,
        content: 'Tiktok'
      }
    ]
  }
] as any;

</script>
