<template>
  <div
    ref="videoContainer"
    class="wistia_swatch h-full w-full overflow-hidden absolute inset-0"
  >
    <div :class="`wistia_embed ${embedOptions} wistia_async_${videoId} absolute inset-0`" />
    <aside
      v-if="hasCentredPlayButton"
      class="wistia_button absolute z-1 inset-0 flex justify-center items-center"
      v-on:click="togglePlay"
      v-html="!videoState.isPlaying ? IconVideoPlayCentred : ''"
    />
    <aside
      v-if="hasControls || hasSound"
      class="absolute z-1 bottom-8 lg:bottom-24 right-8 lg:right-[88px] flex items-center justify-end"
    >
      <button
        v-if="hasControls"
        class="wistia__button"
        v-on:click="togglePlay"
        v-html="videoState.isPlaying ? IconPause : IconPlay"
      />
      <button
        v-if="hasSound"
        class="wistia__button"
        v-on:click="toggleMute"
        v-html="videoState.isMuted ? IconMute : IconSound"
      />
    </aside>
  </div>
</template>

<script setup lang="ts">
import { useIntersectionObserver } from '@vueuse/core';
import IconSound from '~/assets/icons/video/sound.svg?raw';
import IconMute from '~/assets/icons/video/mute.svg?raw';
import IconPlay from '~/assets/icons/video/play.svg?raw';
import IconPause from '~/assets/icons/video/pause.svg?raw';
import IconVideoPlayCentred from '~/assets/icons/video/playCentred.svg?raw';

const props = defineProps({
  videoId: {
    type: String,
    required: true
  },
  muted: {
    type: Boolean,
    default: true
  },
  autoPlay: {
    type: Boolean,
    default: true
  },
  endVideoBehavior: {
    type: String,
    default: 'loop',
    validator: value => ['default', 'reset', 'loop'].includes(value)
  },
  wmode: {
    type: [String, Boolean],
    default: 'transparent',
    validator: value => ['transparent', false].includes(value)
  },
  seo: {
    type: Boolean,
    default: false
  },
  doNotTrack: {
    type: Boolean,
    default: false
  },
  settingsControl: {
    type: Boolean,
    default: false
  },
  controlsVisibleOnLoad: {
    type: Boolean,
    default: false
  },
  playbar: {
    type: Boolean,
    default: false
  },
  playButton: {
    type: Boolean,
    default: false
  },
  hasCentredPlayButton: {
    type: Boolean,
    default: false
  },
  fullscreenButton: {
    type: Boolean,
    default: false
  },
  smallPlayButton: {
    type: Boolean,
    default: false
  },
  volumeControl: {
    type: Boolean,
    default: false
  },
  playPauseNotifier: {
    type: Boolean,
    default: false
  },
  qualityMin: {
    type: Number,
    default: 360,
    validator: value => [224, 360, 540, 720, 1080, 3840].includes(value)
  },
  hasSound: {
    type: Boolean,
    default: false
  },
  hasControls: {
    type: Boolean,
    default: false
  },
  fitStrategy: {
    type: String,
    default: 'fill'
  },
  externalMuteControl: {
    type: Boolean,
    default: false
  },
  externalMuteValue: {
    type: Boolean,
    default: true
  }
});
const emit = defineEmits<{'ready': [video: any]}>();

const videoEl = shallowRef(null);
const videoContainer = ref(null);
const targetIsVisible = ref(false);
const videoState = ref({
  isMuted: props.muted,
  isPlaying: false
});

useIntersectionObserver(videoContainer, ([{ isIntersecting }]) => {
  targetIsVisible.value = isIntersecting;
}, {
  threshold: 0.2
});

const embedOptions = computed(() => Object.entries(props)
  .map(([key, value]) => `${key}=${value}`)
  .join(' '));

const play = () => {
  videoEl.value.play();
  videoState.value.isPlaying = true;
};

const pause = (reset = false) => {
  videoEl.value.pause();
  videoState.value.isPlaying = false;

  if (reset) {
    videoEl.value.time(0);
  }
};

const toggleMute = () => {
  if (videoEl.value) {
    if (videoState.value.isMuted) {
      videoEl.value.unmute();
      videoState.value.isMuted = false;
    } else {
      videoEl.value.mute();
      videoState.value.isMuted = true;
    }
  }
};

const togglePlay = () => {
  if (videoEl.value) {
    if (videoState.value.isPlaying) {
      pause();
    } else {
      play();
    }
  }
};

const {
  isMegaMenuOpened
} = useHeaderModern();

const isVideoActive = computed(() => targetIsVisible.value && !isMegaMenuOpened.value);

watch(isVideoActive, (value) => {
  if (videoEl.value && props.autoPlay) {
    if (value) {
      play();
    } else {
      pause();
    }
  }
}, { immediate: true });

watch(() => props.externalMuteValue, () => {
  toggleMute();
}, { immediate: true });

onMounted(() => {
  window._wq = window._wq || [];
  _wq.push({
    id: props.videoId,
    onReady (video) {
      emit('ready', video);
      videoEl.value = video;
      videoEl.value.volume(1);

      props.hasSound && video.bind('mutechange', function (isMuted:boolean) {
        videoState.value.isMuted = isMuted;
      });

      if (props.hasControls) {
        if (!targetIsVisible.value) {
          pause(true);
        } else {
          play();
        }

        videoEl.value.bind('end', () => {
          videoState.value.isPlaying = false;
          videoEl.value.time(0);
        });
      }
    }
  });
});

defineExpose({
  play,
  pause,
  videoEl
});

useHead(() => ({
  script: () => [
    { hid: 'wistia', src: 'https://fast.wistia.com/assets/external/E-v1.js', async: true },
    { hid: `wistia-${props.videoId}`, src: () => `https://fast.wistia.com/embed/medias/${props.videoId}.jsonp`, async: true }
  ]
}));
</script>

<style lang="scss">
.w-chrome {
  @apply pointer-events-none;
}

.wistia {
  &__button {
    @apply flex items-center justify-center rounded-full text-offwhite-700;
    @apply transition-all ease-in-out duration-200 border-2 border-transparent hover:bg-[#fafbfc14];
    @apply focus:bg-[#fafbfc14] focus:border-white disabled:opacity-[0.38] outline-none w-48 h-48;
  }
}
</style>
