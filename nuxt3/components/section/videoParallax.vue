<template>
  <div
    v-bind="{
      'data-observe-view': dataObserveView,
      'data-section': dataSection
    }"
    class="video-parallax video-parallax__max-height sticky top-[var(--ribbon-height)] overflow-hidden"
    :style="`--video-aspect-ratio-mobile: ${videoAspectRatio.mobile}; --video-aspect-ratio-desktop: ${videoAspectRatio.desktop}; background-color: ${backgroundColor};`"
  >
    <div
      ref="video"
      class="video-parallax__max-height"
      :class="videoWrapperClass"
    >
      <div class="w-full video-parallax__min-height">
        <BasePicture
          v-if="!isStatic && videoPlaceHolder"
          type="M D"
          picture-classes="!absolute inset-0 object-cover"
          img-classes="!absolute inset-0 object-cover object-center h-full"
          v-bind="{
            alt: '',
            path: videoPlaceHolder
          }"
        />
        <BaseVideoWistia
          v-if="!isStatic"
          class="video-parallax__max-height !absolute inset-0"
          v-bind="{
            videoId: videoParams?.videoId,
            showPlaceholder: !videoPlaceHolder,
            embedOptions: {
              ...videoParams?.embedOptions,
              externalMuteControl: hasSoundControl,
              externalMuteValue: isMuted
            },
          }"
        />
        <BasePicture
          v-else
          type="M D"
          picture-classes="w-full"
          img-classes="w-full"
          v-bind="{
            alt: imageAlt,
            path: imagePath
          }"
        />
      </div>
    </div>
  </div>
  <div
    class="py-32 pointer-events-none video-parallax-content md:py-48 lg:py-64"
    :class="isCentered ? 'flex items-end justify-center' : 'xl:py-96'"
    :style="`--video-aspect-ratio-mobile: ${videoAspectRatio.mobile}; --video-aspect-ratio-desktop: ${videoAspectRatio.desktop}`"
  >
    <slot />
    <div
      v-if="headingCopy"
      class="grid-container"
      :style="additionalPadding ? `top: calc(var(--header-with-ribbon-height) + ${additionalPadding}px)` : ''"
      :class="isCentered ? 'relative text-center' : 'video-parallax-sticky-content'"
    >
      <h2
        v-if="subheadingCopy"
        ref="subheading"
        class="mb-16 uppercase md:mb-24"
        :class="[
          headerColorClass,
          isCentered ? 'semibold-18' : 'semibold-14 md:semibold-16 lg:semibold-18'
        ]"
        v-html="subheadingCopy"
      />
      <h1
        ref="heading"
        class="lg:semibold-46 xl:semibold-54"
        :class="[
          headerColorClass,
          isCentered ? 'semibold-36' : 'semibold-28 md:semibold-32',
          isHeroSection ? 'semibold-32 lg:semibold-36 xl:semibold-72' : 'semibold-36'
        ]"
        v-html="headingCopy"
      />
      <BaseLink
        v-if="ctaCopy && (ctaUrl || ctaCallback)"
        v-bind="{
          ...(ctaUrl && { href: ctaUrl }),
          variant: isCentered ? 'filled-dark' : ctaClass ? 'custom' : 'accent',
          trackData: {
            eventLabel: 'cta',
            eventPath: ctaUrl
          }
        }"
        class="inline-flex mt-24 pointer-events-auto md:mt-32 xl:mt-48"
        :class="ctaClass"
        v-on="ctaCallback ? { click: ctaCallback } : {}"
      >
        {{ ctaCopy }}
      </BaseLink>
    </div>

    <button
      v-if="hasSoundControl"
      class="wistia__button pointer-events-auto absolute right-16 lg:right-[88px] bottom-24 text-offwhite-700"
      v-on:click="isMuted = !isMuted"
      v-html="isMuted ? IconMute : IconSound"
    />
  </div>
</template>

<script setup lang="ts">
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import IconSound from '~/assets/icons/video/sound.svg?raw';
import IconMute from '~/assets/icons/video/mute.svg?raw';

interface VideoAspectRatioProp{
  mobile:number;
  desktop:number;
}

const props = withDefaults(defineProps<{
  headingCopy?: string,
  subheadingCopy?: string,
  headerColorClass?: string,
  ctaCopy?: string,
  ctaUrl?: string,
  ctaCallback?: Function,
  imageAlt: string,
  backgroundColor?: string,
  videoAspectRatio: VideoAspectRatioProp,
  isCentered?: boolean,
  additionalPadding?: number,
  hasControls?: boolean,
  hasSoundControl?: boolean,
  isStatic?: boolean,
  isStaticVideo?: boolean,
  isHeroSection?: boolean,
  imagePath?: string,
  resetVideo?: boolean,
  dataObserveView?: string,
  dataSection?: string,
  videoParams?: {
      id: string,
      videoId: {
        mobile: string,
        desktop: string
      },
      alt?: string,
      embedOptions?:{
        fitStrategy: string
      }
    } | null,
  videoPlaceHolder?: string
  videoWrapperClass?: string,
  ctaClass?: string
}>(), {
  headingCopy: '',
  subheadingCopy: '',
  headerColorClass: 'text-offblack-600',
  ctaCopy: '',
  ctaUrl: '',
  ctaCallback: () => {},
  backgroundColor: '#fff',
  isCentered: false,
  additionalPadding: 0,
  hasControls: false,
  hasSoundControl: false,
  isStatic: false,
  isStaticVideo: false,
  isHeroSection: false,
  imagePath: '',
  resetVideo: false,
  dataObserveView: 'video-parallax',
  dataSection: 'video-parallax',
  videoParams: null,
  videoPlaceHolder: '',
  videoWrapperClass: '',
  ctaClass: ''
});

const heading = ref<HTMLElement | null>(null);
const subheading = ref<HTMLElement | null>(null);
const video = ref<HTMLElement | null>(null);
const isMuted = ref<boolean>(true);

if (props.headingCopy && !props.isStaticVideo) {
  onMounted(() => {
    gsap.registerPlugin(ScrollTrigger);

    gsap.timeline({
      scrollTrigger: {
        trigger: video.value,
        scrub: 0.3,
        start: 'top top',
        end: 'bottom'
      }
    })
      .to(video.value, {
        ease: 'none',
        scale: 1.2,
        transformOrigin: 'bottom center'
      });
  });

  onBeforeUnmount(() => {
    ScrollTrigger.killAll();
  });
}
</script>

<style lang="scss">
.video-parallax {
  z-index: -1;

  &__max-height {
    @apply max-h-screen md:min-h-[30vw];
    @apply max-h-[calc((100svh)-var(--header-with-ribbon-height))];
  }

  &__min-height {
    @apply min-h-[calc((100vw)/var(--video-aspect-ratio-mobile))];
    @apply md:min-h-[calc((100vw)/var(--video-aspect-ratio-desktop))];
  }

  &__aspect-ratio {
    aspect-ratio: var(--video-aspect-ratio-mobile) / 1;

    @media screen and (min-width: 768px) {
      aspect-ratio: var(--video-aspect-ratio-desktop) / 1;
    }
  }

  &__vistia-padding {
    padding-top: calc(1 / var(--video-aspect-ratio-mobile) * 100%) !important;

    @media screen and (min-width: 768px) {
      padding-top: calc(1 / var(--video-aspect-ratio-desktop) * 100%) !important;
    }
  }
}

.video-parallax-content {
  @apply absolute z-1 top-0 left-0 w-full;
  @apply h-[calc((100vw)/var(--video-aspect-ratio-mobile))];
  @apply md:h-[calc((100vw)/var(--video-aspect-ratio-desktop))];
  @apply max-h-[calc((100svh)-var(--header-with-ribbon-height))];
}

.video-parallax-sticky-content {
  @apply sticky top-[calc(32px+var(--ribbon-height))] md:top-[calc(48px+var(--ribbon-height))] lg:top-[calc(64px+var(--ribbon-height))] xl:top-[calc(96px+var(--ribbon-height))];
}

</style>
