import {CONFIG_LOCALES} from './../utils/languages';

export const SCHEMA_LANG_FIELDS = CONFIG_LOCALES.map(language => ({
    name: language.code,
    type: 'string',
    title: language.code, 
    id: language.code
}));


enum FURNITURE_TYPES_KEYS {
    't01p' = '01p',
    't02' = '02',
    't01v' = '01v',
    't03' = '03',
    't13' = '13',
    't13v' = '13v',
    't23' = '23',
    't24' = '24',
    't25' = '25'
  }

const pickProperties = <T, K extends keyof T>(obj: T, ...keys: K[]) => keys.reduce((acc, key) => {
    acc[key] = obj[key];
    return acc;
  }, <Pick<T, K>>{});
  
type FURNITURE_TYPE = { name: string, nameKey: string, badgeKey?: string, colorBadgeKey?: string, shelfType: number, categories: any, colors: any }

const bedsideTableNameVariants = ['bedsideTable', 'bedside_table', 'bedside table', 'bedsidetable'];


export const CATEGORIES = () => {
    const vinylStorage = {
      name: 'vinylstorage',
      navigationImagePath: 'common/menu/categories/vinylstorage',
      trackDataEventLabel: 'vinyl_storage',
      nameKey: 'common.category.vinylstorage',
      urlPathKey: 'common.category.vinylstorage_url_path',
      pdpUrlCategoryKey: 'common.category.pdp.vinylstorage_url_path',
      pluralNameKey: 'common.category.vinylstorage_plural',
      gridNameKey: 'common.category.vinylstorage_grid',
      categoryIndex: 'vinyl_storage',
      descriptionKey: 'common.category.vinylstorage_description',
      'data-testid': 'redirect-category-vinyl',
      shelf_type: 7,
      availableColorsOverride: {
        '01v': []
      }
    };
  
    const bedsideTable = {
      name: 'bedsidetable',
      navigationImagePath: 'common/menu/categories/bedsidetable',
      trackDataEventLabel: 'bedside_table',
      nameKey: 'common.category.bedside_table',
      urlPathKey: 'common.category.bedside_table_url_path',
      pdpUrlCategoryKey: 'common.category.pdp.bedside_table_url_path',
      pluralNameKey: 'common.category.bedside_table_plural',
      categoryIndex: 'bedside_table',
      descriptionKey: 'common.category.vinylstorage_description',
      'data-testid': 'redirect-category-bedside-table',
      shelf_type: 9,
      labelKey: 'common.new'
    };
  
    return {
      bookcase: {
        name: 'bookcase',
        navigationImagePath: 'common/menu/categories/bookcase',
        trackDataEventLabel: 'bookcase',
        nameKey: 'common.category.bookcase',
        pluralNameKey: 'common.category.bookcase_plural',
        urlPathKey: 'common.category.bookcase_url_path',
        pdpUrlCategoryKey: 'common.category.pdp.bookcase_url_path',
        categoryIndex: 'bookcase',
        descriptionKey: 'common.category.bookcase_description',
        hasAdditionalStorage: true,
        'data-testid': 'redirect-category-bookcase',
        shelf_type: 4
      },
      sideboard: {
        name: 'sideboard',
        navigationImagePath: 'common/menu/categories/sideboard',
        trackDataEventLabel: 'sideboard',
        nameKey: 'common.category.sideboard',
        pluralNameKey: 'common.category.sideboard_plural',
        urlPathKey: 'common.category.sideboard_url_path',
        pdpUrlCategoryKey: 'common.category.pdp.sideboard_url_path',
        categoryIndex: 'sideboard',
        descriptionKey: 'common.category.sideboard_description',
        hasAdditionalStorage: true,
        'data-testid': 'redirect-category-sideboard',
        shelf_type: 3,
        labelKey: 'common.new'
      },
      wardrobe: {
        name: 'wardrobe',
        navigationImagePath: 'common/menu/categories/wardrobe',
        trackDataEventLabel: 'wardrobe',
        nameKey: 'common.category.wardrobe',
        pluralNameKey: 'common.category.wardrobe_plural',
        urlPathKey: 'common.category.wardrobe_url_path',
        pdpUrlCategoryKey: 'common.category.pdp.wardrobe_url_path',
        categoryIndex: 'wardrobe',
        descriptionKey: 'common.category.wardrobe_description',
        'data-testid': 'redirect-category-wardrobe',
        shelf_type: 8,
      },
      desk: {
        name: 'desk',
        navigationImagePath: 'common/menu/categories/desk',
        trackDataEventLabel: 'desk',
        nameKey: 'common.category.desk',
        pluralNameKey: 'common.category.desk_plural',
        urlPathKey: 'common.category.desk_url_path',
        pdpUrlCategoryKey: 'common.category.pdp.desk_url_path',
        categoryIndex: 'desk',
        descriptionKey: 'common.category.desk_description',
        'data-testid': 'redirect-category-desk',
        shelf_type: 10,
        availableColorsOverride: {
          '02': [3, 0, 10, 11],
          '01v': [],
          '01p': [0, 3, 8, 7, 6, 1]
        }
      },
      tvstand: {
        name: 'tvstand',
        navigationImagePath: 'common/menu/categories/tvstand',
        trackDataEventLabel: 'tv_stand',
        nameKey: 'common.category.tvstand',
        urlPathKey: 'common.category.tvstand_url_path',
        pdpUrlCategoryKey: 'common.category.pdp.tvstand_url_path',
        pluralNameKey: 'common.category.tvstand_plural',
        categoryIndex: 'tvstand',
        descriptionKey: 'common.category.tvstand_description',
        'data-testid': 'redirect-category-tvstand',
        shelf_type: 2,
        labelKey: 'common.new'
      },
      chest: {
        name: 'chest',
        navigationImagePath: 'common/menu/categories/chest',
        trackDataEventLabel: 'chest',
        nameKey: 'common.category.chest',
        urlPathKey: 'common.category.chest_url_path',
        pdpUrlCategoryKey: 'common.category.pdp.chest_url_path',
        pluralNameKey: 'common.category.chest_plural',
        categoryIndex: 'chest',
        descriptionKey: 'common.category.chest_description',
        'data-testid': 'redirect-category-chest',
        shelf_type: 6,
        labelKey: 'common.new'
      },
      shoerack: {
        name: 'shoerack',
        navigationImagePath: 'common/menu/categories/shoerack',
        trackDataEventLabel: 'shoerack',
        nameKey: 'common.category.shoerack',
        urlPathKey: 'common.category.shoerack_url_path',
        pdpUrlCategoryKey: 'common.category.pdp.shoerack_url_path',
        pluralNameKey: 'common.category.shoerack_plural',
        categoryIndex: 'shoerack',
        descriptionKey: 'common.category.shoerack_description',
        'data-testid': 'redirect-category-shoerack',
        shelf_type: 1
      },
      wallstorage: {
        name: 'wallstorage',
        navigationImagePath: 'common/menu/categories/wallstorage',
        trackDataEventLabel: 'wallstorage',
        nameKey: 'common.category.wallstorage',
        urlPathKey: 'common.category.wallstorage_url_path',
        pdpUrlCategoryKey: 'common.category.pdp.wallstorage_url_path',
        pluralNameKey: 'common.category.wallstorage_plural',
        categoryIndex: 'wallstorage',
        descriptionKey: 'common.category.wallstorage_description',
        hasAdditionalStorage: true,
        'data-testid': 'redirect-category-wallstorage',
        shelf_type: 5
      },
      vinylstorage: vinylStorage,
      vinyl_storage: vinylStorage,
      'vinyl storage': vinylStorage,
      bedsideTable,
      bedside_table: bedsideTable,
      'bedside table': bedsideTable,
      bedsidetable: bedsideTable
    };
  };

  export const COLORS = () => ({
    '01p': {
      '01p-white': {
        name: '01p-white',
        nameKey: 'common.material.white_plywood',
        key: 'white',
        colorIndex: 0,
        cv: 0,
        gridQuery: '?material=0&shelfType=0',
        gridQueryNew: '?material=0',
        materialId: 0,
        shelfType: 0,
        model: ['jetty'],
        order: 1,
        iconPath: 't01p/white',
        materialName: 'white'
      },
      '01p-grey': {
        name: '01p-grey',
        nameKey: 'common.material.grey_plywood',
        key: 'grey',
        colorIndex: 2,
        cv: 3,
        gridQuery: '?material=3&shelfType=0',
        gridQueryNew: '?material=3',
        materialId: 3,
        shelfType: 0,
        model: ['jetty'],
        order: 2,
        iconPath: 't01p/grey',
        materialName: 'grey'
      },
      '01p-dusty-pink': {
        name: '01p-dusty-pink',
        nameKey: 'common.material.dusty_pink_plywood',
        key: 'dustyPink',
        colorIndex: 5,
        cv: 8,
        gridQuery: '?material=8&shelfType=0',
        gridQueryNew: '?material=8',
        materialId: 8,
        shelfType: 0,
        model: ['jetty'],
        order: 4,
        iconPath: 't01p/dusty-pink',
        materialName: 'dusty_pink'
      },
      '01p-yellow': {
        name: '01p-yellow',
        nameKey: 'common.material.yellow_plywood',
        key: 'yellow',
        colorIndex: 4,
        cv: 7,
        gridQuery: '?material=7&shelfType=0',
        gridQueryNew: '?material=7',
        materialId: 7,
        shelfType: 0,
        model: ['jetty'],
        order: 5,
        iconPath: 't01p/yellow',
        materialName: 'yellow'
      },
      '01p-black': {
        name: '01p-black',
        nameKey: 'common.material.black_plywood',
        key: 'black',
        colorIndex: 1,
        cv: 1,
        gridQuery: '?material=1&shelfType=0',
        gridQueryNew: '?material=1',
        materialId: 1,
        shelfType: 0,
        model: ['jetty'],
        order: 3,
        iconPath: 't01p/black',
        materialName: 'black'
      },
      '01p-blue': {
        name: '01p-blue',
        nameKey: 'common.material.blue_plywood',
        key: 'blue',
        colorIndex: 3,
        cv: 9,
        gridQuery: '?material=9&shelfType=0',
        gridQueryNew: '?material=9',
        materialId: 9,
        shelfType: 0,
        model: ['jetty'],
        order: 8,
        iconPath: 't01p/blue',
        materialName: 'blue'
      },
      '01p-brown': {
        name: '01p-brown',
        nameKey: 'common.material.dark_brown_plywood',
        colorIndex: 7,
        key: 'darkBrown',
        cv: 10,
        gridQuery: '?material=1&shelfType=0',
        gridQueryNew: '?material=1',
        materialId: 10,
        shelfType: 0,
        model: ['jetty'],
        order: 7,
        iconPath: 't01p/brown',
        materialName: 'dark_brown'
      }
    },
    '01v': {
      '01v-walnut': {
        name: '01v-walnut',
        nameKey: 'common.material.walnut_veneer',
        key: 'walnutVeneer',
        colorIndex: 2,
        cv: 2,
        gridQuery: '?material=2&shelfType=2',
        gridQueryNew: '?material=2',
        materialId: 2,
        shelfType: 2,
        model: ['jetty'],
        order: 1,
        iconPath: 't01v/walnut',
        materialName: 'walnut_veneer'
      },
      '01v-oak': {
        name: '01v-oak',
        nameKey: 'common.material.oak_veneer',
        key: 'oakVeneer',
        colorIndex: 1,
        cv: 1,
        gridQuery: '?material=1&shelfType=2',
        gridQueryNew: '?material=1',
        materialId: 1,
        shelfType: 2,
        model: ['jetty'],
        order: 2,
        iconPath: 't01v/oak',
        materialName: 'oak_veneer'
      },
      '01v-ash': {
        name: '01v-ash',
        nameKey: 'common.material.ash_veneer',
        key: 'ashVeneer',
        colorIndex: 0,
        cv: 0,
        gridQuery: '?material=0&shelfType=2',
        gridQueryNew: '?material=0',
        materialId: 0,
        shelfType: 2,
        model: ['jetty'],
        order: 3,
        iconPath: 't01v/ash',
        materialName: 'ash_veneer'
      }
    },
    '02': {
      '02-white': {
        name: '02-white',
        nameKey: 'common.material.white',
        key: 'white',
        colorIndex: 0,
        cv: 0,
        gridQuery: '?material=0&shelfType=1',
        gridQueryNew: '?material=0',
        materialId: 0,
        shelfType: 1,
        model: ['jetty'],
        order: 1,
        iconPath: 't02/white',
        materialName: 'basic_white'
      },
      '02-cotton': {
        name: '02-cotton',
        nameKey: 'common.material.cotton_beige',
        key: 'cottonBeige',
        colorIndex: 8,
        cv: 9,
        gridQuery: '?material=9&shelfType=1',
        gridQueryNew: '?material=9',
        materialId: 8,
        shelfType: 1,
        model: ['jetty'],
        order: 2,
        iconPath: 't02/cotton',
        materialName: 'cotton'
      },
      '02-sand-mustardyellow': {
        name: '02-sand-mustardyellow',
        nameKey: 'common.material.sand_mustardyellow',
        key: ['sand', 'mustardYellow'],
        colorIndex: 11,
        cv: 12,
        gridQuery: '?material=12&shelfType=1',
        gridQueryNew: '?material=12',
        materialId: 12,
        shelfType: 1,
        model: ['jetty'],
        order: 5,
        iconPath: 't02/sand-mustardyellow',
        materialName: 't02_sand_mustard'
      },
      '02-sand': {
        name: '02-sand',
        nameKey: 'common.material.beige',
        key: ['sand', 'midnightBlue'],
        colorIndex: 3,
        cv: 3,
        gridQuery: '?material=3&shelfType=1',
        gridQueryNew: '?material=3',
        materialId: 3,
        shelfType: 1,
        model: ['jetty'],
        order: 6,
        iconPath: 't02/beige',
        materialName: 'beige'
      },
      '02-grey': {
        name: '02-grey',
        nameKey: 'common.material.greyt02',
        key: 'grey',
        colorIndex: 9,
        cv: 10,
        gridQuery: '?material=10&shelfType=1',
        gridQueryNew: '?material=8',
        materialId: 10,
        shelfType: 1,
        model: ['jetty'],
        order: 3,
        iconPath: 't02/grey',
        materialName: 't02_grey'
      },
      '02-sky-blue': {
        name: '02-sky-blue',
        nameKey: 'common.material.sky_blue',
        key: 'skyBlue',
        colorIndex: 6,
        cv: 7,
        gridQuery: '?material=7&shelfType=1',
        gridQueryNew: '?material=7',
        materialId: 8,
        shelfType: 1,
        model: ['jetty'],
        order: 12,
        iconPath: 't02/sky-blue',
        materialName: 'sky_blue'
      },
      '02-terracotta': {
        name: '02-terracotta',
        nameKey: 'common.material.terracotta',
        key: 'terracotta',
        colorIndex: 1,
        cv: 1,
        gridQuery: '?material=1&shelfType=1',
        gridQueryNew: '?material=1',
        materialId: 1,
        shelfType: 1,
        model: ['jetty'],
        order: 9,
        iconPath: 't02/terracotta',
        materialName: 'orange'
      },
      '02-midnight-blue': {
        name: '02-midnight-blue',
        nameKey: 'common.material.indygo',
        key: 'midnightBlue',
        colorIndex: 2,
        cv: 2,
        gridQuery: '?material=2&shelfType=1',
        gridQueryNew: '?material=2',
        materialId: 1,
        shelfType: 1,
        model: ['jetty'],
        order: 13,
        iconPath: 't02/indygo',
        materialName: 'indygo'
      },
      '02-burgundy': {
        name: '02-burgundy',
        nameKey: 'common.material.burgundy_red',
        key: 'burgundyRed',
        colorIndex: 7,
        cv: 8,
        gridQuery: '?material=8&shelfType=1',
        gridQueryNew: '?material=8',
        materialId: 8,
        shelfType: 1,
        model: ['jetty'],
        order: 10,
        iconPath: 't02/burgundy',
        materialName: 'burgundy'
      },
      '02-matte-black': {
        name: '02-matte-black',
        nameKey: 'common.material.matte_black',
        key: 'matteBlack',
        colorIndex: 5,
        cv: 6,
        gridQuery: '?material=6&shelfType=1',
        gridQueryNew: '?material=6',
        materialId: 6,
        shelfType: 1,
        model: ['jetty'],
        order: 15,
        iconPath: 't02/matte-black',
        materialName: 'matte_black'
      },
      '02-black': {
        name: '02-black',
        nameKey: 'common.material.black',
        key: 'black',
        colorIndex: 19,
        cv: 19,
        materialId: 19,
        order: 16,
        shelfType: 1,
        iconPath: 't02/black',
        materialName: 'black'
      },
      '02-reisingers-pink': {
        name: '02-reisingers-pink',
        nameKey: 'common.material.reisingers_pink',
        key: 'reisingersPink',
        colorIndex: 12,
        cv: 15,
        gridQuery: '?material=15&shelfType=1',
        gridQueryNew: '?material=15',
        materialId: 15,
        shelfType: 1,
        model: ['jetty'],
        order: 14,
        iconPath: 't02/reisingers-pink',
        materialName: 'reisingers_pink'
      },
      '02-sage-green': {
        name: '02-sage-green',
        nameKey: 'common.material.sage_green',
        key: ['stoneGrey', 'sageGreen'],
        colorIndex: 13,
        cv: 16,
        gridQuery: '?material=16&shelfType=1',
        gridQueryNew: '?material=16',
        materialId: 16,
        shelfType: 1,
        model: ['jetty'],
        order: 11,
        iconPath: 't02/sage-green',
        materialName: 'sage_green'
      },
      '02-stone-grey': {
        name: '02-stone-grey',
        nameKey: 'common.material.stone_grey',
        key: 'stoneGrey',
        colorIndex: 14,
        cv: 17,
        gridQuery: '?material=17&shelfType=1',
        gridQueryNew: '?material=17',
        materialId: 17,
        shelfType: 1,
        model: ['jetty'],
        order: 4,
        iconPath: 't02/stone-grey',
        materialName: 'stone_grey'
      },
      '02-stone-grey-walnut': {
        name: '02-stone-grey-walnut',
        nameKey: 'common.material.stone_grey_walnut',
        key: ['stoneGrey', 'walnutVeneer'],
        colorIndex: 5,
        cv: 18,
        gridQuery: '?material=6&shelfType=1',
        gridQueryNew: '?material=6',
        materialId: 18,
        shelfType: 1,
        model: ['jetty'],
        order: 8,
        iconPath: 't02/stone-grey-walnut',
        materialName: 'stone_grey_walnut'
      }
    },
    '03': {
      '03-white': {
        name: '03-white',
        nameKey: 'common.material.white',
        key: 'white',
        colorIndex: 0,
        cv: 0,
        materialId: 0,
        shelfType: 3,
        order: 1,
        iconPath: 't03_2023-05-24/white',
        materialName: 'white'
      },
      '03-white+antique-pink': {
        name: '03-white+antique-pink',
        nameKey: ['common.material.white', 'common.material.antique_pink'],
        key: ['white', 'antiquePink'],
        colorIndex: 0,
        cv: 4,
        materialId: 4,
        shelfType: 3,
        order: 2,
        iconPath: 't03_2023-05-24/white+antique-pink',
        materialName: 'white-antique-pink',
        exteriorParentId: '03-white',
        interiorIconPath: 't03_2023-05-24/interior/antique-pink'
      },
      '03-white+stone-grey': {
        name: '03-white+stone-grey',
        nameKey: ['common.material.white', 'common.material.stone_grey'],
        key: ['white', 'stoneGrey'],
        colorIndex: 0,
        cv: 15,
        materialId: 15,
        shelfType: 3,
        order: 3,
        iconPath: 't03_2023-05-24/white+stone-grey',
        materialName: 'white-stone-grey',
        exteriorParentId: '03-white',
        interiorIconPath: 't03_2023-05-24/interior/stone-grey'
      },
      '03-white+sage-green': {
        name: '03-white+sage-green',
        nameKey: ['common.material.white', 'common.material.sage_green'],
        key: ['white', 'sageGreen'],
        colorIndex: 0,
        cv: 16,
        materialId: 16,
        shelfType: 3,
        order: 4,
        iconPath: 't03_2023-05-24/white+sage-green',
        materialName: 'white-sage-green',
        exteriorParentId: '03-white',
        interiorIconPath: 't03_2023-05-24/interior/sage-green'
      },
      '03-white+misty-blue': {
        name: '03-white+misty-blue',
        nameKey: ['common.material.white', 'common.material.misty_blue'],
        key: ['white', 'mistyBlue'],
        colorIndex: 0,
        cv: 17,
        materialId: 17,
        shelfType: 3,
        order: 5,
        iconPath: 't03_2023-05-24/white+misty-blue',
        materialName: 'white-misty-blue',
        exteriorParentId: '03-white',
        interiorIconPath: 't03_2023-05-24/interior/misty-blue'
      },
  
      '03-cashmere-beige': {
        name: '03-cashmere-beige',
        nameKey: 'common.material.cashmere_beige',
        key: 'cashmereBeige',
        colorIndex: 1,
        cv: 1,
        materialId: 1,
        shelfType: 3,
        order: 6,
        iconPath: 't03_2023-05-24/cashmere-beige',
        materialName: 'cashmere'
      },
      '03-cashmere-beige+antique-pink': {
        name: '03-cashmere-beige+antique-pink',
        nameKey: ['common.material.cashmere_beige', 'common.material.antique_pink'],
        key: ['cashmereBeige', 'antiquePink'],
        colorIndex: 3,
        cv: 3,
        materialId: 3,
        shelfType: 3,
        order: 7,
        iconPath: 't03_2023-05-24/cashmere-beige+antique-pink',
        materialName: 'cashmere-antique-pink',
        exteriorParentId: '03-cashmere-beige',
        interiorIconPath: 't03_2023-05-24/interior/antique-pink'
      },
      '03-cashmere-beige+stone-grey': {
        name: '03-cashmere-beige+stone-grey',
        nameKey: ['common.material.cashmere_beige', 'common.material.stone_grey'],
        key: ['cashmereBeige', 'stoneGrey'],
        colorIndex: 5,
        cv: 18,
        materialId: 18,
        shelfType: 3,
        order: 8,
        iconPath: 't03_2023-05-24/cashmere-beige+stone-grey',
        materialName: 'cashmere-stone-grey',
        exteriorParentId: '03-cashmere-beige',
        interiorIconPath: 't03_2023-05-24/interior/stone-grey'
      },
      '03-cashmere-beige+sage-green': {
        name: '03-cashmere-beige+sage-green',
        nameKey: ['common.material.cashmere_beige', 'common.material.sage_green'],
        key: ['cashmereBeige', 'sageGreen'],
        colorIndex: 2,
        cv: 19,
        materialId: 19,
        shelfType: 3,
        order: 9,
        iconPath: 't03_2023-05-24/cashmere-beige+sage-green',
        materialName: 'cashmere-sage-green',
        exteriorParentId: '03-cashmere-beige',
        interiorIconPath: 't03_2023-05-24/interior/sage-green'
      },
      '03-cashmere-beige+misty-blue': {
        name: '03-cashmere-beige+misty-blue',
        nameKey: ['common.material.cashmere_beige', 'common.material.misty_blue'],
        key: ['cashmereBeige', 'mistyBlue'],
        colorIndex: 2,
        cv: 20,
        materialId: 20,
        shelfType: 3,
        order: 10,
        iconPath: 't03_2023-05-24/cashmere-beige+misty-blue',
        materialName: 'cashmere-misty-blue',
        exteriorParentId: '03-cashmere-beige',
        interiorIconPath: 't03_2023-05-24/interior/misty-blue'
      },
  
      '03-graphite-grey': {
        name: '03-graphite-grey',
        nameKey: 'common.material.graphite_grey',
        key: 'graphiteGrey',
        colorIndex: 2,
        cv: 2,
        materialId: 2,
        shelfType: 3,
        order: 11,
        iconPath: 't03_2023-05-24/graphite-grey',
        materialName: 'graphite-grey'
      },
      '03-graphite-grey+antique-pink': {
        name: '03-graphite-grey+antique-pink',
        nameKey: ['common.material.graphite_grey', 'common.material.antique_pink'],
        key: ['graphiteGrey', 'antiquePink'],
        colorIndex: 2,
        cv: 6,
        materialId: 6,
        shelfType: 3,
        order: 12,
        iconPath: 't03_2023-05-24/graphite-grey+antique-pink',
        materialName: 'graphite-grey-antique-pink',
        exteriorParentId: '03-graphite-grey',
        interiorIconPath: 't03_2023-05-24/interior/antique-pink'
      },
      '03-graphite-grey+stone-grey': {
        name: '03-graphite-grey+stone-grey',
        nameKey: ['common.material.graphite_grey', 'common.material.stone_grey'],
        key: ['graphiteGrey', 'stoneGrey'],
        colorIndex: 2,
        cv: 12,
        materialId: 12,
        shelfType: 3,
        order: 13,
        iconPath: 't03_2023-05-24/graphite-grey+stone-grey',
        materialName: 'graphite-grey-stone-grey',
        exteriorParentId: '03-graphite-grey',
        interiorIconPath: 't03_2023-05-24/interior/stone-grey'
      },
      '03-graphite-grey+sage-green': {
        name: '03-graphite-grey+sage-green',
        nameKey: ['common.material.graphite_grey', 'common.material.sage_green'],
        key: ['graphiteGrey', 'sageGreen'],
        colorIndex: 2,
        cv: 13,
        materialId: 13,
        shelfType: 3,
        order: 14,
        iconPath: 't03_2023-05-24/graphite-grey+sage-green',
        materialName: 'graphite-grey-sage-green',
        exteriorParentId: '03-graphite-grey',
        interiorIconPath: 't03_2023-05-24/interior/sage-green'
      },
      '03-graphite-grey+misty-blue': {
        name: '03-graphite-grey+misty-blue',
        nameKey: ['common.material.graphite_grey', 'common.material.misty_blue'],
        key: ['graphiteGrey', 'mistyBlue'],
        colorIndex: 2,
        cv: 14,
        materialId: 14,
        shelfType: 3,
        order: 15,
        iconPath: 't03_2023-05-24/graphite-grey+misty-blue',
        materialName: 'graphite-grey-misty-blue',
        exteriorParentId: '03-graphite-grey',
        interiorIconPath: 't03_2023-05-24/interior/misty-blue'
      }
    },
    13: {
      '13-white+plywood': {
        name: '13-white+plywood',
        nameKey: 'common.material.white_plywood',
        key: 'white',
        colorIndex: 5,
        cv: 5,
        gridQuery: '?material=2&shelfType=3',
        gridQueryNew: '?material=2',
        materialId: 5,
        shelfType: 4,
        model: ['watty'],
        order: 7,
        iconPath: 't13/white-plywood',
        materialName: 'white-plywood',
        mixed: true
      },
      '13-grey+plywood': {
        name: '13-grey+plywood',
        nameKey: 'common.material.grey_plywood',
        key: 'grey',
        colorIndex: 6,
        cv: 6,
        gridQuery: '?material=2&shelfType=3',
        gridQueryNew: '?material=2',
        materialId: 6,
        shelfType: 4,
        model: ['watty'],
        order: 8,
        iconPath: 't13/grey-plywood',
        materialName: 'grey-plywood',
        mixed: true
      },
      '13-black+plywood': {
        name: '13-black+plywood',
        nameKey: 'common.material.black_plywood',
        key: 'black',
        colorIndex: 7,
        cv: 7,
        gridQuery: '?material=2&shelfType=3',
        gridQueryNew: '?material=2',
        materialId: 7,
        shelfType: 4,
        model: ['watty'],
        order: 9,
        iconPath: 't13/black-plywood',
        materialName: 'black-plywood',
        mixed: true
      },
      '13-sand+mustard-yellow': {
        name: '13-sand+mustard-yellow',
        nameKey: ['common.material.sand', 'common.material.mustard_yellow'],
        key: ['sand', 'mustardYellow'],
        colorIndex: 2,
        cv: 2,
        gridQuery: '?material=3&shelfType=3',
        gridQueryNew: '?material=3',
        materialId: 2,
        shelfType: 3,
        model: ['watty'],
        order: 10,
        iconPath: 't13/sand-mustard-yellow',
        materialName: 'sand-mustard-yellow',
        mixed: true
      },
      '13-sand+midnight-blue': {
        name: '13-sand+midnight-blue',
        nameKey: ['common.material.sand', 'common.material.midnight_blue'],
        key: ['sand', 'midnightBlue'],
        colorIndex: 1,
        cv: 1,
        gridQuery: '?material=6&shelfType=3',
        gridQueryNew: '?material=6',
        materialId: 1,
        shelfType: 4,
        model: ['watty'],
        order: 11,
        iconPath: 't13/sand-midnight-blue',
        materialName: 'sand-midnight-blue',
        mixed: true
      },
      '13-grey+dark-grey': {
        name: '13-grey+dark-grey',
        nameKey: ['common.material.grey', 'common.material.dark_grey'],
        key: ['grey', 'darkGrey'],
        colorIndex: 4,
        cv: 4,
        gridQuery: '?material=1&shelfType=3',
        gridQueryNew: '?material=1',
        materialId: 4,
        shelfType: 4,
        model: ['watty'],
        order: 12,
        iconPath: 't13/grey-darkgrey',
        materialName: 'grey-darkgrey',
        mixed: true
      },
      '13-white': {
        name: '13-white',
        nameKey: 'common.material.white',
        key: 'white',
        colorIndex: 0,
        cv: 0,
        gridQuery: '?material=0&shelfType=3',
        gridQueryNew: '?material=0',
        materialId: 0,
        shelfType: 4,
        model: ['watty'],
        order: 1,
        iconPath: 't13/white',
        materialName: 'white',
        mixed: false
      },
      '13-grey': {
        name: '13-grey',
        nameKey: 'common.material.grey',
        key: 'grey',
        colorIndex: 3,
        cv: 3,
        gridQuery: '?material=4&shelfType=3',
        gridQueryNew: '?material=4',
        materialId: 3,
        shelfType: 4,
        model: ['watty'],
        order: 2,
        iconPath: 't13/grey',
        materialName: 'grey',
        mixed: false
      },
      '13-black': {
        name: '13-black',
        nameKey: 'common.material.black',
        key: 'black',
        colorIndex: 11,
        cv: 11,
        gridQuery: '?material=XX&shelfType=XX',
        gridQueryNew: '?material=XX',
        materialId: 11,
        shelfType: 4,
        model: ['watty'],
        order: 3,
        iconPath: 't13/black',
        materialName: 'black',
        mixed: false
      },
      '13-clay-brown': {
        name: '13-clay-brown',
        nameKey: 'common.material.clay_brown',
        key: 'clayBrown',
        colorIndex: 8,
        cv: 8,
        gridQuery: '?material=XX&shelfType=XX',
        gridQueryNew: '?material=XX',
        materialId: 8,
        shelfType: 4,
        model: ['watty'],
        order: 4,
        iconPath: 't13/clay-brown',
        materialName: 'clay-brown',
        mixed: false
      },
      '13-olive-green': {
        name: '13-olive-green',
        nameKey: 'common.material.olive_green',
        key: 'oliveGreen',
        colorIndex: 9,
        cv: 9,
        gridQuery: '?material=XX&shelfType=XX',
        gridQueryNew: '?material=XX',
        materialId: 9,
        shelfType: 4,
        model: ['watty'],
        order: 5,
        iconPath: 't13/olive-green',
        materialName: 'olive-green',
        mixed: false
      },
      '13-sand': {
        name: '13-sand',
        nameKey: 'common.material.sand',
        key: 'sand',
        colorIndex: 10,
        cv: 10,
        gridQuery: '?material=XX&shelfType=XX',
        gridQueryNew: '?material=XX',
        materialId: 10,
        shelfType: 4,
        model: ['watty'],
        order: 6,
        iconPath: 't13/sand',
        materialName: 'sand',
        mixed: false
      }
    },
    '13v': {
      '13-cleaf-light': {
        name: '13-cleaf-light',
        nameKey: 'common.material.cleaf_light',
        key: 'cleafLight',
        colorIndex: 0,
        cv: 0,
        materialId: 0,
        order: 0,
        iconPath: 't13/cleaf-light',
        materialName: 'cleaf-light',
        mixed: false,
        shelfType: 5
      },
      '13-cleaf-dark': {
        name: '13-cleaf-dark',
        nameKey: 'common.material.cleaf_dark',
        key: 'cleafDark',
        colorIndex: 1,
        cv: 1,
        materialId: 1,
        order: 1,
        iconPath: 't13/cleaf-dark',
        materialName: 'cleaf-dark',
        mixed: false,
        shelfType: 5
      }
    },
    '23': {
      '23-offwhite': {
        name: '23-offwhite',
        nameKey: 'common.material.off_white',
        key: 'offWhite',
        colorIndex: 0,
        cv: 0,
        materialId: 0,
        order: 0,
        shelfType: 6,
        iconPath: 't23/off-white',
        materialName: 'off-white'
      },
      '23-oyster-beige': {
        name: '23-oyster-beige',
        nameKey: 'common.material.oyster_beige',
        key: 'oysterBeige',
        colorIndex: 1,
        cv: 1,
        materialId: 1,
        order: 1,
        shelfType: 6,
        iconPath: 't23/oyster-beige',
        materialName: 'oyster-beige'
      },
      '23-pistachio-green': {
        name: '23-pistachio-green',
        nameKey: 'common.material.pistachio_green',
        key: 'pistachioGreen',
        colorIndex: 2,
        cv: 2,
        materialId: 2,
        order: 2,
        shelfType: 6,
        iconPath: 't23/pistachio-green',
        materialName: 'pistachio-green'
      },
      '23-inky-black': {
        name: '23-inky-black',
        nameKey: 'common.material.inky_black',
        key: 'inkyBlack',
        colorIndex: 3,
        cv: 3,
        materialId: 3,
        order: 3,
        shelfType: 6,
        iconPath: 't23/inky-black',
        materialName: 'inky-black'
      },
      '23-powder-pink': {
        name: '23-powder-pink',
        nameKey: 'common.material.powder_pink',
        key: 'powderPink',
        colorIndex: 4,
        cv: 4,
        materialId: 4,
        order: 4,
        shelfType: 6,
        iconPath: 't23/powder-pink',
        materialName: 'powder-pink'
      }
    },
    24: {
      '24-offwhite': {
        name: '24-offwhite',
        nameKey: 'common.material.off_white',
        key: 'offWhite',
        colorIndex: 0,
        cv: 0,
        materialId: 0,
        order: 0,
        shelfType: 7,
        iconPath: 't24/off-white',
        materialName: 'off-white'
      },
      '24-oyster-beige': {
        name: '24-oyster-beige',
        nameKey: 'common.material.oyster_beige',
        key: 'oysterBeige',
        colorIndex: 1,
        cv: 1,
        materialId: 1,
        order: 1,
        shelfType: 7,
        iconPath: 't24/oyster-beige',
        materialName: 'oyster-beige'
      },
      '24-pistachio-green': {
        name: '24-pistachio-green',
        nameKey: 'common.material.pistachio_green',
        key: 'pistachioGreen',
        colorIndex: 2,
        cv: 2,
        materialId: 2,
        order: 2,
        shelfType: 7,
        iconPath: 't24/pistachio-green',
        materialName: 'pistachio-green'
      },
      '24-inky-black': {
        name: '24-inky-black',
        nameKey: 'common.material.inky_black',
        key: 'inkyBlack',
        colorIndex: 3,
        cv: 3,
        materialId: 3,
        order: 3,
        shelfType: 7,
        iconPath: 't24/inky-black',
        materialName: 'inky-black'
      },
      '24-powder-pink': {
        name: '24-powder-pink',
        nameKey: 'common.material.powder_pink',
        key: 'powderPink',
        colorIndex: 4,
        cv: 4,
        materialId: 4,
        order: 4,
        shelfType: 7,
        iconPath: 't24/powder-pink',
        materialName: 'powder-pink'
      }
    },
    25: {
      '25-offwhite': {
        name: '25-offwhite',
        nameKey: 'common.material.off_white',
        key: 'offWhite',
        colorIndex: 0,
        cv: 0,
        materialId: 0,
        order: 0,
        shelfType: 8,
        iconPath: 't25/off-white',
        materialName: 'off-white'
      },
      '25-oyster-beige': {
        name: '25-oyster-beige',
        nameKey: 'common.material.oyster_beige',
        key: 'oysterBeige',
        colorIndex: 1,
        cv: 1,
        materialId: 1,
        order: 1,
        shelfType: 8,
        iconPath: 't25/oyster-beige',
        materialName: 'oyster-beige'
      },
      '25-pistachio-green': {
        name: '25-pistachio-green',
        nameKey: 'common.material.pistachio_green',
        key: 'pistachioGreen',
        colorIndex: 2,
        cv: 2,
        materialId: 2,
        order: 2,
        shelfType: 8,
        iconPath: 't25/pistachio-green',
        materialName: 'pistachio-green'
      },
      '25-inky-black': {
        name: '25-inky-black',
        nameKey: 'common.material.inky_black',
        key: 'inkyBlack',
        colorIndex: 3,
        cv: 3,
        materialId: 3,
        order: 3,
        shelfType: 8,
        iconPath: 't25/inky-black',
        materialName: 'inky-black'
      },
      '25-powder-pink': {
        name: '25-powder-pink',
        nameKey: 'common.material.powder_pink',
        key: 'powderPink',
        colorIndex: 4,
        cv: 4,
        materialId: 4,
        order: 4,
        shelfType: 8,
        iconPath: 't25/powder-pink',
        materialName: 'powder-pink'
      }
    }
  });

export const FURNITURE_TYPES = (): { [key in FURNITURE_TYPES_KEYS]: FURNITURE_TYPE } => ({
    '01p': {
      name: '01p',
      nameKey: 'common.type_01_plywood',
      shelfType: 0,
      categories: pickProperties(CATEGORIES(), 'bookcase', 'sideboard', 'tvstand', 'shoerack', 'wallstorage', 'chest', 'vinyl storage', 'bedsideTable'),
      colors: COLORS()['01p']
    },
    '01v': {
      name: '01v',
      nameKey: 'common.type_01_veneer',
      shelfType: 2,
      categories: pickProperties(CATEGORIES(), 'bookcase', 'sideboard', 'tvstand', 'shoerack', 'wallstorage', 'chest', 'bedsideTable'),
      colors: COLORS()['01v']
    },
    '02': {
      name: '02',
      nameKey: 'common.type02',
      shelfType: 1,
      categories: pickProperties(CATEGORIES(), 'bookcase', 'sideboard', 'tvstand', 'shoerack', 'wallstorage', 'chest', 'bedsideTable'),
      colors: COLORS()['02'],
      badgeKey: 'common.new_colours',
      colorBadgeKey: 'common.new_colours'
    },
    '03': {
      name: '03',
      nameKey: 'common.type03',
      shelfType: 3,
      categories: pickProperties(CATEGORIES(), 'wardrobe'),
      colors: COLORS()['03'],
      badgeKey: 'common.new'
    },
    13: {
      name: '13',
      nameKey: 'common.type13',
      shelfType: 4,
      categories: pickProperties(CATEGORIES(), 'wardrobe', 'bookcase', 'wallstorage', 'chest'),
      colors: COLORS()['13']
    },
    '13v': {
      name: '13v',
      nameKey: 'common.type13v',
      shelfType: 5,
      categories: pickProperties(CATEGORIES(), 'wardrobe', 'chest', 'wallstorage', 'bookcase'),
      colors: COLORS()['13v']
    },
    23: {
      name: '23',
      nameKey: 'common.type23',
      shelfType: 6,
      categories: pickProperties(CATEGORIES(), 'sideboard', 'tvstand', 'chest', ...bedsideTableNameVariants),
      colors: COLORS()['23']
    },
    24: {
      name: '24',
      nameKey: 'common.type24',
      shelfType: 7,
      categories: pickProperties(CATEGORIES(), 'sideboard', 'tvstand', 'chest', ...bedsideTableNameVariants),
      colors: COLORS()['24']
    },
    25: {
      name: '25',
      nameKey: 'common.type25',
      shelfType: 8,
      categories: pickProperties(CATEGORIES(), 'sideboard', 'tvstand', 'chest', ...bedsideTableNameVariants),
      colors: COLORS()['25']
    }
  });